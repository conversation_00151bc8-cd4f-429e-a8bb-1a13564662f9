# /etc/nginx/conf.d/default.conf
server {
    listen       80;
    listen       [::]:80;
    server_name  nat.hxddz.top localhost;

    # 基础配置
    root   /usr/share/nginx/html;
    index  index.html;
    
    # 强制声明 MIME 类型（关键修复）
    include /etc/nginx/mime.types;
    types {
        application/javascript mjs js;
        application/wasm wasm;
        application/pdf pdf;
    }
    default_type  application/octet-stream;

    # 安全响应头
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";

    # 核心修复1：.mjs 文件的 MIME 类型强制指定（最高优先级）
    location ~ \.mjs$ {
        add_header Content-Type application/javascript;
        default_type application/javascript;
        try_files $uri =404;
        
        # 开发阶段禁用缓存
        add_header Cache-Control "no-store";
        expires 0;
    }

    # 核心修复2：${MGT_PROXY_PATH}/ 路径映射（两种方案任选其一）
    location ^~ ${MGT_PROXY_PATH}/ {
        ## 方案A：rewrite 方式（推荐）##
        rewrite ^${MGT_PROXY_PATH}/(.*)$ /$1 break;
        try_files $uri $uri/ /index.html;
        
        ## 方案B：alias 方式（备选）##
        # alias /usr/share/nginx/html/;
        # try_files $uri $uri/ /index.html;
    }

    # 静态资源长期缓存（生产环境启用）
    location ~* \.(js|mjs|css|png|jpg|jpeg|gif|ico|pdf|wasm)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Vue Router history 模式支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 错误页面配置
    error_page  404 /404.html;
    error_page  500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }
}