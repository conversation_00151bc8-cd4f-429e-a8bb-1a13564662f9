ARG Env="development"
ARG REPO="docker.io"
# 构建阶段
FROM ${REPO}/node:18-alpine AS build-stage
# 重新声明 ARG 并赋值给 ENV
ARG Env
ENV Env=$Env
# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./
RUN npm config set registry https://registry.npmmirror.com
# 安装依赖
RUN npm install

# 复制源代码
COPY . .

# 构建应用
RUN npm run build:$Env

# 生产阶段
FROM ${REPO}/nginx:stable-alpine AS production-stage

# 复制构建产物到 Nginx 目录
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制自定义 Nginx 配置（如果需要）
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 单独复制 entrypoint.sh 到根目录
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# 暴露 80 端口
EXPOSE 80


ARG WEB_BASE_URL=http://itmctr-web/
ENV WEB_BASE_URL=${WEB_BASE_URL}


ARG MGT_PROXY_PATH=/mgt
ENV MGT_PROXY_PATH=${MGT_PROXY_PATH}


ARG HOST=http://localhost:8080/
ENV HOST=${HOST}

# 启动时执行 entrypoint.sh，最后启动nginx
ENTRYPOINT ["/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"] 