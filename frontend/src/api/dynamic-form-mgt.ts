import request from '@/utils/request'
import {
    FormDto,
    FormOperationDto,
    FormFieldDto,
    FormFieldOperationDto,
    FormFieldGroupDto,
    FormFieldGroupOperationDto,
    FormQueryParams,
    FormFieldQueryParams,
    FormFieldGroupQueryParams, AvailableRegistrationNumbersResponse
} from '@/dtos/dynamic-form-mgt.dto'
import {PageResult} from '@/dtos/common'
import {FormInstanceDto} from '@/dtos/dynamic-form.dto'
import {FormInstancePageQueryParams} from '@/dtos/dynamic-form-mgt.dto'
import {getApiBaseUrl} from '@/config/env';

const baseURL = getApiBaseUrl('dynamic-form-mgt');


/**
 * 修改当前实例版本的表单值 主要用于流程审批过程中修改表单值，比如：待判断页面中 要都是否为传统医学进行选择和修改 最终审批完成时会涉及发号  也是会更新字段值
 * @param businessId 业务ID
 * @param version 版本号
 * @param dataValue 值对象
 * @returns 修改后的新版本
 */
export function saveFormInstance(businessId: string, version: string, dataValue: Record<string, any>) {
    return request<string>({
        url: `${baseURL}/FormInstance/instance/${businessId}/${version}`,
        method: 'put',
        data: dataValue
    })
}

/**
 * 确认当前版本表单实例
 * @param businessId 业务ID
 * @param version 版本号
 * @returns 确认结果
 */
export function confirmFormInstance(businessId: string, version: string) {
    return request<boolean>({
        url: `${baseURL}/FormInstance/instance/confirm/${businessId}/${version}`,
        method: 'post'
    })
}

/**
 * 驳回当前版本表单实例
 * @param businessId 业务ID
 * @param version 版本号
 * @returns 驳回结果
 */
export function rejectFormInstance(businessId: string, version: string, annotations: Record<string, string>) {
    return request<boolean>({
        url: `${baseURL}/FormInstance/instance/reject/${businessId}/${version}`,
        method: 'post',
        data: annotations
    })
}


/**
 * 获取表单列表
 * @param params 查询参数
 */
export function getFormList(params?: Partial<FormQueryParams>) {
    return request<FormDto[]>({
        url: `${baseURL}/Form`,
        method: 'get',
        params
    })
}

/**
 * 获取表单分页列表
 * @param params 查询参数
 */
export function getFormPage(params: FormQueryParams) {
    return request<PageResult<FormDto>>({
        url: `${baseURL}/Form/page`,
        method: 'get',
        params
    })
}

/**
 * 获取表单详情
 * @param id 表单ID
 */
export function getForm(id: string) {
    return request<FormDto>({
        url: `${baseURL}/Form/${id}`,
        method: 'get'
    })
}

/**
 * 创建表单
 * @param data 表单数据
 */
export function createForm(data: FormOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Form`,
        method: 'post',
        data
    })
}

export function saveFormDefinition(formId: string, data: any) {
    return request<boolean>({
        url: `${baseURL}/Form/definition/${formId}`,
        method: 'post',
        data
    });
}

export function getFormDefinition(formId: string) {
    return request<any>({
        url: `${baseURL}/Form/definition/${formId}`,
        method: 'get'
    })
}

/**
 * 更新表单
 * @param id 表单ID
 * @param data 表单数据
 */
export function updateForm(id: string, data: FormOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Form/${id}`,
        method: 'put',
        data
    })
}

/**
 * 删除表单
 * @param id 表单ID
 */
export function deleteForm(id: string) {
    return request<boolean>({
        url: `${baseURL}/Form`,
        method: 'delete',
        params: {id}
    })
}

/**
 * 获取表单字段列表
 * @param params 查询参数
 */
export function getFormFieldList(params?: Partial<FormFieldQueryParams>) {
    return request<FormFieldDto[]>({
        url: `${baseURL}/FormField`,
        method: 'get',
        params
    })
}

/**
 * 获取表单字段分页列表
 * @param params 查询参数
 */
export function getFormFieldPage(params: FormFieldQueryParams) {
    return request<PageResult<FormFieldDto>>({
        url: `${baseURL}/FormField/page`,
        method: 'get',
        params
    })
}

/**
 * 获取表单字段详情
 * @param id 表单字段ID
 */
export function getFormField(id: string) {
    return request<FormFieldDto>({
        url: `${baseURL}/FormField/${id}`,
        method: 'get'
    })
}

/**
 * 创建表单字段
 * @param data 表单字段数据
 */
export function createFormField(data: FormFieldOperationDto) {
    return request<boolean>({
        url: `${baseURL}/FormField`,
        method: 'post',
        data
    })
}

/**
 * 更新表单字段
 * @param id 表单字段ID
 * @param data 表单字段数据
 */
export function updateFormField(id: string, data: FormFieldOperationDto) {
    return request<boolean>({
        url: `${baseURL}/FormField/${id}`,
        method: 'put',
        data
    })
}

/**
 * 删除表单字段
 * @param id 表单字段ID
 */
export function deleteFormField(id: string) {
    return request<boolean>({
        url: `${baseURL}/FormField`,
        method: 'delete',
        params: {id}
    })
}

/**
 * 获取表单字段组列表
 * @param params 查询参数
 */
export function getFormFieldGroupList(params?: Partial<FormFieldGroupQueryParams>) {
    return request<FormFieldGroupDto[]>({
        url: `${baseURL}/FormFieldGroup`,
        method: 'get',
        params
    })
}

/**
 * 获取表单字段组分页列表
 * @param params 查询参数
 */
export function getFormFieldGroupPage(params: FormFieldGroupQueryParams) {
    return request<PageResult<FormFieldGroupDto>>({
        url: `${baseURL}/FormFieldGroup/page`,
        method: 'get',
        params
    })
}

/**
 * 获取表单字段组详情
 * @param id 表单字段组ID
 */
export function getFormFieldGroup(id: string) {
    return request<FormFieldGroupDto>({
        url: `${baseURL}/FormFieldGroup/${id}`,
        method: 'get'
    })
}

/**
 * 创建表单字段组
 * @param data 表单字段组数据
 */
export function createFormFieldGroup(data: FormFieldGroupOperationDto) {
    return request<boolean>({
        url: `${baseURL}/FormFieldGroup`,
        method: 'post',
        data
    })
}

/**
 * 更新表单字段组
 * @param id 表单字段组ID
 * @param data 表单字段组数据
 */
export function updateFormFieldGroup(id: string, data: FormFieldGroupOperationDto) {
    return request<boolean>({
        url: `${baseURL}/FormFieldGroup/${id}`,
        method: 'put',
        data
    })
}

/**
 * 删除表单字段组
 * @param id 表单字段组ID
 */
export function deleteFormFieldGroup(id: string) {
    return request<boolean>({
        url: `${baseURL}/FormFieldGroup`,
        method: 'delete',
        params: {id}
    })
}


/**
 * 分页获取表单实例
 * @param params 查询参数
 * @returns 分页结果
 */
export function getFormInstancePage(params: FormInstancePageQueryParams) {
    return request<PageResult<FormInstanceDto>>({
        url: `${baseURL}/FormInstance/page`,
        method: 'get',
        params
    })
}

export function getNewestFormInstancePage(params: FormInstancePageQueryParams) {
    return request<PageResult<FormInstanceDto>>({
        url: `${baseURL}/FormInstance/page-newest`,
        method: 'get',
        params
    })
}


export function existFormDataValue(formCode: string, formDataCode: string, formDataValue: string) {
    return request<boolean>({
        url: `${baseURL}/FormInstance/formData/exist/${formCode}/${formDataCode}/${formDataValue}`,
        method: 'get'
    })
}

export function getAvailableRegistrationNumbers(prefix: string, year: number) {

    /*
    response
    * {
          "prefix": "ITMCTR",
          "year": "2025",
          "usedNumbers": [1,2,3,5,7,8,12],
          "availableNumberRanges": [
            { "start": 4, "end": 4 },
            { "start": 6, "end": 6 },
            { "start": 9, "end": 11 },
            { "start": 13, "end": 999999 }
          ]
        }
    * */

    const data: Record<string, string> = {
        prefix: prefix,
        year: year.toString()
    };
    return request<AvailableRegistrationNumbersResponse>({
        url: `${baseURL}/FormInstance/formData/query/registration_number`,
        method: 'post',
        data: data
    });
}