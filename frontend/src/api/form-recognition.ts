import request from '@/utils/request'
import {
    AnnotationValue,
    FormCompareFieldDto,
    FormRecognitionContext,
    PreCheckResult
} from '@/dtos/itmctr'
import { getApiBaseUrl } from '@/config/env'

const baseURL = getApiBaseUrl('itmctr')

export function applyContentTranslate(businessId: string) {
    return request<string>({
        url: `${baseURL}/content-translate/${businessId}`,
        method: 'post'
    })
}
export function getContentTranslateResult(businessId: string) {
    return request<Record<string, AnnotationValue | null>>({
        url: `${baseURL}/content-translate/${businessId}`,
        method: 'get'
    })
}

// 文档填充检查
export function precheck(data: FormRecognitionContext) {
    return request<PreCheckResult>({
        url: `${baseURL}/precheck`,
        method: 'post',
        data,
        timeout: 100000,
    })
}

// 发起提取
export function extract(taskId: string) {
    return request<boolean>({
        url: `${baseURL}/extract/${taskId}`,
        method: 'post'
    })
}

// 发起内容对比
export function contentCompare(formId: string) {
    return request<boolean>({
        url: `${baseURL}/content-compare/${formId}`,
        method: 'post'
    })
}

// 获取表单对比差异
export function getContentCompare(formId: string) {
    return request<FormCompareFieldDto[]>({
        url: `${baseURL}/content-compare/${formId}`,
        method: 'get'
    })
}

// 发起全文翻译
export function contentTranslate(data: any) {
    return request<string>({
        url: `${baseURL}/content-translate`,
        method: 'post',
        data
    })
}

// 获取全文翻译结果
export function getContentTranslate(formId: string) {
    return request<{ [key: string]: AnnotationValue }>({
        url: `${baseURL}/content-translate/${formId}`,
        method: 'get'
    })
}

// 获取任务是否完成
export function getProgress(taskId: string, taskCode: string) {
    return request<boolean>({
        url: `${baseURL}/progress/${taskId}/${taskCode}`,
        method: 'get'
    })
}

// 尝试完成提取任务
export function extractProgress(taskId: string) {
    return request<number>({
        url: `${baseURL}/extract/progress/${taskId}`,
        method: 'post'
    })
}

// 尝试完成内容对比任务
export function contentCompareProgress(taskId: string) {
    return request<boolean>({
        url: `${baseURL}/content-compare/progress/${taskId}`,
        method: 'post'
    })
}

// 尝试完成全文翻译任务
export function contentTranslateProgress(requestId: string) {
    return request<boolean>({
        url: `${baseURL}/content-translate/progress/${requestId}`,
        method: 'post'
    })
}

// 实时翻译
export function realtimeTranslate(content: string) {
    return request<string>({
        url: `${baseURL}/realtime-translate`,
        method: 'post',
        data: { "content": content },
        timeout: 30000,
    })
}