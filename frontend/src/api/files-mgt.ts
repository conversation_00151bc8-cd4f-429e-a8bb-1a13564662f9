import request from '@/utils/request'
import {
  FileInfoDto,
  FileInfoQueryCriteria,
  FileTypeDto,
  FileTypeOperationDto,
  FileTypeQueryParams,
  FileTypeStorageRelDto,
  FileTypeStorageRelOperationDto,
  FileTypeStorageRelQueryParams,
  PageResult,
  StorageDto,
  StorageOperationDto,
  StorageQueryParams
} from '@/dtos'
import { getApiBaseUrl } from '@/config/env';

const baseURL = getApiBaseUrl('files-mgt');

/**
 * 获取存储列表
 * @param params 查询参数
 */
export function getStorageList(params?: Partial<StorageQueryParams>) {
  return request<StorageDto[]>({
    url: `${baseURL}/Storage`,
    method: 'get',
    params
  })
}

/**
 * 获取存储分页列表
 * @param params 查询参数
 */
export function getStoragePage(params: StorageQueryParams) {
  return request<PageResult<StorageDto>>({
    url: `${baseURL}/Storage/page`,
    method: 'get',
    params
  })
}

/**
 * 获取存储详情
 * @param id 存储ID
 */
export function getStorage(id: string) {
  return request<StorageDto>({
    url: `${baseURL}/Storage/${id}`,
    method: 'get'
  })
}

/**
 * 创建存储
 * @param data 存储数据
 */
export function createStorage(data: StorageOperationDto) {
  return request<boolean>({
    url: `${baseURL}/Storage`,
    method: 'post',
    data
  })
}

/**
 * 更新存储
 * @param id 存储ID
 * @param data 存储数据
 */
export function updateStorage(id: string, data: StorageOperationDto) {
  return request<boolean>({
    url: `${baseURL}/Storage/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除存储
 * @param id 存储ID
 */
export function deleteStorage(id: string) {
  return request<boolean>({
    url: `${baseURL}/Storage`,
    method: 'delete',
    params: { id }
  })
}

/**
 * 获取文件类型列表
 * @param params 查询参数
 */
export function getFileTypeList(params?: Partial<FileTypeQueryParams>) {
  return request<FileTypeDto[]>({
    url: `${baseURL}/FileType`,
    method: 'get',
    params
  })
}

/**
 * 获取文件类型分页列表
 * @param params 查询参数
 */
export function getFileTypePage(params: FileTypeQueryParams) {
  return request<PageResult<FileTypeDto>>({
    url: `${baseURL}/FileType/page`,
    method: 'get',
    params
  })
}

/**
 * 获取文件类型详情
 * @param id 文件类型ID
 */
export function getFileType(id: string) {
  return request<FileTypeDto>({
    url: `${baseURL}/FileType/${id}`,
    method: 'get'
  })
}

/**
 * 创建文件类型
 * @param data 文件类型数据
 */
export function createFileType(data: FileTypeOperationDto) {
  return request<boolean>({
    url: `${baseURL}/FileType`,
    method: 'post',
    data
  })
}

/**
 * 更新文件类型
 * @param id 文件类型ID
 * @param data 文件类型数据
 */
export function updateFileType(id: string, data: FileTypeOperationDto) {
  return request<boolean>({
    url: `${baseURL}/FileType/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除文件类型
 * @param id 文件类型ID
 */
export function deleteFileType(id: string) {
  return request<boolean>({
    url: `${baseURL}/FileType`,
    method: 'delete',
    params: { id }
  })
}

/**
 * 获取文件类型存储关系列表
 * @param params 查询参数
 */
export function getFileTypeStorageRelList(params?: Partial<FileTypeStorageRelQueryParams>) {
  return request<FileTypeStorageRelDto[]>({
    url: `${baseURL}/FileTypeStorageRel`,
    method: 'get',
    params
  })
}

/**
 * 获取文件类型存储关系分页列表
 * @param params 查询参数
 */
export function getFileTypeStorageRelPage(params: FileTypeStorageRelQueryParams) {
  return request<PageResult<FileTypeStorageRelDto>>({
    url: `${baseURL}/FileTypeStorageRel/page`,
    method: 'get',
    params
  })
}

/**
 * 获取文件类型存储关系详情
 * @param id 文件类型存储关系ID
 */
export function getFileTypeStorageRel(id: string) {
  return request<FileTypeStorageRelDto>({
    url: `${baseURL}/FileTypeStorageRel/${id}`,
    method: 'get'
  })
}

/**
 * 创建文件类型存储关系
 * @param data 文件类型存储关系数据
 */
export function createFileTypeStorageRel(data: FileTypeStorageRelOperationDto) {
  return request<boolean>({
    url: `${baseURL}/FileTypeStorageRel`,
    method: 'post',
    data
  })
}

/**
 * 更新文件类型存储关系
 * @param id 文件类型存储关系ID
 * @param data 文件类型存储关系数据
 */
export function updateFileTypeStorageRel(id: string, data: FileTypeStorageRelOperationDto) {
  return request<boolean>({
    url: `${baseURL}/FileTypeStorageRel/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除文件类型存储关系
 * @param id 文件类型存储关系ID
 */
export function deleteFileTypeStorageRel(id: string) {
  return request<boolean>({
    url: `${baseURL}/FileTypeStorageRel`,
    method: 'delete',
    params: { id }
  })
}

/**
 * 获取文件信息
 * @param id 文件ID
 */
export function getFileInfo(id: string) {
  return request<FileInfoDto>({
    url: `${baseURL}/FileInfo/${id}`,
    method: 'get'
  })
}

/**
 * 获取文件信息列表
 * @param params 查询参数
 */
export function getFileInfoList(params?: FileInfoQueryCriteria) {
  return request<FileInfoDto[]>({
    url: `${baseURL}/FileInfo`,
    method: 'get',
    params
  })
}

/**
 * 获取文件信息分页列表
 * @param params 查询参数
 */
export function getFileInfoPage(params: any) {
  return request<PageResult<FileInfoDto>>({
    url: `${baseURL}/FileInfo/page`,
    method: 'get',
    params
  })
}