import request from '@/utils/request'
import {
    PageResult,
    UserQueryParams

} from '@/dtos'
import { getApiBaseUrl } from '@/config/env'
import {ApplicationLogDto} from "@/dtos/logging.dto";

const baseURL = getApiBaseUrl('logging-mgt')



/**
 * 获取用户分页列表
 * @param params 查询参数
 */
export function getPage(params: UserQueryParams) {
    return request<PageResult<ApplicationLogDto>>({
        url: `${baseURL}/User/page`,
        method: 'get',
        params
    })
}

