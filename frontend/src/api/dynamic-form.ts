import request from '@/utils/request'
import {
    FormDefinitionDto,
    FormInstanceDto,
    FormInstanceJsonDto, InternalPageQueryParams,
    NewFormInstanceDto,
    UserFormInstancePageQueryParams
} from '@/dtos/dynamic-form.dto'
import {getApiBaseUrl} from '@/config/env';

const baseURL = getApiBaseUrl('dynamic-form');


/**
 * 使用表单代码获取当前表单的最新定义
 * @param forCode 表单代码
 * @returns 表单定义
 */
export function getFormDefinition(formCode: string) {
    return request<FormDefinitionDto>({
        url: `${baseURL}/Form/definition/${formCode}`,
        method: 'get'
    })
}

/**
 * 使用formCode和业务id创建表单实例（如果有上一版本从上一版本获取表单定义）
 * @param formCode 表单代码
 * @param businessId 业务ID
 * @returns 表单实例ID
 */
export function createFormInstance(formCode: string, businessId: string) {
    return request<string>({
        url: `${baseURL}/FormInstance/instance/${formCode}/${businessId}`,
        method: 'post'
    })
}

/**
 * 使用formCode和业务id创建基于最新定义的表单实例
 * @param formCode 表单代码
 * @param businessId 业务ID
 * @returns 表单实例ID
 */
export function createFormInstanceFromNewest(formCode: string, businessId: string) {
    return request<string>({
        url: `${baseURL}/FormInstance/instance/${formCode}/${businessId}/newest`,
        method: 'post'
    })
}

/**
 * 使用业务id获取当前表单的最新版本
 * @param businessId 业务ID
 * @param version 表单版本
 * @returns 表单定义
 */
export function getLatestFormInstance(businessId: string, version: string) {
    return request<FormDefinitionDto>({
        url: `${baseURL}/FormInstance/instance/${businessId}/${version}/newest`,
        method: 'get'
    })
}


/**
 * 无实例保存表单实例
 * @param formCode 表单编码
 * @param formDefinition 表单定义
 * @returns 保存结果
 */
export function saveFormInstanceWithoutVersion(formCode: string, formDefinition: FormDefinitionDto) {
    return request<NewFormInstanceDto>({
        url: `${baseURL}/FormInstance/instance/${formCode}`,
        method: 'post',
        data: formDefinition
    })
}


/**
 * 保存表单实例
 * @param businessId 业务ID
 * @param version 版本号
 * @param formDefinition 表单定义
 * @returns 保存结果
 */
export function saveFormInstance(businessId: string, version: string, formDefinition: FormDefinitionDto) {
    return request<boolean>({
        url: `${baseURL}/FormInstance/instance/${businessId}/${version}`,
        method: 'put',
        data: formDefinition
    })
}


/**
 * 通过业务id和版本号获取指定版本的表单实例
 * @param businessId 业务ID
 * @param version 版本号
 * @returns 表单定义
 */
export function getFormInstanceByVersion(businessId: string, version: string) {
    return request<FormDefinitionDto>({
        url: `${baseURL}/FormInstance/instance/${businessId}/${version}`,
        method: 'get'
    })
}

/**
 * 取消当前版本表单实例
 * @param businessId 业务ID
 * @param version 版本号
 * @returns 取消结果
 */
export function cancelFormInstance(businessId: string, version: string) {
    return request<boolean>({
        url: `${baseURL}/FormInstance/instance/cancel/${businessId}/${version}`,
        method: 'post'
    })
}

/**
 * 提交当前版本表单实例
 * @param businessId 业务ID
 * @param version 版本号
 * @returns 提交结果
 */
export function submitFormInstance(businessId: string, version: string) {
    return request<boolean>({
        url: `${baseURL}/FormInstance/instance/submit/${businessId}/${version}`,
        method: 'post'
    })
}

/**
 * 获取与上一版本的差异
 * @param businessId 业务ID
 * @param version 版本号
 * @returns 差异表单定义
 */
export function getDiffWithPreviousVersion(businessId: string, version: string) {
    return request<FormDefinitionDto>({
        url: `${baseURL}/FormInstance/instance/diff/${businessId}/${version}`,
        method: 'get'
    })
}

/**
 * 获取与指定版本的差异
 * @param businessId 业务ID
 * @param version 版本号
 * @param namedVersion 指定待比较的版本号
 * @returns 差异表单定义
 */
export function getDiffWithSpecificVersion(businessId: string, version: string, namedVersion: string) {
    return request<FormDefinitionDto>({
        url: `${baseURL}/FormInstance/instance/diff/${businessId}/${version}/${namedVersion}`,
        method: 'get'
    })
}

/**
 * 根据业务id获取所有版本的表单实例
 * @param businessId 业务ID
 * @returns 表单实例列表
 */
export function getFormInstanceHistories(businessId: string) {
    return request<FormInstanceDto[]>({
        url: `${baseURL}/FormInstance/instance/histories/${businessId}`,
        method: 'get'
    })
}

/**
 * 获取当前表单验证结果 针对于提交前的验证
 * @param businessId 业务ID
 * @param version 版本号
 * @returns 验证结果
 */
export function validateFormInstance(businessId: string, version: string) {
    return request<boolean>({
        url: `${baseURL}/FormInstance/instance/validate/${businessId}/${version}`,
        method: 'get'
    })
}

/**
 * 分页获取表单实例
 * @param params 查询参数
 * @returns 分页结果
 */
export function getUserFormInstancePage(params: UserFormInstancePageQueryParams) {
    return request<FormInstanceDataDto>({
        url: `${baseURL}/FormInstance/page`,
        method: 'get',
        params
    })
}


export interface FormInstanceDataDto {
    rows: FormInstanceJsonDto[]
    formDefinition: FormDefinitionDto
    totals: number
}
