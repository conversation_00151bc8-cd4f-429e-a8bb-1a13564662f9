import request from '@/utils/request'
import { CaptchaResult, ChangePasswordParams, ConfirmEmailParams, LoginParams, LoginResult, PermissionDto, RefreshTokenParams, ResetPasswordParams, SendEmailConfirmationCodeParams, SendPhoneConfirmationCodeParams, UpdateProfileParams, UserInfo,RegisterParams, PositionResult } from '@/dtos'
import { getApiBaseUrl } from '@/config/env'

const baseURL = getApiBaseUrl('rbac')

/**
 * 获取验证码
 */
export function getCaptcha() {
  return request<CaptchaResult>({
    url: `${baseURL}/User/captcha`,
    method: 'get'
  })
}

/**
 * 用户登录
 * @param data 登录参数
 */
export function login(data: LoginParams) {
  return request<LoginResult>({
    url: `${baseURL}/User/login`,
    method: 'post',
    data
  })
}

/**
 * 用户登出
 */
export function logout() {
  return request<boolean>({
    url: `${baseURL}/User/logout`,
    method: 'post'
  })
}

/**
 * 刷新令牌
 * @param data 刷新令牌参数
 */
export function refreshToken(data: RefreshTokenParams) {
  return request<LoginResult>({
    url: `${baseURL}/User/refresh-token`,
    method: 'post',
    data,
    // 添加超时设置，避免长时间等待
    timeout: 5000
  }).catch((error: any) => {
    console.error('刷新令牌API请求失败:', error)
    //
    // // 如果是401错误，立即跳转到登录页
    // if (error.response && error.response.status === 401) {
    //   console.log('刷新令牌返回401，立即跳转到登录页')
    //
    //   // 使用replace强制刷新页面
    //   window.location.replace('/login')
    //
    //   // 添加一个延迟执行的备份跳转
    //   setTimeout(() => {
    //     console.log('执行备份跳转')
    //     window.location.href = '/login'
    //   }, 100)
    // }

    return Promise.reject(error)
  })
}

/**
 * 获取用户权限
 *
 * 注意：此方法已不再使用，权限数据直接从登录响应中获取
 */
export function getUserPermissions() {
  return request<PermissionDto[]>({
    url: `${baseURL}/Permission/permissions`,
    method: 'get'
  })
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return request<UserInfo>({
    url: `${baseURL}/User/user-info`,
    method: 'get'
  })
}


/**
 * 更新个人资料
 * @param data 更新个人资料参数
 */
export function updateProfile(data: UpdateProfileParams) {
  return request<boolean>({
    url: `${baseURL}/User/update-profile`,
    method: 'post',
    data
  })
}
/**
 * 发送邮箱确认码
 * @param data 发送邮箱确认码参数
 */
export function sendEmailConfirmationCode(data: SendEmailConfirmationCodeParams) {
  return request<boolean>({
    url: `${baseURL}/User/send-email-confirmation-code`,
    method: 'post',
    data
  })
}


/**
 * 确认邮箱
 * @param data 确认邮箱参数
 */
export function confirmEmail(data: ConfirmEmailParams) {
  return request<boolean>({
    url: `${baseURL}/User/confirm-email`,
    method: 'post',
    data
  })
}

/**
 * 发送手机确认码
 * @param data 发送手机确认码参数
 */
export function sendPhoneConfirmationCode(data: SendPhoneConfirmationCodeParams) {
  return request<boolean>({
    url: `${baseURL}/User/send-phone-confirmation-code`,
    method: 'post',
    data
  })
}

// 确认手机号参数
export interface ConfirmPhoneParams {
  phoneNumber: string
  code: string
}

/**
 * 确认手机号
 * @param data 确认手机号参数
 */
export function confirmPhone(data: ConfirmPhoneParams) {
  return request<boolean>({
    url: `${baseURL}/User/confirm-phone`,
    method: 'post',
    data
  })
}

/**
 * 修改密码
 * @param data 修改密码参数
 */
export function changePassword(data: ChangePasswordParams) {
  return request<boolean>({
    url: `${baseURL}/User/change-password`,
    method: 'post',
    data
  })
}

// 忘记密码参数
export interface ForgotPasswordParams {
  username: string
  verifyMethod: string // 'email' 或 'phone'
  captchaId: string
  captchaCode: string
}

/**
 * 忘记密码（发送验证码）
 * @param data 忘记密码参数
 */
export function forgotPassword(data: ForgotPasswordParams) {
  return request<boolean>({
    url: `${baseURL}/User/forgot-password`,
    method: 'post',
    data
  })
}

/**
 * 重置密码
 * @param data 重置密码参数
 */
export function resetPassword(data: ResetPasswordParams) {
  return request<boolean>({
    url: `${baseURL}/User/reset-password`,
    method: 'post',
    data
  })
}

/**
 * 用户注册
 * @param data 注册参数
 * @returns 
 */
export function Register(data: RegisterParams) {
  return request<boolean>({
    url: `${baseURL}/User/register`,
    method: 'post',
    data
  })
}

export function getUserPositions() {
  return request<PositionResult[]>({
    url: `${baseURL}/User/user-positions`,
    method: 'get'
  })
}