import request from '@/utils/request'

import {
    FormDefinitionDto,
    FormInstanceDto,
    FormInstanceJsonDto,
    UserFormInstancePageQueryParams
} from "@/dtos/dynamic-form.dto";
import {getApiBaseUrl} from '@/config/env';
import {IndexProjectCountRequestDto, TrialSearchPageQueryParams} from "@/dtos/itmctr";
import {PageResult} from '@/dtos';
import {ProjectSearchResultDto, ProjectViewDto} from "@/dtos/project.dto";

const baseURL = getApiBaseUrl('itmctr');


export function getProjectDefinition() {
    return request<FormDefinitionDto>({
        url: `${baseURL}/Project/define`,
        method: 'get'
    })
}

export function getProject(businessId: string) {
    return request<FormDefinitionDto>({
        url: `${baseURL}/Project/newest/${businessId}`,
        method: 'get'
    })
}

export function getProjectWithDiff(businessId: string) {
    return request<FormDefinitionDto>({
        url: `${baseURL}/Project/diff/${businessId}`,
        method: 'get'
    })
}


export function createExtractProject(taskId: string) {
    return request<string>({
        url: `${baseURL}/Project/create/${taskId}`,
        method: 'post'
    })
}


export function createProject(formDefinition: FormDefinitionDto) {
    return request<string>({
        url: `${baseURL}/Project/save`,
        method: 'post',
        data: formDefinition
    })
}

export function saveProject(businessId: string, formDefinition: FormDefinitionDto) {
    return request<string>({
        url: `${baseURL}/Project/save/${businessId}`,
        method: 'post',
        data: formDefinition
    })
}

export function submitProject(businessId: string) {
    return request<string>({
        url: `${baseURL}/Project/submit/${businessId}`,
        method: 'post',
        timeout: 30000,
    })
}

export function getIndexProjectCount(data: IndexProjectCountRequestDto) {
    return request<Record<string, number>>({
        url: `${baseURL}/Project/indexProjectCount`,
        method: 'post',
        data: data
    })
}


/**
 * 分页获取表单实例
 * @param params 查询参数
 * @returns 分页结果
 */
export function getTrialSearchPage(params: TrialSearchPageQueryParams) {
    return request<PageResult<ProjectSearchResultDto>>({
        url: `${baseURL}/Project/trialsearch`,
        method: 'get',
        params
    })
}

export function editApplyProject(businessId: string, data: any) {
    return request<boolean>({
        url: `${baseURL}/Project/edit-apply-project/${businessId}`,
        method: 'post',
        data: data
    })
}

export function getSelectOptions() {
    return request<any[]>({
        url: `${baseURL}/Project/options`,
        method: 'get'
    })

}

export function getProjectInfo(key: number | string, isHistory: boolean = false) {
    return request<ProjectViewDto>({
        url: `${baseURL}/Project/info/${key}/${isHistory}`,
        method: 'get'
    })
}

export function getHistoryPage(params: TrialSearchPageQueryParams) {
    return request<PageResult<ProjectSearchResultDto>>({
        url: `${baseURL}/Project/history`,
        method: 'get',
        params
    })
}

export function test() {
    return request<boolean>({
        url: `${baseURL}/Project/test`,
        method: 'get'
    })
}

export interface FormInstanceDataDto {
    rows: FormInstanceJsonDto[]
    formDefinition: FormDefinitionDto
    totals: number
}