import {
    JudgeProjectDto,
    AssignProjectDto,
    AssignReviewProjectDto,
    AnnotationValue
} from "@/dtos/itmctr";
import request from '@/utils/request'
import {EditProjectRejectOperationDto, SendNumberDto} from "@/dtos/dynamic-form-mgt.dto";
import {getApiBaseUrl} from '@/config/env';
import {InternalPageQueryParams} from "@/dtos/dynamic-form.dto";
import {FormInstanceDataDto} from "@/api/itmctr";

const baseURL = getApiBaseUrl('itmctr-mgt');

export function judgeProject(businessId: string, data: JudgeProjectDto) {
    return request<boolean>({
        url: `${baseURL}/Project/judge/${businessId}`,
        method: 'post',
        data
    })
}


export function assignProject(businessId: string, data: AssignProjectDto) {
    return request<boolean>({
        url: `${baseURL}/Project/assign/${businessId}`,
        method: 'post',
        data
    })
}

export function returnProjectLevel2(businessId: string, data: AssignProjectDto) {
    return request<boolean>({
        url: `${baseURL}/Project/return-level2/${businessId}`,
        method: 'post',
        data
    })
}


export function assignReviewProject(businessId: string, data: AssignReviewProjectDto) {
    return request<boolean>({
        url: `${baseURL}/Project/assign-review/${businessId}`,
        method: 'post',
        data
    })
}

export function approvalProjectLevel4(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/approval/${businessId}/level4`,
        method: 'post',
    })
}

export function rejectProjectLevel4(businessId: string, annotationValues: Record<string, AnnotationValue | null>) {
    return request<boolean>({
        url: `${baseURL}/Project/reject/${businessId}/level4`,
        method: 'post',
        data: annotationValues
    })
}

export function approvalProjectLevel3(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/approval/${businessId}/level3`,
        method: 'post',
    })
}

export function rejectProjectLevel3(businessId: string, annotationValues: Record<string, AnnotationValue | null>) {
    return request<boolean>({
        url: `${baseURL}/Project/reject/${businessId}/level3`,
        method: 'post',
        data: annotationValues
    })
}


export function approvalProjectLevel2(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/approval/${businessId}/level2`,
        method: 'post',
    })
}

export function rejectProjectLevel2(businessId: string, annotationValues: Record<string, AnnotationValue | null>) {
    return request<boolean>({
        url: `${baseURL}/Project/reject/${businessId}/level2`,
        method: 'post',
        data: annotationValues
    })
}


export function approvalProjectLevel1(businessId: string, input: SendNumberDto) {
    return request<boolean>({
        url: `${baseURL}/Project/approval/${businessId}/level1`,
        method: 'post',
        data: input
    })
}

export function rejectProjectLevel1(businessId: string, input: SendNumberDto) {
    return request<boolean>({
        url: `${baseURL}/Project/reject/${businessId}/level1`,
        method: 'post',
        data: input
    })
}

export function approvalProjectLevel1edit(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/approval/${businessId}/level1/edit`,
        method: 'post'
    })
}

export function rejectProjectLevel1edit(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/reject/${businessId}/level1/edit`,
        method: 'post'
    })
}


export function editConfirmedProject(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/edit-confirmed/${businessId}`,
        method: 'post'
    })
}

export function editRejectedProject(businessId: string, dto: EditProjectRejectOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Project/edit-rejected/${businessId}`,
        method: 'post',
        data: dto
    })
}

export function RecallProject(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/recall/${businessId}`,
        method: 'post'
    })
}

export function getPage(base: string, params: InternalPageQueryParams) {
    return request<FormInstanceDataDto>({
        url: `${baseURL}/Project/${base}`,
        method: 'get',
        params
    })
}