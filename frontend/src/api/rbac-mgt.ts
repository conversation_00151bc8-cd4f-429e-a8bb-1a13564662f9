import request from '@/utils/request'
import {
    PermissionDto,
    UserDto,
    UserOperationDto,
    RoleDto,
    RoleOperationDto,
    UserRoleDto,
    UserRoleOperationDto,
    OrganizationDto,
    OrganizationOperationDto,
    UserOrganizationDto,
    UserOrganizationOperationDto,
    PositionDto,
    PositionOperationDto,
    UserPositionDto,
    UserPositionOperationDto,
    PermissionOperationDto,
    RolePermissionDto,
    RolePermissionOperationDto,
    PermissionTreeDto,
    PageResult,
    PermissionQueryParams,
    PositionQueryParams,
    OrganizationQueryParams,
    RoleQueryParams,
    UserQueryParams, OrganizationUserDto
} from '@/dtos'
import { getApiBaseUrl } from '@/config/env'

const baseURL = getApiBaseUrl('rbac-mgt')

/**
 * 获取用户列表
 * @param params 查询参数
 */
export function getUserList(params: UserQueryParams) {
    return request<UserDto[]>({
        url: `${baseURL}/User`,
        method: 'get',
        params
    })
}

/**
 * 获取用户分页列表
 * @param params 查询参数
 */
export function getUserPage(params: UserQueryParams) {
    return request<PageResult<UserDto>>({
        url: `${baseURL}/User/page`,
        method: 'get',
        params
    })
}

/**
 * 获取用户详情
 * @param id 用户ID
 */
export function getUserById(id: string) {
    return request<UserDto>({
        url: `${baseURL}/User/${id}`,
        method: 'get'
    })
}


export function getUsersByCommonRoleCode(roleCode: string) {
    return request<UserDto[]>({
        url: `${baseURL}/User/role-in-common/${roleCode}`,
        method: 'get'
    })
}

export function getManagedPositionUsers(positionCode: string) {
    return request<OrganizationUserDto[]>({
        url: `${baseURL}/User/position-in-user-managed/${positionCode}`,
        method: 'get'
    })
}

/**
 * 创建用户
 * @param data 用户数据
 */
export function createUser(data: UserOperationDto) {
    return request<boolean>({
        url: `${baseURL}/User`,
        method: 'post',
        data
    })
}

/**
 * 更新用户
 * @param id 用户ID
 * @param data 用户数据
 */
export function updateUser(id: string, data: UserOperationDto) {
    return request<boolean>({
        url: `${baseURL}/User/${id}`,
        method: 'put',
        data
    })
}

/**
 * 启用用户
 * @param id 用户ID
 */
export function enableUser(id: string) {
    return request<boolean>({
        url: `${baseURL}/User/${id}/enable`,
        method: 'put'
    })
}

/**
 * 禁用用户
 * @param id 用户ID
 */
export function disableUser(id: string) {
    return request<boolean>({
        url: `${baseURL}/User/${id}/disable`,
        method: 'put'
    })
}

/**
 * 获取用户角色列表
 * @param params 查询参数
 */
export function getUserRoleList(params: { userId?: string, roleId?: string }) {
    return request<UserRoleDto[]>({
        url: `${baseURL}/UserRole`,
        method: 'get',
        params: params
    })
}

/**
 * 创建用户角色
 * @param data 用户角色数据
 */
export function createUserRole(data: UserRoleOperationDto) {
    return request<boolean>({
        url: `${baseURL}/UserRole`,
        method: 'post',
        data
    })
}

/**
 * 删除用户角色
 * @param id 用户角色ID
 */
export function deleteUserRole(id: string) {
    return request<boolean>({
        url: `${baseURL}/UserRole/${id}`,
        method: 'delete'
    })
}

/**
 * 批量更新用户角色
 * @param userId 用户ID
 * @param data 用户角色数据
 */
export function updateUserRoles(userId: string, data: UserRoleOperationDto[]) {
    return request<boolean>({
        url: `${baseURL}/UserRole/${userId}`,
        method: 'put',
        data
    })
}

/**
 * 获取组织机构列表
 * @param criteria 查询条件
 */
export function getOrganizationList(criteria?: OrganizationQueryParams) {
    return request<OrganizationDto[]>({
        url: `${baseURL}/Organization`,
        method: 'get',
        params: criteria
    })
}

/**
 * 获取组织机构分页列表
 * @param params 查询参数
 */
export function getOrganizationPage(params: OrganizationQueryParams) {
    return request<PageResult<OrganizationDto>>({
        url: `${baseURL}/Organization/page`,
        method: 'get',
        params
    })
}

/**
 * 获取角色列表
 * @param params 查询参数
 */
export function getRoleList(params?: RoleQueryParams) {
    return request<RoleDto[]>({
        url: `${baseURL}/Role`,
        method: 'get',
        params
    })
}

/**
 * 获取角色分页列表
 * @param params 查询参数
 */
export function getRolePage(params: RoleQueryParams) {
    return request<PageResult<RoleDto>>({
        url: `${baseURL}/Role/page`,
        method: 'get',
        params
    })
}

/**
 * 获取角色详情
 * @param id 角色ID
 */
export function getRoleById(id: string) {
    return request<RoleDto>({
        url: `${baseURL}/Role/${id}`,
        method: 'get'
    })
}

/**
 * 创建角色
 * @param data 角色数据
 */
export function createRole(data: RoleOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Role`,
        method: 'post',
        data
    })
}

/**
 * 更新角色
 * @param id 角色ID
 * @param data 角色数据
 */
export function updateRole(id: string, data: RoleOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Role/${id}`,
        method: 'put',
        data
    })
}

/**
 * 删除角色
 * @param id 角色ID
 */
export function deleteRole(id: string) {
    return request<boolean>({
        url: `${baseURL}/Role/${id}`,
        method: 'delete'
    })
}

/**
 * 获取组织机构详情
 * @param id 组织机构ID
 */
export function getOrganizationById(id: string) {
    return request<OrganizationDto>({
        url: `${baseURL}/Organization/${id}`,
        method: 'get'
    })
}

/**
 * 创建组织机构
 * @param data 组织机构数据
 */
export function createOrganization(data: OrganizationOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Organization`,
        method: 'post',
        data
    })
}

/**
 * 更新组织机构
 * @param id 组织机构ID
 * @param data 组织机构数据
 */
export function updateOrganization(id: string, data: OrganizationOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Organization/${id}`,
        method: 'put',
        data
    })
}

/**
 * 删除组织机构
 * @param id 组织机构ID
 */
export function deleteOrganization(id: string) {
    return request<boolean>({
        url: `${baseURL}/Organization/${id}`,
        method: 'delete'
    })
}

/**
 * 获取用户组织机构列表
 * @param params 查询参数
 */
export function getUserOrganizationList(params: { UserId?: string }) {
    return request<UserOrganizationDto[]>({
        url: `${baseURL}/UserOrganization`,
        method: 'get',
        params
    })
}

/**
 * 创建用户组织机构
 * @param data 用户组织机构数据
 */
export function createUserOrganization(data: UserOrganizationOperationDto) {
    return request<boolean>({
        url: `${baseURL}/UserOrganization`,
        method: 'post',
        data
    })
}

/**
 * 删除用户组织机构
 * @param id 用户组织机构ID
 */
export function deleteUserOrganization(id: string) {
    return request<boolean>({
        url: `${baseURL}/UserOrganization/${id}`,
        method: 'delete'
    })
}

/**
 * 批量更新用户组织机构
 * @param userId 用户ID
 * @param data 用户组织机构数据
 */
export function updateUserOrganizations(userId: string, data: { organizationId: string, isPrimary: boolean }[]) {
    return request<boolean>({
        url: `${baseURL}/UserOrganization/${userId}`,
        method: 'put',
        data
    })
}


/**
 * 获取职位列表
 * @param criteria 查询条件
 */
export function getPositionList(criteria?: PositionQueryParams) {
    return request<PositionDto[]>({
        url: `${baseURL}/Position`,
        method: 'get',
        params: criteria
    })
}

/**
 * 获取职位分页列表
 * @param params 查询参数
 */
export function getPositionPage(params: PositionQueryParams) {
    return request<PageResult<PositionDto>>({
        url: `${baseURL}/Position/page`,
        method: 'get',
        params
    })
}

/**
 * 获取职位详情
 * @param id 职位ID
 */
export function getPositionById(id: string) {
    return request<PositionDto>({
        url: `${baseURL}/Position/${id}`,
        method: 'get'
    })
}

/**
 * 创建职位
 * @param data 职位数据
 */
export function createPosition(data: PositionOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Position`,
        method: 'post',
        data
    })
}

/**
 * 更新职位
 * @param id 职位ID
 * @param data 职位数据
 */
export function updatePosition(id: string, data: PositionOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Position/${id}`,
        method: 'put',
        data
    })
}

/**
 * 删除职位
 * @param id 职位ID
 */
export function deletePosition(id: string) {
    return request<boolean>({
        url: `${baseURL}/Position/${id}`,
        method: 'delete'
    })
}

/**
 * 获取用户职位列表
 * @param params 查询参数
 */
export function getUserPositionList(params: { userId?: string }) {
    return request<UserPositionDto[]>({
        url: `${baseURL}/UserPosition`,
        method: 'get',
        params
    })
}

/**
 * 创建用户职位
 * @param data 用户职位数据
 */
export function createUserPosition(data: UserPositionOperationDto) {
    return request<boolean>({
        url: `${baseURL}/UserPosition`,
        method: 'post',
        data
    })
}

/**
 * 删除用户职位
 * @param id 用户职位ID
 */
export function deleteUserPosition(id: string) {
    return request<boolean>({
        url: `${baseURL}/UserPosition/${id}`,
        method: 'delete'
    })
}

/**
 * 批量更新用户职位
 * @param userId 用户ID
 * @param data 用户职位数据
 */
export function updateUserPositions(userId: string, data: UserPositionOperationDto[]) {
    return request<boolean>({
        url: `${baseURL}/UserPosition/${userId}`,
        method: 'put',
        data
    })
}

/**
 * 获取权限列表
 * @param criteria 查询条件
 */
export function getPermissionList(criteria?: PermissionQueryParams) {
    return request<PermissionDto[]>({
        url: `${baseURL}/Permission`,
        method: 'get',
        params: criteria
    })
}

/**
 * 获取权限分页列表
 * @param params 查询参数
 */
export function getPermissionPage(params: PermissionQueryParams) {
    return request<PageResult<PermissionDto>>({
        url: `${baseURL}/Permission/page`,
        method: 'get',
        params
    })
}

/**
 * 获取权限详情
 * @param id 权限ID
 */
export function getPermissionById(id: string) {
    return request<PermissionDto>({
        url: `${baseURL}/Permission/${id}`,
        method: 'get'
    })
}

/**
 * 创建权限
 * @param data 权限数据
 */
export function createPermission(data: PermissionOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Permission`,
        method: 'post',
        data
    })
}

/**
 * 更新权限
 * @param id 权限ID
 * @param data 权限数据
 */
export function updatePermission(id: string, data: PermissionOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Permission/${id}`,
        method: 'put',
        data
    })
}

/**
 * 删除权限
 * @param id 权限ID
 */
export function deletePermission(id: string) {
    return request<boolean>({
        url: `${baseURL}/Permission/${id}`,
        method: 'delete'
    })
}

/**
 * 获取角色权限列表
 * @param params 查询参数
 */
export function getRolePermissionList(params: { RoleId?: string }) {
    return request<RolePermissionDto[]>({
        url: `${baseURL}/RolePermission`,
        method: 'get',
        params
    })
}

/**
 * 批量更新角色权限
 * @param roleId 角色ID
 * @param data 角色权限数据
 */
export function updateRolePermissions(roleId: string, data: RolePermissionOperationDto[]) {
    return request<boolean>({
        url: `${baseURL}/RolePermission/${roleId}`,
        method: 'put',
        data
    })
}

/**
 * 批量更新API权限
 * @param appCode 应用代码
 * @param parentId 父级权限ID
 * @param data 权限树数据
 */
export function batchUpdateApiPermissions(appCode: string, data: PermissionTreeDto[]) {
    const url = `${baseURL}/Permission/batch/${appCode}`;

    return request<boolean>({
        url,
        method: 'post',
        data
    })
}
