import request from '@/utils/request'
import type { FileUploadResponseDto, FileTypeDto } from '@/dtos/common'
import type { AxiosResponse } from 'axios'
import { getApiBaseUrl } from '@/config/env'

const baseURL = getApiBaseUrl('files')

// 分片上传（首块/后续块自动判断）
export function uploadChunk(data: FormData): Promise<AxiosResponse<FileUploadResponseDto>> {
  return request.post(`${baseURL}/FileInfo/chunk`, data)
}

// 获取下载令牌
export async function getDownloadToken(fileId: string) {
  return request.post(`${baseURL}/FileInfo/download-token/${fileId}`, null);
}

// 获取上传附件类型定义
export function getFileTypeDefine(typeCode: string) {
  return request<FileTypeDto>({
    url: `${baseURL}/FileInfo/define/${typeCode}`,
    method: 'get'
  })
}