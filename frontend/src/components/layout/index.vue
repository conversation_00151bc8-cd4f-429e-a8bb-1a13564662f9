<template>
  <div class="app-wrapper">
    <div class="main-container">
      <navbar />
      <horizontal-menu />
      <div class="content-wrapper">
        <app-main />
        <!-- 浮动操作按钮区域 -->
        <div class="floating-actions-wrapper" v-if="$slots.floatingActions">
          <div class="floating-actions-container">
            <slot name="floatingActions"></slot>
          </div>
        </div>
        <app-footer />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AppMain, Navbar, HorizontalMenu } from "./components";
import AppFooter from "./Footer.vue";
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.floating-actions-wrapper {
  position: sticky;
  bottom: 0;
  z-index: 100;

  .floating-actions-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
    padding: 12px 24px;
    display: flex;
    justify-content: center;
    gap: 12px;

    // 响应式适配
    @media (max-width: 768px) {
      padding: 8px 16px;
      gap: 8px;

      :deep(.el-button) {
        font-size: 12px;
        padding: 8px 12px;
      }
    }
  }
}
</style>
