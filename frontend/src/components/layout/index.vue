<template>
  <div class="app-wrapper">
    <div class="main-container">
      <navbar />
      <horizontal-menu />
      <div class="content-wrapper">
        <app-main />
        <app-footer />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AppMain, Navbar, HorizontalMenu } from "./components";
import AppFooter from "./Footer.vue";
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
