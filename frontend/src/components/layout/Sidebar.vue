<template>
  <div class="sidebar">
    <el-scrollbar>
      <Menu
        :is-collapse="isCollapse"
        :background-color="'#304156'"
        :text-color="'#bfcbd9'"
        :active-text-color="'#409EFF'"
      />
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Menu from '@/components/Menu/index.vue'

const isCollapse = ref(false)
</script>

<style lang="scss" scoped>
.sidebar {
  height: 100%;
  background-color: #304156;

  .logo-container {
    height: 50px;
    padding: 10px 0;
    text-align: center;
    overflow: hidden;

    a {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-decoration: none;

      .sidebar-logo {
        width: 32px;
        height: 32px;
        vertical-align: middle;
        margin-right: 12px;
      }

      .sidebar-title {
        display: inline-block;
        margin: 0;
        color: #fff;
        font-weight: 600;
        line-height: 50px;
        font-size: 14px;
        font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
        vertical-align: middle;
      }
    }
  }

  :deep(.el-menu) {
    border: none;
  }
}
</style>