<template>
  <div class="footer">
    <div class="copyright">
      {{ t('Footer.Copyright')}}

    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const currentYear = computed(() => new Date().getFullYear())
</script>

<style lang="scss" scoped>
.footer {
  height: 50px;
  background: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
  border-top: 1px solid #e6e6e6;
}
</style>
