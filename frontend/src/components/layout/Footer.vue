<template>
  <div class="footer">
    <div class="copyright">
      {{ t('Footer.CopyrightTop') }}<br>
      {{ t('Footer.CopyrightMiddle') }}<br>
      {{ t('Footer.CopyrightBottom') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const currentYear = computed(() => new Date().getFullYear())
</script>

<style lang="scss" scoped>
.footer {
  height: 60px;
  background: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
  border-top: 1px solid #e6e6e6;
}
.copyright {
  text-align: center;
}
</style>
