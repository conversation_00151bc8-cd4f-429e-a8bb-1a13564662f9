<template>
  <el-image :src="avatarUrl" :style="imageStyle" :fit="elFit" />
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useAuthImageUrl } from "@/utils/files";

const props = defineProps<{
  file?: { fileId?: string; fileName?: string };
  defaultUrl?: string;
  size?: number | string;
  fit?: "fill" | "contain" | "cover" | "none" | "scale-down";
  cycle?: boolean;
}>();

const fileId = computed(() => props.file?.fileId || "");
const fileName = computed(() => props.file?.fileName || "");
const blobUrl = useAuthImageUrl(fileId, fileName);
const avatarUrl = computed(() => blobUrl.value || props.defaultUrl);

const imageStyle = computed(() => {
  const size =
    typeof props.size === "number" ? `${props.size}px` : props.size || "100px";
  return {
    width: size,
    height: size,
    borderRadius: props.cycle ? "50%" : undefined,
  };
});

const elFit = computed(() => {
  const validFits = ["fill", "contain", "cover", "none", "scale-down"];
  return validFits.includes(props.fit as string) ? props.fit : undefined;
});
</script>
