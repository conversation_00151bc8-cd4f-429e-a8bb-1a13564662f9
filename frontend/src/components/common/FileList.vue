<template>
  <div>
    <div v-for="(file, idx) in files" :key="file.fileId" style="margin-bottom: 8px;">
      <template v-if="type === 'image'">
        <img :src="file.url" :alt="file.fileName" style="max-width: 120px; max-height: 120px; border:1px solid #eee;" />
        <span>{{ file.fileName }} ({{ formatSize(file.fileSize) }})</span>
      </template>
      <template v-else>
        <a :href="file.url" target="_blank">{{ file.fileName }}</a>
        <span>({{ formatSize(file.fileSize) }})</span>
      </template>
      <el-button type="text" @click="$emit('remove', idx)">移除</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FileInfo } from '@/dtos/common'
const props = defineProps<{ files: FileInfo[], type?: 'file' | 'image' }>()
function formatSize(size: number) {
  if (size > 1024 * 1024) return (size / 1024 / 1024).toFixed(2) + ' MB'
  if (size > 1024) return (size / 1024).toFixed(2) + ' KB'
  return size + ' B'
}
</script>
