<template>
  <el-link
    v-if="file != null && file.fileName && file.fileSize"
    underline
    @click.stop="download"
    :disabled="loading"
  >
    <el-text v-text="file.fileName + '(' + formatSize(file.fileSize) + ')'" />
  </el-link>
  <el-button
    v-if="
      !disabled && !loading && file != null && file.fileName && file.fileSize
    "
    size="small"
    circle
    @click.stop="handleDeleteFile"
  >
    <el-icon>
      <Delete />
    </el-icon
    >
  </el-button>
</template>

<script lang="ts">
import {defineComponent, ref, watch} from "vue";
import {getAuthDownloadUrl, getDownloadUrl, formatSize} from "@/utils/files";
import {Delete} from "@element-plus/icons-vue";
import {getDownloadToken} from "@/api/files";

interface FileInfo {
  fileId: string;
  fileName: string;
  fileSize: number;
  // ... 其他可能的属性
}

export default defineComponent({
  name: "FileDownloader",
  components: {},
  props: {
    file: {
      type: Object as PropType<FileInfo | null>,
      required: false,
      default: null,
    },
    auth: {
      type: Boolean,
      required: false,
      default: false,
    },
    disabled: {
      type: Boolean,
      required: false,
      default: true,
    },
  },

  emits: ["delete-file"],
  setup(props, {emit}) {
    // Create a reactive reference to the value
    const modelValue = ref(props.file);

    const loading = ref(false);

    function handleDeleteFile() {
      if (props.file != null) {
        emit("delete-file", props.file);
      } else {
        console.warn("Cannot delete file: file prop is null.");
      }
    }

    // Watch for external value changes
    watch(
        () => props.file,
        (newVal) => {
          modelValue.value = newVal;
        },
        {deep: true}
    );

    async function download() {
      if (props.file == null) {
        console.warn("Cannot download: file prop is null.");
        return;
      }

      loading.value = true;
      var downloadUrl = "";
      if (props.auth) {
        if (props.file.fileId) {
          var downloadToken = await getDownloadToken(props.file.fileId);

          downloadUrl = getAuthDownloadUrl(
              props.file.fileId,
              props.file.fileName,
              downloadToken.data
          );
        } else {
          console.warn("Cannot download: fileId is missing.");
          loading.value = false;
        }
      } else {
        if (props.file.fileId && props.file.fileName) {
          downloadUrl = getDownloadUrl(props.file.fileId, props.file.fileName);
        } else {
          console.warn("Cannot download: fileId or fileName is missing.");
          loading.value = false;
        }
      }

      if (downloadUrl) {
        const a = document.createElement("a");
        a.href = downloadUrl;
        if (props.file.fileName) {
          a.download = props.file.fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        } else {
          console.warn("Cannot download: fileName is missing for download attribute.");
        }
      } else {
        console.warn("Cannot download: downloadUrl is not generated.");
      }

      loading.value = false;
    }

    return {
      loading,
      download,
      formatSize,
      Delete,
      handleDeleteFile,
      getDownloadToken,
    };
  },
});
</script>
<style scoped>
.el-link .el-text {
  color: var(--el-color-primary) !important;
}

.el-link .el-text:hover {
  //text-decoration: underline;
}
</style>
