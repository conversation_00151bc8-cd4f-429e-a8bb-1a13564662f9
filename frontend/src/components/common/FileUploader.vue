<template>
  <div>
    <input
        type="file"
        :accept="accept"
        :multiple="multiple"
        @change="handleFileChange"
        ref="inputRef"
        style="display: none"
    />
    <el-button @click="triggerSelect" :loading="loading" v-if="!disabled">
      <el-text v-if="language == 'zh'">{{
          type === "image" ? "选择图片" : "选择文件"
        }}
      </el-text>
      <el-text v-else-if="language == 'en'">{{
          type === "image" ? "Choose image" : "Choose file"
        }}
      </el-text>
      <el-text v-else-if="language == 'both'">{{
          type === "image" ? "选择图片/Choose image" : "选择文件/Choose file"
        }}
      </el-text>
    </el-button>


    <el-text
        v-if="(language == 'zh'||language == 'both') && limit && accept && !disabled"
        type="info"
        size="small"
        style="display: flex"
    >
      上传文件最大不超过{{ formatSize(limit) }}。后缀名[{{ accept }}]
    </el-text
    >
    <el-text
        v-if="(language == 'en'||language == 'both') && limit && accept && !disabled"
        type="info"
        size="small"
        style="display: flex"
    >Upload file does not exceed the maximum {{ formatSize(limit) }}.file extensions[{{ accept }}]
    </el-text
    >

    <!-- <FileList :files="fileList" :type="type" @remove="removeFile" /> -->

    <!-- 上传进度显示区域 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress
          :percentage="uploadProgress"
          :format="percentageFormat"
          :status="uploadProgress === 100 ? 'success' : ''"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, watch, onMounted} from "vue";
import {uploadChunk, getFileTypeDefine} from "@/api/files";
// import FileList from "./FileList.vue";
import type {FileInfo, FileUploadResponseDto} from "@/dtos/common";
import type {AxiosResponse} from "axios";
import {formatSize} from "@/utils/files";

const props = defineProps<{
  multiple?: boolean;
  typeCode: string;
  language: string;
  disabled: false;
  type?: "file" | "image";
}>();
const emit = defineEmits(["update:files", "update:limit", "update:accept"]);

const chunkSize = ref<number>();
const accept = ref<string>();
const limit = ref<number>(0);

const fileList = ref<FileInfo[]>([]);
const inputRef = ref<HTMLInputElement>();

const loading = ref<boolean>(false);
const uploading = ref<boolean>(false);
const uploadProgress = ref<number>(0);
const currentFileName = ref<string>("");
const currentFileSize = ref<number>(0);

function triggerSelect() {
  inputRef.value?.click();
}

// 格式化百分比显示
function percentageFormat(percentage: number): string {
  return percentage === 100 ? "完成" : `${percentage}%`;
}

// 验证文件类型
function validateFileType(file: File): boolean {
  if (!accept.value) return true;

  const fileExtension = file.name
      .substring(file.name.lastIndexOf("."))
      .toLowerCase();
  const acceptedExtensions = accept.value
      .split(",")
      .map((ext) => ext.trim().toLowerCase());

  return acceptedExtensions.some((ext) => fileExtension.endsWith(ext));
}

// 验证文件大小
function validateFileSize(file: File): boolean {
  if (!limit.value) return true;
  return file.size <= limit.value;
}

async function uploadFileInChunks(file: File) {
  loading.value = true;
  uploading.value = true;
  uploadProgress.value = 0;
  currentFileName.value = file.name;
  currentFileSize.value = file.size;

  const CHUNK_SIZE = chunkSize.value ?? 1024 * 1024 * 8;

  try {
    const totalSize = file.size;
    const totalChunk = Math.ceil(totalSize / CHUNK_SIZE);
    let fileId = "";
    // let fileName = file.name;
    // let fileType = file.type;
    // let fileSize = file.size;
    // let url = "";
    for (let chunkIndex = 0; chunkIndex < totalChunk; chunkIndex++) {
      const start = chunkIndex * CHUNK_SIZE;
      const end = Math.min(start + CHUNK_SIZE, totalSize);
      const chunk = file.slice(start, end);
      const formData = new FormData();
      formData.append("file", chunk);
      formData.append("fileName", file.name);
      formData.append("fileTypeCode", props.typeCode || "");
      formData.append("totalSize", totalSize.toString());
      formData.append("totalChunk", totalChunk.toString());
      formData.append("chunkIndex", chunkIndex.toString());
      if (chunkIndex === 0) {
        // 首块不带 fileId
      } else {
        formData.append("fileId", fileId);
      }
      const res = (await uploadChunk(
          formData
      )) as AxiosResponse<FileUploadResponseDto>;
      const data = res.data;
      if (chunkIndex === 0) {
        fileId = data.fileInfo.key;
      }

      // 更新上传进度
      uploadProgress.value = Math.round(((chunkIndex + 1) / totalChunk) * 100);

      if (data.isCompleted) {
        loading.value = false;
        // 保持进度条显示一段时间后隐藏
        setTimeout(() => {
          uploading.value = false;
        }, 1000);
        return {
          fileId: data.fileInfo.key,
          fileName: data.fileInfo.fileName || file.name,
          fileSize: data.fileInfo.fileSize,
          fileType: data.fileInfo.fileTypeCode || file.type,
        };
      }
    }
  } catch (err) {
    // ElMessage.error("上传文件失败" + err);
    loading.value = false;
    uploading.value = false;
  }
  return null;
}

async function handleFileChange(e: Event) {
  const files = (e.target as HTMLInputElement).files;
  if (!files) return;

  for (const file of Array.from(files)) {
    // 验证文件类型
    if (!validateFileType(file)) {
      ElMessage.error(
          `文件"${file.name}"的类型不被支持，支持的类型: ${accept.value}
The file type of "${file.name}" is not supported. Supported types: ${accept.value}  `
      );
      continue;
    }

    // 验证文件大小
    if (!validateFileSize(file)) {
      var fileSize = formatSize(limit.value);

      ElMessage.error(`文件"${file.name}"超过最大限制 ${fileSize}
The file "${file.name}" exceeds the maximum size limit of ${fileSize}  `);
      continue;
    }

    const fileInfo = await uploadFileInChunks(file);
    if (fileInfo) {
      fileList.value.push(fileInfo);
    }
  }
  // 清空输入，确保可以重复选择相同文件
  if (inputRef.value) {
    inputRef.value.value = "";
  }
  emit("update:files", fileList.value);
}

async function loadFieldTypeDefine() {
  const rsp = await getFileTypeDefine(props.typeCode);
  var fileTypeDto = rsp.data;
  accept.value = fileTypeDto.extension;
  limit.value = Number(fileTypeDto.sizeLimit);
  chunkSize.value = Number(fileTypeDto.chunkLimit);

  // 冒泡出去
  emit("update:accept", accept.value);
  emit("update:limit", limit.value);
}

onMounted(async () => {
  await loadFieldTypeDefine();
});

// 监听props.typeCode变化，重新加载文件类型定义
watch(
    () => props.typeCode,
    async (newVal, oldVal) => {
      if (newVal !== oldVal) {
        await loadFieldTypeDefine();
      }
    }
);
</script>

<style scoped>
.upload-progress {
  margin-top: 16px;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.file-info {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}
</style>
