<template>
  <el-card class="search-card">
    <el-form :model="formData" ref="formRef" :inline="true">
      <template v-for="(item, index) in formItems" :key="index">
        <el-form-item :label="item.label" v-if="item.visible !== false" :prop="item.prop">
          <!-- 输入框 -->
          <el-input 
            v-if="item.type === 'input'" 
            v-model="formData[item.prop]" 
            :placeholder="item.placeholder" 
            clearable
          />
          
          <!-- 下拉框 -->
          <el-select 
            v-else-if="item.type === 'select'" 
            v-model="formData[item.prop]" 
            :placeholder="item.placeholder" 
            clearable
          >
            <el-option 
              v-for="option in item.options" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value"
            />
          </el-select>
          
          <!-- 日期选择器 -->
          <el-date-picker
            v-else-if="item.type === 'date'"
            v-model="formData[item.prop]"
            :type="item.dateType || 'date'"
            :placeholder="item.placeholder"
            :format="item.format"
            :value-format="item.valueFormat"
            clearable
          />

          <!-- 日期时间选择器 -->
          <el-date-picker
            v-else-if="item.type === 'datetime'"
            v-model="formData[item.prop]"
            type="datetime"
            :placeholder="item.placeholder"
            :format="item.format || 'YYYY-MM-DD HH:mm:ss'"
            :value-format="item.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
            :default-time="item.defaultTime"
            clearable
          />
          
          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="item.type === 'number'"
            v-model="formData[item.prop]"
            :placeholder="item.placeholder"
            :min="item.min"
            :max="item.max"
            :step="item.step"
            clearable
          />
          
          <!-- 默认为输入框 -->
          <el-input 
            v-else
            v-model="formData[item.prop]" 
            :placeholder="item.placeholder" 
            clearable
          />
        </el-form-item>
      </template>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <el-button v-if="!showMore && showMoreButton" @click="handleMore">
          <el-icon><ArrowDown /></el-icon>
          更多
        </el-button>
        <el-button v-if="showMore && showMoreButton" @click="handleHide">
          <el-icon><ArrowUp /></el-icon>
          隐藏
        </el-button>
        <slot name="buttons"></slot>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Search, Refresh,ArrowDown,ArrowUp } from '@element-plus/icons-vue'

// 定义表单项类型
export interface FormItem {
  type: 'input' | 'select' | 'date' | 'datetime' | 'number'
  label: string
  prop: string
  placeholder?: string
  options?: Array<{ label: string, value: any }>
  dateType?: 'date' | 'datetime' | 'week' | 'month' | 'year' | 'dates'
  format?: string
  valueFormat?: string
  defaultTime?: string // 日期时间选择器的默认时间
  min?: number
  max?: number
  step?: number
  visible?: boolean // 是否显示该表单项
}
const showMore = ref(false)
// 定义组件 props
const props = defineProps({
  formItems: {
    type: Array as () => FormItem[],
    required: true
  },
  initialValues: {
    type: Object,
    default: () => ({})
  },
  showMoreButton: {
    type: Boolean,
    default: false
  }
})

// 定义组件事件
const emit = defineEmits(['search', 'reset'])

// 表单数据
const formData = reactive<Record<string, any>>({})
// 记录每个表单项初始的 visible 状态
const initialVisibleMap = new Map<string, boolean | undefined>();

// 初始化表单数据
const initFormData = () => {
  // 清空现有数据
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
  
  props.formItems.forEach(item => {
    if (item.visible === undefined) item.visible = true;
    // 记录初始 visible 状态
    if (!initialVisibleMap.has(item.prop)) {
      initialVisibleMap.set(item.prop, item.visible);
    }
    const initialValue = props.initialValues[item.prop]
    formData[item.prop] = initialValue !== undefined ? initialValue : null
  })
}

// 初始化表单数据
initFormData()

// 表单引用
const formRef = ref()

// 监听初始值变化
watch(() => props.initialValues, () => {
  initFormData()
}, { deep: true })

// 监听表单项变化
watch(() => props.formItems, () => {
  initFormData()
}, { deep: true })

// 搜索处理
const handleSearch = () => {
  emit('search', { ...formData })
}

// 重置处理
const handleReset = () => {
  debugger;
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置为初始值
  initFormData()
  
  emit('reset', { ...formData })
}
// 更多处理
const handleMore = () => {
  // 展开更多表单项
  props.formItems.forEach(item => {
    item.visible = true;
  })
   showMore.value = true
}
// 隐藏处理
const handleHide = () => {
  // 收起更多表单项
  props.formItems.forEach(item => {
    if (initialVisibleMap.get(item.prop) !== true) {
      item.visible = false;
    }
  })
  showMore.value = false
}
</script>
