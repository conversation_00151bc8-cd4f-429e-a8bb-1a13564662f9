<template>
  <!-- 语言切换 -->
  <el-dropdown class="language-container" trigger="click">
    <div class="language-wrapper">
      <i class="el-icon-language"></i>
      <span class="language-text">{{ currentLanguage }}</span>
      <el-icon><caret-bottom /></el-icon>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item @click="changeLanguage('zh-CN')">中文</el-dropdown-item>
        <el-dropdown-item @click="changeLanguage('en-US')"
        >English</el-dropdown-item
        >
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";

const { locale } = useI18n();


// 当前语言
const currentLanguage = computed(() => {
  return locale.value === "zh-CN" ? "中文" : "English";
});

// 切换语言
const changeLanguage = (lang: string) => {
  locale.value = lang;
  localStorage.setItem("language", lang);
};
</script>

<style lang="scss" scoped>
.international {
  cursor: pointer;
  vertical-align: middle;
}
</style>
