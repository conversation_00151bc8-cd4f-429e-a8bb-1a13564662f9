<template>
  <el-config-provider :locale="elementLocale">
    <el-date-picker
      v-model="modelValue"
      type="datetime"
      :placeholder="placeholder"
      :format="format || 'YYYY-MM-DD HH:mm:ss'"
      :value-format="valueFormat || 'YYYY-MM-DD HH:mm:ss'"
      :default-time="defaultTime"
      :clearable="clearable"
      :editable="editable"
      :disabled="disabled"
      :readonly="readonly"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
    />
  </el-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useElementLocale } from '@/composables/useElementLocale'

// 定义组件 props
const props = defineProps({
  modelValue: {
    type: [String, Date],
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择日期时间'
  },
  format: {
    type: String,
    default: 'YYYY-MM-DD HH:mm:ss'
  },
  valueFormat: {
    type: String,
    default: 'YYYY-MM-DD HH:mm:ss'
  },
  defaultTime: {
    type: String,
    default: '00:00:00'
  },
  clearable: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  // 自定义语言设置
  locale: {
    type: String,
    default: ''
  }
})

// 定义组件事件
const emit = defineEmits(['update:modelValue', 'change', 'blur', 'focus'])

// 使用 Element Plus 国际化
const { elementLocale } = useElementLocale(props.locale || undefined)

// 计算属性：双向绑定
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 事件处理
const handleChange = (value: any) => {
  emit('change', value)
}

const handleBlur = (event: Event) => {
  emit('blur', event)
}

const handleFocus = (event: Event) => {
  emit('focus', event)
}
</script>

<style lang="scss" scoped>
// 可以在这里添加自定义样式
</style>
