<template>
  <div class="date-property-editor">
    <el-form-item label="日期格式">
      <el-input
          v-model="format"
          placeholder="例如: yyyy/M/d"
          @input="onChange"
      />
    </el-form-item>

    <el-form-item label="最小值">
      <el-input
          v-model="min"
          placeholder=""
          @input="onChange"
      />
    </el-form-item>
    <el-form-item label="最大值">
      <el-input
          v-model="max"
          placeholder=""
          @input="onChange"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import {ref, watch, onMounted} from "vue";
import {ElFormItem, ElInput} from "element-plus";

const props = defineProps({
  field: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["change"]);

// 确保 extends 对象存在
if (!props.field.extends) {
  props.field.extends = {};
}

// 本地响应式数据
const format = ref(props.field.extends.format);
const min = ref(props.field.extends.min || null);
const max = ref(props.field.extends.max || null);

// 监听属性变化
watch(format, (newVal) => {
  props.field.extends.format = newVal;
  onChange();
});
watch(min, (newVal) => {
  props.field.extends.min = newVal;
  onChange();
});
watch(max, (newVal) => {
  props.field.extends.max = newVal;
  onChange();
});

// 初始化
onMounted(() => {
});

// 当字段属性变更时触发
function onChange() {
  emit("change", props.field);
}
</script>

<style scoped>
.date-property-editor {
  /* 日期区间字段属性编辑器样式 */
}
</style>
