<template>
  <div class="number-property-editor">
    <el-form-item label="最小">
      <el-input-number
        v-model="min"
        @change="onChange"
      />
    </el-form-item>
    <el-form-item label="最大">
      <el-input-number
        v-model="max"
        @change="onChange"
      />
    </el-form-item>
    <el-form-item label="精度">
      <el-input-number
        v-model="precision"
        :min="0"
        :max="10"
        @change="onChange"
      />
    </el-form-item>
    <el-form-item label="步长">
      <el-input-number
        v-model="step"
        :min="0.01"
        :step="0.01"
        @change="onChange"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { ElFormItem, ElInputNumber } from 'element-plus';

const props = defineProps({
  field: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['change']);

// 确保 extends 对象存在
if (!props.field.extends) {
  props.field.extends = {};
}

// 本地响应式数据
const precision = ref(props.field.extends.precision);
const step = ref(props.field.extends.step);
const min = ref(props.field.extends.min);
const max = ref(props.field.extends.max);

// 监听属性变化
watch(precision, (newVal) => {
  props.field.extends.precision = newVal;
  onChange();
});

watch(step, (newVal) => {
  props.field.extends.step = newVal;
  onChange();
});
watch(min, (newVal) => {
  props.field.extends.min = newVal;
  onChange();
});
watch(max, (newVal) => {
  props.field.extends.max = newVal;
  onChange();
});

// 初始化
onMounted(() => {
});

// 当字段属性变更时触发
function onChange() {
  emit('change', props.field);
}
</script>

<style scoped>
.number-property-editor {
  /* 数字字段属性编辑器样式 */
}
</style>
