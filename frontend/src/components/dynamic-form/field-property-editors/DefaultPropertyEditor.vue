<template>
  <!-- 默认属性编辑器不显示任何特殊属性 -->
  <div class="default-property-editor">
    <!-- 这里可以放置所有字段类型通用的属性编辑器 -->
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  field: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['change']);

// 当字段属性变更时触发
function onChange() {
  emit('change', props.field);
}
</script>

<style scoped>
.default-property-editor {
  /* 默认样式 */
}
</style>
