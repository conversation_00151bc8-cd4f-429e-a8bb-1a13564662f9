<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div>
    <BaseField
      :field="field"
      :value="value"
      :previousValue="previousValue"
      :language="language"
      :rowIndex="rowIndex"
      :formData="formData"
      :annotation="annotation"
      :renderMode="renderMode"
      :index="index"
      :total="total"
      :inSubform="inSubform"
      default-label="多选框"
      @edit-field="(...args) => $emit('edit-field', ...args)"
      @delete-field="(...args) => $emit('delete-field', ...args)"
      @move-down="(...args) => $emit('move-down', ...args)"
      @move-up="(...args) => $emit('move-up', ...args)"
    >
      <template #edit="{ readonly }">
        <el-checkbox-group
          v-model="modelValue"
          :disabled="readonly"
          style="flex: 1"
        >
          <el-checkbox
            v-for="opt in field.options || []"
            :key="opt.value"
            :label="opt.value"
          >
            {{ opt.labelZh || opt.value }}/{{ opt.labelEn }}
          </el-checkbox>
        </el-checkbox-group>
        <div class="field-content" v-if="annotation?.fill">
          <el-text type="primary">
            ⬆️AI提取内容：{{ annotation?.fill }}
          </el-text>
        </div>
      </template>
      <template #view>
        <div class="form-view-value">
          {{ getDisplay(modelValue, field) }}
        </div>
      </template>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import FieldItem from "@/views/form-management/form-list/components/FieldItem.vue";

export type RenderMode = "design" | "edit" | "view" | "approval";
import { ElCheckboxGroup, ElCheckbox } from "element-plus";
import { BaseField } from "../index";
import { getDisplay } from "./display";
import { FieldDto } from "@/dtos/dynamic-form.dto";
import { ref, watch } from "vue";
import { validate as logicValidate } from "./logic"; // 你的校验逻辑

const props = defineProps<{
  field: FieldDto;
  value: (string | number)[];
  annotation?: Object;
  previousValue: (string | number)[];
  renderMode?: RenderMode;
  index: { type: Number },
  total: { type: Number },
  language?: "zh" | "en" | "both";
  rowIndex: number | any;
  formData: Record<string, any>;
  inSubform?: boolean;
}>();

const emit = defineEmits(["update:value"]);

// 创建本地响应式数据
const modelValue = ref<(string | number)[]>(props.value || []);
// 监听外部值变化
watch(
  () => props.value,
  (newVal) => {
    modelValue.value = newVal || [];
  },
  { deep: true }
);

// 监听本地值变化，向上传递
watch(
  () => modelValue.value,
  (newVal) => {
    emit("update:value", newVal);
  },
  { deep: true }
);
</script>

<style scoped>
/* 组件特定样式 */
</style>
