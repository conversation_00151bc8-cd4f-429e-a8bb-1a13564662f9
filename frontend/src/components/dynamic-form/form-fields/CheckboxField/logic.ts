export function validate(value: any, field: any, language: string) {
    if (value && !value.every((v: any) => field.options?.some((opt: any) => opt.value === v))) {
        return (language == 'both' || language == 'zh') ? "存在无效选项" : 'There are invalid options.';
    }
    if (field.required && (!value || value.length === 0)) {
        return (language == 'both' || language == 'zh') ? "必选" : "Required";
    }
    if (field.extends?.min != null && value.length < field.extends.min) {
        return (language == 'both' || language == 'zh') ? `最少选择${field.extends.min}项` : `At least select ${field.extends.min} items`;
    }
    if (field.extends?.max != null && value.length > field.extends.max) {
        return (language == 'both' || language == 'zh') ? `最多选择${field.extends.max}项` : `At most select ${field.extends.max} items`;
    }
    return '';
}