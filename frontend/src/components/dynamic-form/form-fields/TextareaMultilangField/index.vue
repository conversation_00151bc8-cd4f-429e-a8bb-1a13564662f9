<template>
  <div>
    <BaseField
      :field="field"
      :value="value"
      :previousValue="previousValue"
      :language="language"
      :rowIndex="rowIndex"
      :annotation="annotation"
      :formData="formData"
      :index="index"
      :total="total"
      :inSubform="inSubform"
      :renderMode="renderMode as RenderMode"
      default-label="多语言多行文本"
      @edit-field="(...args) => $emit('edit-field', ...args)"
      @delete-field="(...args) => $emit('delete-field', ...args)"
      @move-down="(...args) => $emit('move-down', ...args)"
      @move-up="(...args) => $emit('move-up', ...args)"
    >
      <template #edit="{ readonly }">
        <div class="multilang-textareas" v-if="renderMode !== 'view'">
          <div
            class="textarea-input"
            v-if="language === 'zh' || language == 'both'"
          >
            <el-input
              ref="zhInputRef"
              v-model="modelValue.zh"
              type="textarea"
              :rows="4"
              placeholder="中文"
              :disabled="readonly || field.readonly?.zh"
            />
            <!-- 魔术棒按钮放在 textarea-input 容器内 -->
            <button
              v-if="
                field.zhButtonIcon && typeof field.zhButtonClick === 'function'
              "
              type="button"
              class="magic-btn"
              :disabled="readonly || isButtonLoading"
              @click="handleButtonClick"
            >
              <el-icon v-if="isButtonLoading" class="is-loading"
                ><Loading
              /></el-icon>
              <span v-else>{{ field.zhButtonIcon }}</span>
            </button>
            <div class="field-content" v-if="annotation?.fill">
              <el-text type="primary">
                ⬆️AI提取内容：{{ annotation?.fill }}
              </el-text>
            </div>
          </div>
          <div
            class="textarea-input"
            v-if="language === 'en' || language == 'both'"
          >
            <el-input
              ref="enInputRef"
              v-model="modelValue.en"
              type="textarea"
              :rows="4"
              :placeholder="field.placeholder?.en ?? 'English'"
              :disabled="readonly || field.readonly?.en"
            />
          </div>
        </div>
      </template>
      <template #view>
        <div class="form-view-value">
          {{ getDisplay(modelValue, field as FieldDto) }}
        </div>
      </template>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import { BaseField, RenderMode } from "../index";
import { getDisplay } from "./display";
import { FieldDto } from "@/dtos/dynamic-form.dto";
import { Loading } from "@element-plus/icons-vue"; // 导入加载图标
import { ref, watch, defineExpose, onMounted, onUnmounted, getCurrentInstance } from "vue";
import { validate as logicValidate } from "./logic"; // 你的校验逻辑


interface MultilangValue {
  zh: string;
  en: string;
}

const props = defineProps({
  field: {
    type: Object as PropType<
      FieldDto & {
        zhButtonIcon?: string;
        zhButtonClick?: (field: any, rowIndex?: number) => Promise<void> | void;
        readonly?: {
          zh?: boolean;
          en?: boolean;
        };
        placeholder?: {
          zh?: string;
          en?: string;
        };
      }
    >,
    required: true,
  },
  inSubform: {
    type: Boolean,
    default: false,
  },
  index: { type: Number },
  total: { type: Number },
  value: {
    type: Object as () => MultilangValue,
    default: () => ({ zh: "", en: "" }),
  },
  rowIndex: {
    type: Number,
    required: false,
  },
  previousValue: {
    type: Object as () => MultilangValue,
    default: () => ({ zh: "", en: "" }),
  },
  annotation: { type: Object, required: false },
  renderMode: {
    type: String,
    default: "edit",
  },
  language: { type: String as () => "zh" | "en" | "both", default: "zh" },
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const emit = defineEmits(["update:value"]);

const modelValue = ref({ ...props.value });

// 新增：输入框ref
const enInputRef = ref();
const zhInputRef = ref();

// 新增：按钮加载状态
const isButtonLoading = ref(false);

// 新增：处理按钮点击事件
async function handleButtonClick() {
  // 确保 field.zhButtonClick 存在且是函数
  if (typeof props.field?.zhButtonClick === "function") {
    isButtonLoading.value = true;
    // 确保 field 和 readonly 属性存在
    if (props.field?.readonly) {
      props.field.readonly.zh = true;
    }
    try {
      // 传递正确的参数数量和类型
      await props.field.zhButtonClick(props.field, props.value, props.rowIndex);
    } catch (error) {
      console.error("按钮点击事件执行失败:", error);
      // 可以在这里添加错误提示
    } finally {
      isButtonLoading.value = false;
      // 确保 field 和 readonly 属性存在
      if (props.field?.readonly) {
        props.field.readonly.zh = false;
      }
    }
  }
}

onMounted(() => {
  if (props.field.code) {
    const inst = getCurrentInstance();
  }
});

onUnmounted(() => {
  if (props.field.code) {
  }
});

// 只同步外部 value 到本地
watch(
  () => props.value,
  (newVal) => {
    if (newVal?.zh !== modelValue.value.zh)
      modelValue.value.zh = newVal?.zh || "";
    if (newVal?.en !== modelValue.value.en)
      modelValue.value.en = newVal?.en || "";
  },
  { deep: true }
);

// 只要本地值变化就 emit
watch(
  modelValue,
  (val) => {
    emit("update:value", { ...val });
  },
  { deep: true }
);

</script>

<style scoped>
.multilang-textareas {
  gap: 12px;
  flex: 1;
  /* position: relative; /* 使内部按钮定位相对 */
}
.textarea-input {
  flex: 1;
  /* margin-top: 5px; */
  position: relative; /* 为内部的 magic-btn 提供定位上下文 */
}
/* 将 magic-btn 的样式移动到这里，并调整定位 */
:deep(.el-textarea__inner) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  padding-right: 30px; /* 为按钮留出空间 */
}

:deep(.el-textarea__inner:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-textarea__inner.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  box-shadow: 0 0 0 1px #e4e7ed inset;
  background-color: #f5f7fa;
}

/* magic-btn 样式，定位在 textarea-input 容器内 */
.magic-btn {
  position: absolute;
  right: 8px; /* 根据实际布局调整位置 */
  top: 8px; /* 调整到顶部对齐 */
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px; /* 根据 emoji 大小调整 */
  padding: 0;
  z-index: 10; /* 确保按钮在输入框上面 */
}

/* 按钮加载状态样式 */
.magic-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6; /* 可选：降低透明度 */
}

.magic-btn .el-icon.is-loading {
  margin-top: 5px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
