import { ENGLISH_PATTERN } from '../../../../utils/strings';
// 示例：多语言多行文本字段的校验逻辑

export function validate(value: any, field: any, language: string) {
    const errors: string[] = [];

    // 必填校验
    if (field.required) {
        if ((language === 'zh' || language === 'both') && (!value?.zh || value.zh.trim() === "")) {
            errors.push((language == 'both' || language == 'zh') ? "中文必填" : "Chinese is required");
        }
        if ((language === 'en' || language === 'both') && (!value?.en || value.en.trim() === "")) {
            errors.push((language == 'both' || language == 'zh') ? "英文必填" : "English is required");
        }
    }

    // 英文格式校验
    if ((language === 'en' || language === 'both') && value?.en && !ENGLISH_PATTERN.test(value.en)) {
        errors.push((language == 'both' || language == 'zh') ? "英文只能输入英文、数字和半角符号" : "English can only contain letters, numbers, and half-width symbols");
    }

    // minLength/maxLength 分开配置
    const minLengthZh = field.extends?.minLengthZh;
    const maxLengthZh = field.extends?.maxLengthZh;
    const minLengthEn = field.extends?.minLengthEn;
    const maxLengthEn = field.extends?.maxLengthEn;

    if ((language === 'zh' || language === 'both') && minLengthZh && value?.zh && value.zh.length < minLengthZh) {
        errors.push((language == 'both' || language == 'zh') ? `中文最少${minLengthZh}个字符` : `Chinese must be at least ${minLengthZh} characters`);
    }
    if ((language === 'zh' || language === 'both') && maxLengthZh && value?.zh && value.zh.length > maxLengthZh) {
        errors.push((language == 'both' || language == 'zh') ? `中文最多${maxLengthZh}个字符` : `Chinese can be at most ${maxLengthZh} characters`);
    }
    if ((language === 'en' || language === 'both') && minLengthEn && value?.en && value.en.length < minLengthEn) {
        errors.push((language == 'both' || language == 'zh') ? `英文最少${minLengthEn}个字符` : `English must be at least ${minLengthEn} characters`);
    }
    if ((language === 'en' || language === 'both') && maxLengthEn && value?.en && value.en.length > maxLengthEn) {
        errors.push((language == 'both' || language == 'zh') ? `英文最多${maxLengthEn}个字符` : `English can be at most ${maxLengthEn} characters`);
    }

    return errors.join("；");
} 