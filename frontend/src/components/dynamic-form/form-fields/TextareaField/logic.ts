// 示例：输入框字段的校验逻辑
export function validate(value: any, field: any, language: string) {
    if (field.required && (!value || value === '')) {
        return (language == 'both' || language == 'zh') ? '该字段为必填项' : 'This field is required';
    }
    if (field.extends?.minLength && value && value.length < field.extends.minLength) {
        return (language == 'both' || language == 'zh') ? `最少${field.extends.minLength}个字符` : 'At least ' + field.extends.minLength + ' characters required';
    }
    if (field.extends?.maxLength && value && value.length > field.extends.maxLength) {
        return (language == 'both' || language == 'zh') ? `最多${field.extends.maxLength}个字符` : 'At most ' + field.extends.maxLength + ' characters allowed';
    }
    if (field.extends?.pattern && value && !(new RegExp(field.extends.pattern)).test(value)) {
        return field.extends?.patternMsg[language == 'both' ? 'zh' : language] || '格式错误';
    }
    return '';
} 