<template>
  <div>
    <BaseField
      :field="field"
      :value="value"
      :previousValue="previousValue"
      :language="language"
      :formData="formData"
      :inSubform="inSubform"
      :rowIndex="rowIndex"
      :annotation="annotation"
      :renderMode="renderMode"
      :index="index"
      :total="total"
      default-label="文件"
      @edit-field="(...args) => $emit('edit-field', ...args)"
      @delete-field="(...args) => $emit('delete-field', ...args)"
      @move-down="(...args) => $emit('move-down', ...args)"
      @move-up="(...args) => $emit('move-up', ...args)"
    >
      <template #edit="{ readonly }">
        <FileUploader
            :type="'file'"
            :typeCode="field.extends.typeCode"
            :multiple="false"
            :language="language"
            :disabled="readonly"
            @update:files="onFileUpload"
            @update:accept="val => field.extends.fileAccept = val"
            @update:limit="val => field.extends.fileLimit = val"
        />
        <div class="form-view-value" v-if="modelValue != null">
          <FileDownloader
              :file="value"
              :auth="true"
              :disabled="readonly"
              @delete-file="onDeleteFile"
          />
        </div>
        <div class="field-content" v-if="annotation?.fill">
          <el-text type="primary"> ⬆️AI提取内容：{{ annotation?.fill }}</el-text>
        </div>
      </template>
      <template #view>
        <div class="form-view-value" v-if="modelValue != null">
          <FileDownloader
              :file="value"
              :auth="true"
              :disabled="true"
          />
        </div>
      </template>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import {BaseField, RenderMode} from "../index";
import FileDownloader from "@/components/common/FileDownloader.vue";
import { ref, watch, defineExpose, onMounted, onUnmounted, getCurrentInstance, inject } from "vue";
import { validate as logicValidate } from "./logic";
import {FieldDto} from "@/dtos/dynamic-form.dto";
import {getDisplay} from "@/components/dynamic-form/form-fields/FileField/display"; // 你的校验逻辑

const props = defineProps({
  field: {type: Object, required: true},
  value: {type: [String, Number, Object], default: ""},
  annotation: {type: Object, required: false},
  previousValue: {type: [String, Number, Object], default: ""},
  rowIndex: {
    type: Number,
    required: false,
  },
  index: { type: Number },
  total: { type: Number },
  inSubform: {
    type: Boolean,
    default: false,
  },
  renderMode: {
    type: String as () => RenderMode,
    default: "edit",
  },
  language: {type: String as () => "zh" | "en" | "both", default: "zh"},
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const emit = defineEmits(["update:value"]);

function onFileUpload(files: any[]) {
  emit(
      "update:value",
      files && files.length > 0 ? files[files.length - 1] : null
  );
}

function onDeleteFile(file: any) {
  emit("update:value", null);
}

// 创建本地响应式数据
const modelValue = ref(props.value);

// 监听外部值变化
watch(
    () => props.value,
    (newVal) => {
      if (modelValue.value !== newVal) {
        modelValue.value = newVal;
      }
    }
);

// 监听本地值变化，向上传递
watch(
    () => modelValue.value,
    (newVal) => {
      emit("update:value", newVal);
    }
);

</script>

<style scoped>
/* 自定义 el-input 样式 */
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-input__inner) {
  color: #333;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  box-shadow: 0 0 0 1px #e4e7ed inset;
  background-color: #f5f7fa;
}
</style>
