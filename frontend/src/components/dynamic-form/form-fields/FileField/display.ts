
import { FieldDto } from "@/dtos/dynamic-form.dto";
import { formatSize } from "@/utils/files"
export function getDisplay(value: any, field: FieldDto) {
    if (value && value != null && value.fileName && value.fileSize) {
        return `${value.fileName}(${formatSize(value.fileSize)})`;
    }
    return "--";
    // return `<a href='${getDownloadUrl(value.fileId, value.FileName)}'>${value.fileName}(${formatSize(value.fileSize)})</a>`
}