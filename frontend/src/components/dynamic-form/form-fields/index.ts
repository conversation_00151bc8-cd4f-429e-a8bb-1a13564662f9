// Import field components
import Input<PERSON>ield from './InputField/index'
import Textarea<PERSON>ield from './TextareaField/index'
import TextMultilangField from './TextMultilangField/index'
import TextareaMultilangField from './TextareaMultilangField/index'
import SelectField from './SelectField/index'
import CheckboxField from './CheckboxField/index'
import RadioField from './RadioField/index'
import DateField from './DateField/index'
import NumberField from './NumberField/index'
import UnitSelectField from './UnitSelectField/index'
import IntRangeField from './IntRangeField/index'
import DateRangeField from './DateRangeField/index'
import SubFormField from './SubFormField/index'
import MultiSubFormField from './MultiSubFormField/index'
import FileField from './FileField/index'

export const fieldTypeMap: Record<string, any> = {
  input: InputField,
  textarea: TextareaField,
  text_multilang: TextMultilangField,
  textarea_multilang: TextareaMultilangField,
  select: <PERSON>Field,
  checkbox: CheckboxField,
  radio: RadioField,
  date: DateField,
  number: NumberField,
  unit_select: UnitSelectField,
  int_range: IntRangeField,
  date_range: DateRangeField,
  subForm: SubFormField,
  multiSubForm: MultiSubFormField,
  file: FileField
}

// Export the base field components for direct use
export { default as BaseField, type RenderMode } from './BaseField.vue'
export { default as BaseSubFormField } from './BaseSubFormField.vue'