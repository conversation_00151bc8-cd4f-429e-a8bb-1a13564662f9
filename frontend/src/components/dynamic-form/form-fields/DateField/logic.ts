// 示例：日期字段的校验逻辑
export function validate(value: any, field: any, language: string) {
    if (value && isNaN(Date.parse(value))) {
        return (language == 'both' || language == 'zh') ? "请输入合法日期" : 'Please enter a valid date';
    }
    if (field.required && (!value || value === "")) {
        return (language == 'both' || language == 'zh') ? "必填" : "Required";
    }
    if (field.extends?.min && new Date(value) < new Date(field.extends.min)) {
        return (language == 'both' || language == 'zh') ? `不能早于${field.extends.min}` : 'Cannot be earlier than ' + field.extends.min;
    }
    if (field.extends?.max && new Date(value) > new Date(field.extends.max)) {
        return (language == 'both' || language == 'zh') ? `不能晚于${field.extends.max}` : 'Cannot be later than ' + field.extends.max;
    }
    return '';
} 