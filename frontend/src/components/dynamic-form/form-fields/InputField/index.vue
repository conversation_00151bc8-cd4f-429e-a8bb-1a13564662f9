<template>
  <div>
    <BaseField
      :field="field"
      :value="value"
      :previousValue="previousValue"
      :language="language"
      :formData="formData"
      :rowIndex="rowIndex"
      :annotation="annotation"
      :renderMode="renderMode"
      :inSubform="inSubform"
      :index="index"
      :total="total"
      default-label="单行文本"
      @edit-field="(...args) => $emit('edit-field', ...args)"
      @delete-field="(...args) => $emit('delete-field', ...args)"
      @move-down="(...args) => $emit('move-down', ...args)"
      @move-up="(...args) => $emit('move-up', ...args)"
    >
      <template #edit="{ readonly }">
        <el-input
            v-model="modelValue"
            :placeholder="placeholder"
            :disabled="readonly||field.initReadonly"
            clearable
            style="flex: 1"
        />
        <div class="field-content" v-if="annotation?.fill">
          <el-text type="primary"> ⬆️AI提取内容：{{ annotation?.fill }}</el-text>
        </div>
      </template>
      <template #view>
        <div class="form-view-value">
          {{ getDisplay(modelValue, field as FieldDto) }}
        </div>
      </template>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import {ElInput} from "element-plus";
import {BaseField, RenderMode} from "../index";
import {getDisplay} from "./display";
import {FieldDto} from "@/dtos/dynamic-form.dto";
import { ref, watch, defineExpose, onMounted, onUnmounted, getCurrentInstance, inject } from "vue";
import { validate as logicValidate } from "./logic"; // 你的校验逻辑

const props = defineProps({
  field: {type: Object, required: true},
  value: {type: [String, Number], default: ""},
  previousValue: {type: [String, Number], default: ""},
  annotation: {type: Object, required: false},
  renderMode: {
    type: String as () => RenderMode,
    default: "edit",
  },
  index: { type: Number },
  total: { type: Number },
  rowIndex: {
    type: Number,
    required: false,
  },
  inSubform: {
    type: Boolean,
    default: false,
  },
  language: {type: String as () => "zh" | "en" | "both", default: "zh"},
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const emit = defineEmits(["update:value"]);

// 创建本地响应式数据
const modelValue = ref(props.value);

const placeholder = computed(() => {
  if (props.language == "zh") {
    return "中文";
  } else if (props.language == "en") {
    return "English";
  } else {
    return "中文/English";
  }
});

// 监听外部值变化
watch(
    () => props.value,
    (newVal) => {
      if (modelValue.value !== newVal) {
        modelValue.value = newVal;
      }
    }
);

// 监听本地值变化，向上传递
watch(
    () => modelValue.value,
    (newVal) => {
      emit("update:value", newVal);
    }
);

</script>

<style scoped>
/* 自定义 el-input 样式 */
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-input__inner) {
  color: #333;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  box-shadow: 0 0 0 1px #e4e7ed inset;
  background-color: #f5f7fa;
}
</style>
