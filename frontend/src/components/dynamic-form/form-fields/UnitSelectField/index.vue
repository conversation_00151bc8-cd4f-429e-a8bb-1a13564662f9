<template>
  <div>
    <BaseField
      :field="field"
      :value="value"
      :previousValue="previousValue"
      :language="language"
      :annotation="annotation"
      :renderMode="renderMode"
      :rowIndex="rowIndex"
      :formData="formData"
      :inSubform="inSubform"
      :index="index"
      :total="total"
      default-label="单位选择"
      @edit-field="(...args) => $emit('edit-field', ...args)"
      @delete-field="(...args) => $emit('delete-field', ...args)"
      @move-down="(...args) => $emit('move-down', ...args)"
      @move-up="(...args) => $emit('move-up', ...args)"
    >
      <template #edit="{ readonly }">
        <el-input
          v-model="inputValue"
          :placeholder="valuePlaceholder"
          @input="updateValue"
          :disabled="readonly"
          style="flex: 1"
        >
          <template #append>
            <el-select
              v-model="unitValue"
              :placeholder="unitPlaceholder"
              :disabled="readonly"
              @change="updateValue"
              class="unit-select"
              popper-class="unit-select-dropdown"
              style="width: 120px"
            >
              <el-option
                v-for="opt in field.options || []"
                :key="opt.value"
                :label="language === 'both'
                  ? (opt.labelZh || opt.label || opt.value) + '/' + (opt.labelEn || opt.value)
                  : (language === 'zh'
                    ? (opt.labelZh || opt.label || opt.value)
                    : (opt.labelEn || opt.value))"
                :value="opt.value"
              />
            </el-select>
          </template>
        </el-input>
        <div class="field-content" v-if="annotation?.fill">
          <el-text type="primary">
            ⬆️AI提取内容：{{ annotation?.fill }}
          </el-text>
        </div>
      </template>
      <template #view>
        <div class="form-view-value">
          {{ getDisplay({ unit: unitValue, value: inputValue }, field as FieldDto) }}
        </div>
      </template>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import { ElInput, ElSelect, ElOption } from "element-plus";
import { BaseField, RenderMode } from "../index";
import { getDisplay } from "./display";
import { FieldDto } from "@/dtos/dynamic-form.dto";

import { ref, watch, defineExpose, onMounted, onUnmounted, getCurrentInstance, inject } from "vue";
import { validate as logicValidate } from "./logic"; // 你的校验逻辑


interface UnitSelectValue {
  value: string;
  unit: string;
}

const valuePlaceholder = computed(() => {
  if (props.language == "zh") {
    return "请输入";
  } else if (props.language == "en") {
    return "Please input";
  } else {
    return "请输入/Please input";
  }
});

const unitPlaceholder = computed(() => {
  if (props.language == "zh") {
    return "选择单位";
  } else if (props.language == "en") {
    return "Select unit";
  } else {
    return "选择单位/Select unit";
  }
});

const props = defineProps({
  field: { type: Object, required: true },
  value: {
    type: Object as () => { value: string | number; unit: string },
    default: () => ({ value: "", unit: "" }),
  },
  previousValue: {
    type: Object as () => { value: string | number; unit: string },
    default: () => ({ value: "", unit: "" }),
  },

  index: { type: Number },
  total: { type: Number },
  rowIndex: {
    type: Number,
    required: false,
  },
  inSubform: {
    type: Boolean,
    default: false,
  },
  annotation: { type: Object, required: false },
  renderMode: {
    type: String as () => RenderMode,
    default: "edit",
  },
  language: { type: String as () => "zh" | "en" | "both", default: "zh" },
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const emit = defineEmits(["update:value"]);

const inputValue = ref(props.value?.value || "");
const unitValue = ref(props.value?.unit || "");

// 监听外部值变化
watch(
  () => props.value,
  (newVal) => {
    inputValue.value = newVal?.value || "";
    unitValue.value = newVal?.unit || "";
  },
  { deep: true }
);

// 当值变化时更新父组件
function updateValue() {
  emit("update:value", {
    value: inputValue.value,
    unit: unitValue.value,
  });
}


</script>

<style scoped>
/* 组件特定样式 */
</style>
