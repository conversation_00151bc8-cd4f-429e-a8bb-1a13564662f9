<template>
  <div
    class="form-field-row"
    :class="{
      'design-mode': renderMode === 'design',
      'approval-mode':
        renderMode === 'approval' && field.fields?.length == 0 && !inSubform,
      'has-warning': field.annotations && field.annotations.approval,
    }"
  >
    <div
      v-if="
        renderMode === 'approval' && field.fields?.length == 0 && !inSubform"
      class="field-actions"
    >
      <el-button-group>
        <el-button
          :icon="Comment"
          circle
          size="small"
          @click.stop="onApprovalMark"
          title="批注"
        />
        <el-button
          v-if="
            field.annotations != null &&
              field.annotations.approval != null &&
              field.annotations.approval.length > 0
          "
          :icon="Remove"
          circle
          size="small"
          @click.stop="onRemove"
          title="取消"
        />
      </el-button-group>
    </div>
    <!-- 设计模式下显示操作按钮 -->
    <div v-if="renderMode === 'design'" class="field-actions">
      <el-button-group>
        <el-button
          :icon="ArrowUp"
          circle
          size="small"
          @click.stop="moveUp"
          title="上移"
        />
        <el-button
          :icon="ArrowDown"
          circle
          size="small"
          @click.stop="moveDown"
          title="下移"
        />
        <el-button
          :icon="Delete"
          type="danger"
          circle
          size="small"
          @click.stop="deleteField"
          title="删除"
        />
        <el-button
          :icon="Edit"
          type="primary"
          circle
          size="small"
          @click.stop="editField"
          title="编辑"
        />
      </el-button-group>
    </div>

    <!-- 标签区域 -->
    <label class="form-label" v-if="language === 'both'">
      <div v-text="field.labelZh || defaultLabel"></div>
      <div v-text="field.labelEn ? field.labelEn : ''"></div>

      <span v-if="field.required" class="required">*</span>
    </label>
    <label class="form-label" v-else-if="language === 'zh'">
      {{ field.labelZh || defaultLabel }}

      <span v-if="field.required" class="required">*</span>
    </label>
    <label class="form-label" v-else-if="language === 'en'">
      {{ field.labelEn || defaultLabel }}

      <span v-if="field.required" class="required">*</span>
    </label>
    <!-- 控件内容区域 -->
    <div class="field-content" style="display: inline-block;">

      <el-row :gutter="0">
        <el-col :span="field.extends&&field.extends.width?field.extends.width*2:12*2">
          <slot
            name="edit"
            v-if="
              renderMode === 'design' ||
                renderMode === 'edit' ||
                renderMode === 'add' ||
                renderMode === 'approval'
            "
            :readonly="renderMode === 'approval'"
          ></slot>
          <slot name="view" v-else-if="renderMode === 'view'"></slot>

          <el-text
            v-if="field.description&&field.description.tooltip &&
              (renderMode === 'design' ||
                renderMode === 'edit' ||
                renderMode === 'add') &&!field.description.tooltip.split" :type="field.description.tooltip.type"
            style="font-size:10px !important;">
            {{
              language === 'both'
                ? field.description.tooltip.zh + '/' + field.description.tooltip.en
                : (language === 'zh'
                  ? field.description.tooltip.zh
                  : field.description.tooltip.en)
            }}
          </el-text>
          <el-row
            v-if="field.description&&field.description.tooltip && (language=='both'||language=='zh') &&
              (renderMode === 'design' ||
                renderMode === 'edit' ||
                renderMode === 'add') &&field.description.tooltip.split"
          >
            <el-text :type="field.description.tooltip.type" style="font-size:10px !important;">
              {{ field.description.tooltip.zh }}
            </el-text>
          </el-row>
          <el-row
            v-if="field.description&&field.description.tooltip&& (language=='both'||language=='en') &&
              (renderMode === 'design' ||
                renderMode === 'edit' ||
                renderMode === 'add') &&field.description.tooltip.split"
          >
            <el-text :type="field.description.tooltip.type" style="font-size:10px !important;">
              {{ field.description.tooltip.en }}
            </el-text>
          </el-row>

          <div class="field-content">
            <el-text type="warning">
              {{ getDifferenceDisplay() }}
            </el-text>
          </div>
        </el-col>
        <el-col
          :span="24-(field.extends&&field.extends.width?field.extends.width*2:12*2)==0?24:24-(field.extends&&field.extends.width?field.extends.width*2:12*2)">


          <el-text
            v-if="field.description&&field.description.right &&
              (renderMode === 'design' ||
                renderMode === 'edit' ||
                renderMode === 'add') &&!field.description.right.split" :type="field.description.right.type"
            style="font-size:10px;">

            {{
              language === 'both'
                ? field.description.right.zh + '/' + field.description.right.en
                : (language === 'zh'
                  ? field.description.right.zh
                  : field.description.right.en)
            }}
          </el-text>

          <el-row
            v-if="field.description&&field.description.right && (language=='both'||language=='zh') &&
              (renderMode === 'design' ||
                renderMode === 'edit' ||
                renderMode === 'add') &&field.description.right.split"
          >
            <el-text :type="field.description.right.type" style="font-size:10px;">
              {{ field.description.right.zh }}
            </el-text>
          </el-row>
          <el-row
            v-if="field.description&&field.description.right && (language=='both'||language=='en') &&
              (renderMode === 'design' ||
                renderMode === 'edit' ||
                renderMode === 'add') &&field.description.right.split"
          >
            <el-text :type="field.description.right.type" style="font-size:10px;">
              {{ field.description.right.en }}
            </el-text>
          </el-row>
        </el-col>
      </el-row>
      <el-text
        v-if="rowIndex != null ? field.errorMsgMap && field.errorMsgMap[rowIndex] : field.errorMsg"
        type="danger"
        class="field-error-msg"
        style="margin-top: 4px;"
      >
        {{ rowIndex != null ? field.errorMsgMap[rowIndex] : field.errorMsg }}
      </el-text>
    </div>
  </div>
  <el-text
    type="danger"
    v-if="field.annotations != null && field.annotations.approval"
  >
    驳回原因：{{ field.annotations.approval }}
  </el-text>
  <!-- <el-text type="danger" v-if="annotation != null">
    翻译内容：
    {{
      inSubform && rowIndex
        ? field.annotations[rowIndex].translate
        : field.annotations.translate
    }}
  </el-text> -->
  <!-- 字段编辑对话框 -->
  <FieldEditDialog
    :visible="editDialogVisible"
    :field="editingField"
    @save="onSaveField"
    @cancel="editDialogVisible = false"
  />

  <FieldAnnotationDialog
    :visible="annotationDialogVisible"
    :field="annotationField"
    :annotation="annotations ?? {}"
    @save="onSaveAnnotationField"
    @cancel="annotationDialogVisible = false"
  />
</template>

<script lang="ts">
import {defineComponent, ref, watch, PropType, nextTick} from "vue";
import {
  ArrowUp,
  ArrowDown,
  Delete,
  Edit,
  Comment,
  Remove,
} from "@element-plus/icons-vue";
import FieldEditDialog from "../FieldEditDialog.vue";
import FieldAnnotationDialog from "../FieldAnnotationDialog.vue";
import {isEqual} from "lodash-es";
import {fieldTypeMap} from "./index";
import {FieldDto} from "@/dtos/dynamic-form.dto";
import {ElRow, progressProps} from "element-plus";
import {isArray} from "element-plus/es/utils";

// 定义渲染模式类型
export type RenderMode = "design" | "add" | "edit" | "view" | "approval";

// Define the base field interface
export interface BaseFieldProps {
  field: {
    id?: string;
    code?: string;
    required?: boolean;
    labelZh?: string;
    labelEn?: string;
    type?: string;
    options?: Array<{
      value: string | number;
      labelZh?: string;
      labelEn?: string;
      label?: string;
    }>;
    value?: any;
    previsouValue?: any;
    extends?: Record<string, any>;
    [key: string]: any;
  };
  value: any;
  annotation: any;
  rowIndex?: number;
  inSubform: Boolean;
  previousValue: any;
  defaultLabel?: string;
  renderMode?: RenderMode; // 添加渲染模式
  formData: Record<string, any>;
  index?: number; // 字段在数组中的索引，用于上下移动
  total?: number; // 字段数组的总长度，用于判断是否可以上下移动
}

export default defineComponent({
  name: "BaseField",
  components: {
    ElRow,
    FieldEditDialog,
    FieldAnnotationDialog,
  },
  props: {
    field: {
      type: Object as PropType<FieldDto>,
      required: true,
    },
    value: {
      type: null,
      required: true,
    },
    previousValue: {
      type: null,
      required: false,
    },

    annotation: {type: Object, required: false},
    inSubform: {
      type: Boolean,
      default: false,
    },
    defaultLabel: {
      type: String,
      default: "表单控件",
    },
    rowIndex: {type: Number, required: false},
    language: {type: String as () => "zh" | "en" | "both", default: "zh"},
    renderMode: {
      type: String as PropType<RenderMode>,
      default: "edit",
    },
    index: {
      type: Number,
    },
    total: {
      type: Number,
    },
    formData: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  },
  emits: ["update:value", "delete-field", "edit-field", "move-up", "move-down"],
  setup(props, {emit}) {
    // Create a reactive reference to the value
    const modelValue = ref(props.value);

    // 字段编辑对话框状态
    const editDialogVisible = ref(false);
    const annotationDialogVisible = ref(false);
    const editingField = ref<any>(null);
    const annotationField = ref<any>(null);
    const annotations = ref<any>(null);

    // Watch for external value changes
    watch(
        () => props.value,
        (newVal) => {
          modelValue.value = newVal;
        },
        {deep: true}
    );

    // Watch for internal value changes and emit updates
    watch(
        () => modelValue.value,
        (newVal) => {
          emit("update:value", newVal);
        },
        {deep: true}
    );


    // Utility function to update the value
    function updateValue(newVal: any) {
      emit("update:value", newVal);
    }

    // 删除字段
    function deleteField() {
      emit("delete-field", props.field.id);
    }

    // 编辑字段
    function editField() {
      // 创建字段的深拷贝，避免直接修改原始对象
      editingField.value = JSON.parse(JSON.stringify(props.field));
      editDialogVisible.value = true;
    }

    // 保存批注信息
    function onSaveAnnotationField(annotation: Record<string, string | any>) {
      // console.log(annotation);
      annotationDialogVisible.value = false;

      props.field.annotations = annotation;

      // console.log(props.field.annotations);

      // 使用 nextTick 确保对话框已关闭后再触发事件
      nextTick(() => {
        // 直接触发编辑字段事件，由父组件处理更新逻辑
        emit("edit-field", props.field);
      });
    }

    // 保存编辑后的字段
    function onSaveField(field: any) {
      console.log("[DEBUG] BaseField 保存字段，field:", field);

      // 先关闭编辑对话框，避免事件循环
      editDialogVisible.value = false;

      // 使用 nextTick 确保对话框已关闭后再触发事件
      nextTick(() => {
        // 直接触发编辑字段事件，由父组件处理更新逻辑
        emit("edit-field", field);
      });
    }

    // 审批批注
    function onApprovalMark() {
      annotationField.value = JSON.parse(JSON.stringify(props.field));
      annotations.value = JSON.parse(JSON.stringify(props.field.annotations));
      annotationDialogVisible.value = true;
    }

    async function onRemove() {
      await ElMessageBox.confirm("是否确认进行该操作？");
      props.field.annotations.approval = null;
      onSaveAnnotationField(props.field.annotations);
    }

    // 上移字段
    function moveUp() {
      if (typeof props.index === "number" && props.index > 0) {
        emit("move-up", {field: props.field, index: props.index});
      }
    }

    // 下移字段
    function moveDown() {
      if (
          typeof props.index === "number" &&
          props.total &&
          props.index < props.total - 1
      ) {
        emit("move-down", {field: props.field, index: props.index});
      }
    }

    function getDifferenceDisplay() {
      var result = "";
      if (
          props.previousValue != null &&
          !isEqual(props.previousValue, props.value) &&
          props.renderMode != "design"
      ) {
        if (fieldTypeMap[props.field.type].getDisplay) {
          result =
              "原值：" +
              fieldTypeMap[props.field.type].getDisplay(
                  props.previousValue,
                  props.field
              );
        }
      }
      return result;
    }

    return {
      modelValue,
      updateValue,
      deleteField,
      editField,
      moveUp,
      onApprovalMark,
      moveDown,
      editDialogVisible,
      annotationDialogVisible,
      onSaveAnnotationField,
      editingField,
      annotationField,
      onSaveField,
      ArrowUp,
      ArrowDown,
      Delete,
      getDifferenceDisplay,
      Edit,
      Comment,
      Remove,
      onRemove,
      annotations,
    };
  },
});
</script>

<style scoped>
.form-field-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4px;
  position: relative;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s;
}

.form-field-row.design-mode {
  border: 1px dashed transparent;
  padding: 8px;
  margin-bottom: 8px;
}

.form-field-row.design-mode:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

.form-field-row.approval-mode {
  border: 1px dashed transparent;
  padding: 8px;
  margin-bottom: 8px;
}

.form-field-row.approval-mode:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

.form-field-row.has-warning {
  border-color: var(--el-color-danger);
  background-color: rgba(64, 158, 255, 0.05);
}

.form-label {
  /* min-width: 180px;
  max-width: 250px; */
  width: 150px;
  margin-right: 8px;
  color: #333;
  padding-top: 8px;
  text-align: right;
  font-size: 12px;
}

.multi-subform-container .form-label {
  /* min-width: 180px;
  max-width: 250px; */
  width: 80px;
  margin-right: 8px;
  color: #333;
  padding-top: 8px;
  text-align: right;
  font-size: 12px;
}

.required {
  color: #f56c6c;
  margin-left: 2px;
}

.field-content {
  flex: 1;
  display: flex;
}

.field-content.readonly :deep(.el-input__wrapper),
.field-content.readonly :deep(.el-textarea__wrapper),
.field-content.readonly :deep(.el-select),
.field-content.readonly :deep(.el-checkbox),
.field-content.readonly :deep(.el-radio),
.field-content.readonly :deep(.el-date-editor) {
  pointer-events: none;
  opacity: 0.8;
}

.field-actions {
  position: absolute;
  right: 4px;
  top: 4px;
  z-index: 10;
  display: none;
}

.form-field-row.design-mode:hover > .field-actions {
  display: block;
}

.form-field-row.approval-mode:hover > .field-actions {
  display: block;
}

/* 导出这些样式，以便子组件可以使用 */
:export {
  fieldRowClass: form-field-row;
  fieldLabelClass: form-label;
  fieldContentClass: field-content;
  fieldActionsClass: field-actions;
}

.field-content .el-row .el-col:last-child {
  padding-left: 5px;
}

.field-error-msg {
  color: #f56c6c;
  font-size: 13px;
  margin-top: 4px;
}
</style>
