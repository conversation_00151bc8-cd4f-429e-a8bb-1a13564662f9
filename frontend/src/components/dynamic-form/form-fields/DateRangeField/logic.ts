export function validate(value: any, field: any, language: string) {
    if ((value.start && isNaN(Date.parse(value.start))) || (value.end && isNaN(Date.parse(value.end)))) {
        return (language == 'both' || language == 'zh') ? "请输入合法日期" : 'Please enter a valid date';
    }
    if (value.start && value.end && new Date(value.start) >= new Date(value.end)) {
        return (language == 'both' || language == 'zh') ? "开始时间必须早于结束时间" : 'Start time must be earlier than end time';
    }
    if (field.required && (!value.start || !value.end)) {
        return (language == 'both' || language == 'zh') ? "区间必填" : "Range is required";
    }
    if (field.extends?.min && new Date(value.start) < new Date(field.extends.min)) {
        return (language == 'both' || language == 'zh') ? `不能早于${field.extends.min}` : 'Cannot be earlier than ' + field.extends.min;
    }
    if (field.extends?.max && new Date(value.end) > new Date(field.extends.max)) {
        return (language == 'both' || language == 'zh') ? `不能晚于${field.extends.max}` : 'Cannot be later than ' + field.extends.max;
    }
    return '';
}