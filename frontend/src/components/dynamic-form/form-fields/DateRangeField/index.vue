<template>
  <div>
    <BaseField
        :field="field"
        :value="value"
        :previousValue="previousValue"
        :language="language"
        :inSubform="inSubform"
        :rowIndex="rowIndex"
        :annotation="annotation"
        :formData="formData"
        :index="index"
        :total="total"
        :renderMode="renderMode as RenderMode"
        default-label="日期区间"
        @edit-field="(...args) => $emit('edit-field', ...args)"
        @delete-field="(...args) => $emit('delete-field', ...args)"
        @move-down="(...args) => $emit('move-down', ...args)"
        @move-up="(...args) => $emit('move-up', ...args)"
    >
      <template #edit="{ readonly }">
        <div class="date-range-container">
          <el-config-provider :locale="elementLocale">
            <el-date-picker
                v-model="modelValue"
                type="daterange"
                :start-placeholder="startPlaceholder"
                :end-placeholder="endPlaceholder"
                :format="field.extends.format"
                :value-format="field.extends.format"
                :disabled="readonly"
                :clearable="true"
                style="width: 100%"
            />
          </el-config-provider>
        </div>
        <div class="field-content" v-if="annotation?.fill">
          <el-text type="primary"> ⬆️AI提取内容：{{ annotation?.fill }}</el-text>
        </div>
      </template>
      <template #view>
        <div class="form-view-value">
          {{
            getDisplay(
                {start: modelValue[0], end: modelValue[1]},
                field as FieldDto
            )
          }}
        </div>
      </template>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import {BaseField, RenderMode} from "../index";
import {getDisplay} from "./display";
import {FieldDto} from "@/dtos/dynamic-form.dto";
import {ref, watch, defineExpose, onMounted, onUnmounted, getCurrentInstance, inject, computed} from "vue";
import {validate as logicValidate} from "./logic";
import {useElementLocale} from "@/composables/useElementLocale"; // 你的校验逻辑

const props = defineProps({
  field: {type: Object, required: true},
  value: {
    type: Object as () => { start: string; end: string },
    default: () => ({start: "", end: ""}),
  },
  previousValue: {
    type: Object as () => { start: string; end: string },
    default: () => ({start: "", end: ""}),
  },
  annotation: {
    type: Object,
    required: false,
  },
  index: {type: Number},
  total: {type: Number},
  inSubform: {
    type: Boolean,
    default: false,
  },
  renderMode: {
    type: String,
    default: "edit",
  },
  rowIndex: {
    type: Number,
    required: false,
  },
  language: {type: String as () => "zh" | "en" | "both", default: "zh"},
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});


// 计算响应式的语言值
const currentLang = computed(() => props.language === 'en' ? 'en-US' : 'zh-CN')

// 使用 Element Plus 国际化
const {elementLocale} = useElementLocale(currentLang)

const startPlaceholder = computed(() => {
  if (props.language == "zh") {
    return "开始日期";
  } else if (props.language == "en") {
    return "start date";
  } else {
    return "开始日期/start date";
  }
});
const endPlaceholder = computed(() => {
  if (props.language == "zh") {
    return "结束日期";
  } else if (props.language == "en") {
    return "end date";
  } else {
    return "结束日期/end date";
  }
});

const emit = defineEmits(["update:value"]);

const modelValue = ref([props.value.start, props.value.end]);

// 只同步外部 value 到本地
watch(
    () => props.value,
    (newVal) => {
      if (
          newVal.start !== modelValue.value[0] ||
          newVal.end !== modelValue.value[1]
      ) {
        modelValue.value = [newVal.start, newVal.end];
      }
    },
    {deep: true}
);

// 只要本地值变化就 emit
watch(
    modelValue,
    (val) => {
      emit("update:value", {
        start: val[0] || "",
        end: val[1] || "",
      });
    },
    {deep: true}
);

</script>

<style scoped>
.date-range-container {
  flex: 1;
}

/* 自定义 el-date-picker 样式 */
:deep(.el-date-editor) {
  width: 100%;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  box-shadow: 0 0 0 1px #e4e7ed inset;
  background-color: #f5f7fa;
}
</style>
