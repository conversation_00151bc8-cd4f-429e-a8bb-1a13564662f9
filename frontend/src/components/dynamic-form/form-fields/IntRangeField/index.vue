<template>
  <div>
    <BaseField
      :field="field"
      :value="value"
      :previousValue="previousValue"
      :language="language"
      :formData="formData"
      :rowIndex="rowIndex"
      :annotation="annotation"
      :renderMode="renderMode"
      :inSubform="inSubform"
      :index="index"
      :total="total"
      default-label="整数区间"
      @edit-field="(...args) => $emit('edit-field', ...args)"
      @delete-field="(...args) => $emit('delete-field', ...args)"
      @move-down="(...args) => $emit('move-down', ...args)"
      @move-up="(...args) => $emit('move-up', ...args)"
    >
      <template #edit="{ readonly }">
        <div class="int-range-wrapper">
          <div class="int-range-content">
            <span class="range-icon">
              <el-icon><Document /></el-icon>
            </span>
            <el-input
              v-model="minValue"
              type="number"
              :placeholder="minPlaceholder"
              @change="updateValue"
              class="min-input"
              :disabled="readonly"
            />
            <span class="range-sep">-</span>
            <el-input
              v-model="maxValue"
              type="number"
              :placeholder="maxPlaceholder"
              @change="updateValue"
              class="max-input"
              :disabled="readonly"
            />
          </div>
        </div>
        <div class="field-content" v-if="annotation?.fill">
          <el-text type="primary"> ⬆️AI提取内容：{{ annotation?.fill }} </el-text>
        </div>
      </template>
      <template #view>
        <div class="form-view-value">
          {{ getDisplay({ min: minValue, max: maxValue }, field as FieldDto) }}
        </div>
      </template>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import { ElInput, ElIcon } from "element-plus";
import { Document } from "@element-plus/icons-vue";
import { BaseField, RenderMode } from "../index";
import { getDisplay } from "./display";
import { FieldDto } from "@/dtos/dynamic-form.dto";
import { ref, watch, defineExpose, onMounted, onUnmounted, getCurrentInstance } from "vue";
import { validate as logicValidate } from "./logic"; // 你的校验逻辑



interface IntRangeValue {
  min: string | number;
  max: string | number;
}

const props = defineProps({
  field: { type: Object, required: true },
  value: {
    type: Object as () => IntRangeValue,
    default: () => ({ min: "", max: "" }),
  },
  inSubform: {
    type: Boolean,
    default: false,
  },
  index: { type: Number },
  total: { type: Number },
  previousValue: {
    type: Object as () => IntRangeValue,
    default: () => ({ min: "", max: "" }),
  },
  annotation: { type: Object, required: false },

  rowIndex: {
    type: Number,
    required: false,
  },
  renderMode: {
    type: String as () => RenderMode,
    default: "edit",
  },
  language: { type: String as () => "zh" | "en" | "both", default: "zh" },
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const minPlaceholder = computed(() => {
  if (props.language == "zh") {
    return "最小值";
  } else if (props.language == "en") {
    return "Min value";
  } else {
    return "最小值/Min value";
  }
});
const maxPlaceholder = computed(() => {
  if (props.language == "zh") {
    return "最大值";
  } else if (props.language == "en") {
    return "Max value";
  } else {
    return "最大值/Max value";
  }
});

const emit = defineEmits(["update:value"]);

const minValue = ref(props.value?.min || "");
const maxValue = ref(props.value?.max || "");

// 监听外部值变化
watch(
  () => props.value,
  (newVal) => {
    minValue.value = newVal?.min || "";
    maxValue.value = newVal?.max || "";
  },
  { deep: true }
);

// 当值变化时更新父组件
function updateValue() {
  emit("update:value", {
    min: minValue.value,
    max: maxValue.value,
  });
}


</script>

<style scoped>
.int-range-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 0;
  box-sizing: border-box;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  /* background-color: #fff; */
  transition: border-color 0.2s;
  white-space: nowrap;
  padding: 0 4px;
}

.int-range-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: 32px;
  padding: 0 4px;
}

.range-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  margin-right: 4px;
  font-size: 16px;
  width: 24px;
}

.min-input,
.max-input {
  width: calc(50% - 20px);
}

.min-input :deep(.el-input__wrapper),
.max-input :deep(.el-input__wrapper) {
  box-shadow: none !important;
  padding: 0 8px;
  border: none;
  /* background: transparent; */
}

.min-input :deep(.el-input__inner),
.max-input :deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
  border: none;
  /* background: transparent; */
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.range-sep {
  margin: 0 4px;
  color: #909399;
  font-weight: normal;
  flex-shrink: 0;
  width: 12px;
  text-align: center;
}
</style>
