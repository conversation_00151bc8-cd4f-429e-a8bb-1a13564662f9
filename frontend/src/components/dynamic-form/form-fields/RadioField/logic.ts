// 示例：单选框字段的校验逻辑
export function validate(value: any, field: any, language: string) {
    if (value != null && value !== "" && !field.options?.some(opt => opt.value === value)) {
        return (language == 'both' || language == 'zh') ? "无效选项" : 'Invalid option';
    }
    if (field.required && (value == null || value === "")) {
        return (language == 'both' || language == 'zh') ? "必选" : "Required";
    }
    return '';
} 