<template>
  <div class="expression-editor">
    <LogicExprEditor
      v-model="expression"
      :all-fields="allFields"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import LogicExprEditor from './LogicExprEditor.vue';

const props = defineProps({
  modelValue: { type: Object, required: true },
  allFields: { type: Array, default: () => [] }
});

const emit = defineEmits(['update:modelValue']);

const expression = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
  }
});
</script>

<style scoped>
.expression-editor {
  min-height: 200px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background: #fafbfc;
}
</style>