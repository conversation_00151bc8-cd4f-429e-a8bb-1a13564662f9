@import './variables.scss';
@import './transition.scss';
@import './element-variables.scss';

html {
  height: 100%;
  box-sizing: border-box;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Helvetica, PingFang SC, Microsoft YaHei, Arial, sans-serif;
}
.horizontal-menu{
  height: 35px;
}
#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a,
a:focus,
a:hover {
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

// 横向菜单样式
.el-menu--horizontal {
  border-bottom: none !important;

  .el-menu-item, .el-sub-menu__title {
    height: 35px;
    line-height: 35px;
  }
}

// 横向菜单下拉菜单样式
.el-menu--popup {
  min-width: 150px;

  .el-menu-item {
    height: 35px;
    line-height: 35px;

    .el-icon {
      margin-right: 5px;
    }
  }
}

// 修复横向菜单下拉菜单和对话框的z-index
.el-popper,
.el-dialog,
.el-dialog__wrapper {
  z-index: 4000 !important;
}


.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.search-card .el-card__body{
  padding-bottom: 0px !important;
}
.pagination-container{
  margin-top: 15px !important;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
.app-container{
  padding: 20px 20px 0px 20px;
}
// 通用浮动操作按钮样式 - 支持原生触底反弹
.floating-actions {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  padding: 12px 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: auto; // 确保在页面底部

  // 响应式适配
  @media (max-width: 768px) {
    padding: 8px 16px;
    gap: 8px;

    .el-button {
      font-size: 12px;
      padding: 8px 12px;
    }
  }
}

// 使用sticky浮动按钮的页面容器
.page-with-sticky-actions {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .page-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

/* 统一表单项宽度 (排除登录页面) */
.app-container .el-form-item__content {
  .el-input,
  .el-select {
    width: 220px !important;
  }
}

/* 表单项在搜索表单中的宽度 */
.search-card {
  .el-form-item__content {
    .el-input,
    .el-select {
      width: 220px !important;
    }
  }
}

/* Transfer 组件样式 */
.el-transfer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .el-transfer__buttons {
    padding: 0 10px;
  }

  .el-transfer-panel {
    width: 40%;
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    .el-transfer-panel__header {
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;
      padding: 8px 12px;

      .el-checkbox {
        color: #606266;
      }
    }

    .el-transfer-panel__body {
      height: 300px;

      .el-checkbox-group {
        padding: 6px 0;
      }

      .el-checkbox {
        display: block;
        padding: 8px 15px;
        margin: 0;
        border-bottom: 1px solid #ebeef5;

        &:hover {
          background-color: #f5f7fa;
        }

        &.is-checked {
          background-color: #ecf5ff;
        }
      }
    }

    .el-transfer-panel__footer {
      border-top: 1px solid #dcdfe6;
      background-color: #f5f7fa;
      padding: 6px 12px;
      text-align: right;

      .el-button {
        padding: 8px 12px;
      }
    }
  }
}
.field-edit-dialog  {
  .el-dialog__body{
    /* 添加滚动条 */
    max-height: 500px;
    overflow-y: auto;
  }
}
.form-view-value{
  margin-top:4px;
  font-size: 16px;
}
/* 角色分配对话框样式 */
.role-assignment-dialog {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__header {
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 15px 20px;

    .el-dialog__title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-transfer {
    margin-top: 10px;

    .el-transfer-panel__header {
      background-color: #f5f7fa;

      .el-transfer-panel__header-title {
        font-weight: bold;
      }
    }

    .el-transfer-panel__body {
      .el-checkbox-group {
        .el-checkbox {
          height: auto;
          line-height: 1.5;
          padding: 10px 15px;

          .el-checkbox__label {
            font-size: 14px;
          }
        }
      }
    }

    .el-transfer__buttons {
      padding: 0 15px;

      button {
        padding: 8px 12px;
        margin: 5px 0;
      }
    }
  }

  .dialog-footer {
    margin-top: 20px;
    border-top: 1px solid #e4e7ed;
    padding-top: 15px;
  }

  .user-info {
    display: flex;
    align-items: center;

    .username {
      font-weight: bold;
      font-size: 15px;
      color: #303133;
    }

    .realname {
      margin-left: 5px;
      color: #606266;
    }
  }

  .role-description {
    margin-bottom: 10px;
    color: #606266;
    font-size: 14px;
  }

  .role-item {
    display: flex;
    flex-direction: column;
    padding: 4px 0;

    .role-name {
      font-weight: bold;
      color: #303133;
      line-height: 1.4;
    }

    .role-code {
      font-size: 12px;
      color: #909399;
      margin-top: 2px;
      line-height: 1.2;
    }
  }
}

/* 组织管理相关样式 */
.org-container {
  display: flex;
  height: calc(100vh - 180px);
  min-height: 500px;

  .org-tree-container {
    width: 280px;
    margin-right: 20px;
    border-right: 1px solid #ebeef5;
    padding-right: 20px;
    overflow: auto;

    .org-tree-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      margin-bottom: 10px;
      border-bottom: 1px solid #ebeef5;

      .org-tree-title {
        font-size: 16px;
        font-weight: bold;
      }
    }
  }

  .org-content {
    flex: 1;
    overflow: auto;
  }
}

/* 树形控件样式 */
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;

  .node-label {
    flex: 1;
  }

  .node-actions {
    display: none;
  }
}

.el-tree-node:hover .node-actions {
  display: inline-flex;
}

/* 组织状态标签样式 */
.org-status-tag {
  min-width: 60px;
  text-align: center;
}

/* 组织类型标签样式 */
.org-type-tag {
  min-width: 70px;
  text-align: center;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 岗位分配对话框样式 */
.position-assignment-dialog {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__header {
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 15px 20px;

    .el-dialog__title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
  }

  .position-dialog-content {
    padding: 0 20px;
  }

  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .user-label {
      font-weight: bold;
      margin-right: 10px;
      min-width: 40px;
    }

    .username {
      font-weight: bold;
      margin-right: 5px;
    }

    .realname {
      color: #666;
    }
  }

  .position-list {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .position-item {
      border: 1px solid #ebeef5;
      padding: 15px;
      border-radius: 4px;
      background-color: #f8f9fa;

      .position-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        flex-wrap: wrap;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .position-field {
        flex: 1;
        min-width: 200px;

        &.primary-field {
          flex: 0 0 auto;
          min-width: 120px;
          display: flex;
          align-items: center;
        }

        &.delete-field {
          flex: 0 0 auto;
          min-width: auto;
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }
      }
    }

    .add-position {
      margin-top: 10px;
    }
  }

  .el-select {
    width: 100%;
  }

  .el-date-editor {
    width: 100%;
  }

  .el-radio {
    margin-right: 0;
  }

  .full-width {
    width: 100%;
  }

  .delete-btn {
    margin-left: auto;
  }
}

/* 状态下拉框样式 */
.status-select-dropdown {
  .el-select-dropdown__item {
    &.selected {
      color: #409eff;
      font-weight: bold;
    }
  }
}

.el-alert.el-alert--error{
  border-left: 5px solid var(--el-color-error);
}
.el-alert.el-alert--warning{
  border-left: 5px solid var(--el-color-warning);
}
.el-alert.el-alert--success{
  border-left: 5px solid var(--el-color-success);
}

.el-alert.el-alert--info{
  border-left: 5px solid var(--el-color-info);
}