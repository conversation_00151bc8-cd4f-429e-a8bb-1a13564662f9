import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
// Element Plus 国际化
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import en from 'element-plus/dist/locale/en.mjs'
import App from './App.vue'
import router from './router'
import i18n from './locales'
import './styles/index.scss'
import { hasPermission } from './directives/permission'
import { initRouterProgress } from './utils/progress'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)

// 配置 Element Plus 国际化
const getElementLocale = () => {
  const currentLocale = i18n.global.locale.value
  return currentLocale === 'zh-CN' ? zhCn : en
}

app.use(ElementPlus, {
  locale: getElementLocale()
})
app.use(i18n)

// 注册自定义指令
app.directive('hasPermission', hasPermission)

// 初始化路由进度条
initRouterProgress()

app.mount('#app')