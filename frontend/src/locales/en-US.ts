export default {
  route: {
    dashboard: 'Dashboard',
    system: 'System',
    user: 'User',
    role: 'Role'
  },
  login: {
    title: 'Lo<PERSON>',
    username: '<PERSON><PERSON><PERSON>',
    password: 'Password',
    login: 'Login',
    captcha: 'Verification Code',
    rememberMe: 'Remember me',
    forgotPassword: 'Forgot Password?',
    register: 'User Register',
  },
  forgotPassword: {
    title: 'Forgot Password',
    username: '<PERSON>rna<PERSON>',
    verifyMethod: 'Verification Method',
    email: '<PERSON><PERSON>',
    phone: 'Phone',
    captcha: 'Verification Code',
    next: 'Next',
    back: 'Back to Login'
  },
  resetPassword: {
    title: 'Reset Password',
    verificationCode: 'Verification Code',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    submit: 'Submit',
    back: 'Back',
    success: 'Password reset successfully'
  },
  navbar: {
    dashboard: 'Dashboard',
    logout: 'Logout',
    profile: 'Profile',
    theme: 'Theme',
    size: 'Size'
  },
  system: {
    user: {
      title: 'User Management',
      username: '<PERSON><PERSON><PERSON>',
      nickname: 'Nickname',
      email: 'Email',
      mobile: 'Mobile',
      role: 'Role',
      status: 'Status',
      createTime: 'Create Time',
      operation: 'Operation',
      search: 'Search',
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      enable: 'Enable',
      disable: 'Disable',
      confirmDelete: 'Are you sure to delete this user?',
      form: {
        username: 'Please enter username',
        password: 'Please enter password',
        nickname: 'Please enter nickname',
        email: 'Please enter email',
        mobile: 'Please enter mobile',
        role: 'Please select role'
      }
    },
    role: {
      title: 'Role Management',
      name: 'Role Name',
      code: 'Role Code',
      status: 'Status',
      createTime: 'Create Time',
      operation: 'Operation',
      search: 'Search',
      add: 'Add',
      edit: 'Edit',
      permission: 'Permissions',
      delete: 'Delete',
      enable: 'Enable',
      disable: 'Disable',
      confirmDelete: 'Are you sure to delete this role?',
      form: {
        name: 'Please enter role name',
        code: 'Please enter role code'
      }
    }
  },
  error: {
    pageNotFound: 'Page not found',
    backToHome: 'Back to home',
    pageNoRight: 'Sorry, you do not have the permission to access this page.'
  },
  register: {
    title: 'Register',
    label: {
      username: 'Username',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      email: 'Email Address',
      realname: 'Your Name',
      gender: 'Gender',
      country: 'country',
      unit: 'Institution',
      contactaddress: 'Address',
      mobile: 'Mobile Phone',
      telephone: 'Landline',
      btnRegister: 'Register Now'
    },
    placeholder: {
      username: 'Please enter your username',
      password: 'Please enter the user password',
      confirmPassword: 'Please enter the confirmation password',
      email: 'Please enter email',
      realname: 'Please enter your name',
      gender: 'Please select gender',
      country: 'Please select a country',
      unit: 'Please enter the name of the registered unit',
      contactaddress: 'Please enter the contact address',
      mobile: 'Please enter your phone number',
      telephone: 'Please enter a landline phone number',
    }
  },
  profile: {
    title: 'Personal Information',
    username: 'Username',
    realname: 'Real Name',
    email: 'Email',
    mobile: 'Mobile Number',
    gender: 'Gender',
    country: 'country',
    unit: 'Institution',
    contactaddress: 'Address',
    telephone: 'Landline',
    usertype: {
      label: 'User type',
      usertype1: 'Internal user',
      usertype2: 'External user',
      usertype3: 'System user',
    },
    status: 'Account Status',
    title2: 'Security Settings',
    accountPassword: 'Account Password',
    accountPasswordDesc: 'Regularly changing passwords can improve account security',
    accountPasswordBtn: 'Change Password',
    emailbind: 'Email Binding',
    emailNobindDesc: 'Email Not Bound',
    emailbindDesc: 'Email Bound',
    emailbindBtn: 'Verify Email',
    phonebind: 'Mobile Binding',
    phoneNobindDesc: 'Mobile Not Bound',
    phonebindDesc: 'Mobile Bound',
    phonebindBtn: 'Verify mobile phone',
    verificationcode: 'Verification Code',
    changepassword: {
      title: 'Change password',
      oldPassword: 'Current password',
      newPassword: 'New password',
      confirmNewPassword: 'Confirm new password',
      oldPasswordrequired: 'Please enter the current password',
      newPasswordrequired: 'Please enter the new password',
      confirmNewPasswordrequired: 'Please enter the new password again',
      passwordMismatch: 'The two passwords are inconsistent',
      passwordlength: 'The password length cannot be less than 6 characters',
    }
  },
  menu: {
    trialSearch: 'Trial search',
    dashboard: 'Dashboard',
    projectCenter: 'Project Center',
    projectUserAdd: 'Register a new project',
    projectApproval: 'Project Approval',
    projectSystemPendingJudgeList: 'Pending project for judgment',
    projectSystemPendingSendNumberList: 'Pending Number Project',
    projectSystemApplyEditList: 'Modify the application list again',
    projectSystemPendingReviewList: 'Revise Review Project Again',
    projectSystemReturnEditList: 'Modify the return list again',
    projectSystemApprovedList: 'Issued Project',
    projectSystemNonTraditionalList: 'Non Traditional Medicine Project',
    projectSystemAllSubmittedList: 'Audit Status Query',
    projectSystemPendingAssignList: 'Projects to be Allocated',
    projectSystemPendingReviewList2: 'Projects awaiting review',
    projectSystemReviewReturnedList2: 'Project Returned',
    projectSystemPendingApprovedList2: 'Approved project',
    projectSystemApprovedList2: 'Issued Project',
    projectSystemReAssignList: 'Reassign Project',
    projectSystemPendingAssignReviewList: 'Projects to be Allocated',
    projectSystemPendingReviewList3: 'Projects awaiting review',
    projectSystemPendingApprovedList3: 'Project has been reviewed and approved',
    projectSystemReviewReturnedList3: 'Project Returned',
    projectSystemApprovedList3: 'Issued Project',
    projectSystemPendingReviewList4: 'Projects awaiting initial review',
    projectSystemPendingApprovedList4: 'The project has passed the initial review',
    projectSystemReviewReturnedList4: 'Project Returned',
    projectSystemApprovedList4: 'Issued Project',
    system: 'System Management',
    systemLogging: 'Logging Management',
    systemUser: 'User Management',
    systemRole: 'Role Management',
    systemOrganization: 'Organizational Management',
    systemPosition: 'Job Management',
    systemPermission: 'Permission Management',
    files: 'Storage Management',
    filesStorage: 'Storage Management',
    filesFileType: 'File Type Management',
    formManagement: 'Form Management',
    formList: 'Form Definition',
    profileIndex: 'Personal Center',
    changePassword: 'Change password',
    projectUserAllList: 'My Project',
    projectUserPendingSubmit: 'Project to be submitted',
    projectUserPendingApproval: 'Project under review',
    projectUserApprovedList: 'Project approved',
  },
  Footer: {
    Copyright: 'ITMCTR BJ-ICP:07032215-5 Tip: IE8 is recommended Use the system with widescreen display resolution above'
  },
  cancel: 'Cancel',
  confirm: 'Confirm',
  success: 'Success',
  failed: 'Failed',
  normal: 'Normal',
  disabled: 'Disable',
  locking: 'Lock',
  enable: 'Enable',
  btnSave: 'Save Changes',
  btnReset: 'Reset',
}
