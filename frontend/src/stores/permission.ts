import { defineStore } from 'pinia'
import { baseRoutes, permissionRoutes } from '@/router'
import type { RouteRecordRaw } from 'vue-router'

/**
 * 权限管理Store
 * 负责管理路由权限和动态路由
 */
export const usePermissionStore = defineStore('permission', {
  state: () => ({
    // 所有路由
    routes: [] as RouteRecordRaw[],
    // 动态添加的路由
    addRoutes: [] as RouteRecordRaw[]
  }),

  actions: {
    /**
     * 生成路由
     *
     * 从用户权限数据生成路由配置
     * 注意：权限数据已经过权限过滤，不需要再次过滤
     *
     * @returns 可访问的路由
     */
    async generateRoutes() {
      console.log('permission store - generateRoutes')

      this.addRoutes = permissionRoutes;
      this.routes = [...baseRoutes, ...permissionRoutes]
      return permissionRoutes
    },

    /**
     * 重置状态
     * 用于用户退出登录时重置权限状态
     */
    $reset() {
      this.routes = []
      this.addRoutes = []
    }
  }
})