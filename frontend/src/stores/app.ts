import {defineStore} from 'pinia'

interface AppState {
    language: string
    sidebar: {
        opened: boolean
    }
}

export const useAppStore = defineStore('app', {
    state: (): AppState => ({
        language: localStorage.getItem('language') || 'zh-CN',
        sidebar: {
            opened: localStorage.getItem('sidebarStatus') ? !!+localStorage.getItem('sidebarStatus')! : true
        }
    }),

    actions: {
        getFirstTour(): boolean {
            const firstTour = localStorage.getItem('firstTour')
            if (firstTour === null) {
                localStorage.setItem('firstTour', 'true')
                return true
            }
            return localStorage.getItem('firstTour') === 'true';
        },
        setFirstTour() {
            localStorage.setItem('firstTour', 'false')
        },
        setLanguage(language: string) {
            this.language = language
            localStorage.setItem('language', language)
        },

        toggleSidebar() {
            this.sidebar.opened = !this.sidebar.opened
            localStorage.setItem('sidebarStatus', this.sidebar.opened ? '1' : '0')
        }
    }
}) 