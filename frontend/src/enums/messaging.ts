/**
 * 消息系统相关枚举
 */

/**
 * 消息类型枚举
 */
export enum MessageType {
  /** 短信 */
  SMS = 'sms',
  /** 邮件 */
  EMAIL = 'email'
}

/**
 * 消息状态枚举
 */
export enum MessageStatus {
  /** 待发送 */
  PENDING = 0,
  /** 发送中 */
  SENDING = 1,
  /** 发送成功 */
  SUCCESS = 2,
  /** 发送失败 */
  FAILED = 3
}

/**
 * 消息类型选项
 */
export const MESSAGE_TYPE_OPTIONS = [
  { label: '短信', value: MessageType.SMS },
  { label: '邮件', value: MessageType.EMAIL }
]

/**
 * 消息状态选项
 */
export const MESSAGE_STATUS_OPTIONS = [
  { label: '待发送', value: MessageStatus.PENDING },
  { label: '发送中', value: MessageStatus.SENDING },
  { label: '发送成功', value: MessageStatus.SUCCESS },
  { label: '发送失败', value: MessageStatus.FAILED }
]

/**
 * 消息状态标签类型映射
 */
export const MESSAGE_STATUS_TAG_TYPE = {
  [MessageStatus.PENDING]: 'info',
  [MessageStatus.SENDING]: 'warning',
  [MessageStatus.SUCCESS]: 'success',
  [MessageStatus.FAILED]: 'danger'
} as const

/**
 * 获取消息类型标签
 */
export function getMessageTypeLabel(type: string): string {
  const option = MESSAGE_TYPE_OPTIONS.find(item => item.value === type)
  return option?.label || type
}

/**
 * 获取消息状态标签
 */
export function getMessageStatusLabel(status: number): string {
  const option = MESSAGE_STATUS_OPTIONS.find(item => item.value === status)
  return option?.label || String(status)
}

/**
 * 获取消息状态标签类型
 */
export function getMessageStatusTagType(status: number): string {
  return MESSAGE_STATUS_TAG_TYPE[status as MessageStatus] || 'info'
}
