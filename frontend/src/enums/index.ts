
// 用户状态枚举
export enum UserStatus {
    Disabled = 0,
    Enabled = 1,
    Locked = 2
}

// 用户类型枚举
export enum UserType {
    Internal = 1,
    External = 2,
    System = 3
}

// 文件状态枚举
export enum FileStatus {
    Uploading = 0,
    Completed = 1,
    Deleted = 2
}

// 存储协议枚举
export enum StorageProtocol {
    Local = 1,
    Nas = 2,
    S3 = 3,
    Ftp = 4
}

// 权限类型枚举
export enum PermissionType {
    Directory = 1, // 目录
    Menu = 2,      // 菜单
    Button = 3,    // 按钮
    Api = 4        // API接口
}

// HTTP方法枚举
export enum HttpMethod {
    GET = 1,
    POST = 2,
    PUT = 3,
    DELETE = 4,
    PATCH = 5,
    HEAD = 6,
    OPTIONS = 7
}


// 通用状态枚举
export enum CommonStatus {
    Disabled = 0,
    Enabled = 1
}

// 角色类型枚举
export enum RoleType {
    General = 1, // 通用角色
    Position = 2, // 岗位角色
    System = 3    // 系统角色
}

// 组织机构状态枚举
export enum OrganizationStatus {
    Disabled = 0,
    Enabled = 1
}

// 组织机构类型枚举
export enum OrganizationType {
    Internal = 1,
    External = 2
}
// 职位状态枚举
export enum PositionStatus {
    Disabled = 0,
    Enabled = 1
}


// 表单实例状态枚举
export enum FormInstanceStatus {
    Draft = 1,       // 草稿
    Submitted = 2,   // 已提交
    Confirmed = 3,   // 已确认
    Cancelled = 4,   // 已取消
    Rejected = 5    // 已驳回
}

// 字段排序方向枚举
export enum FieldSortDirection {
    Ascending = 0,   // 升序
    Descending = 1   // 降序
}

export enum FormDataQueryOperator {
    Equal = 1,
    Empty = 2,
    NotEqual = 3,
    In = 4,
    NotEmpty = 5
}