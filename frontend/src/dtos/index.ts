import { CommonStatus, HttpMethod, PermissionType } from "@/enums"

// 权限DTO
export interface PermissionDto {
    key: string
    appId?: string
    appCode?: string
    parentId?: string
    code: string
    name: string
    type: PermissionType
    path?: string
    component?: string
    redirect?: string
    icon?: string
    method?: HttpMethod
    sortOrder: number
    status: CommonStatus
    description?: string
    children?: PermissionDto[]
}

/**
 * 权限树DTO
 */
export interface PermissionTreeDto {
    appId?: string;
    appCode: string;
    code: string;
    name: string;
    type: PermissionType;
    path?: string;
    component?: string;
    redirect?: string;
    icon?: string;
    method?: HttpMethod;
    sortOrder: number;
    status: CommonStatus;
    description?: string;
    children?: PermissionTreeDto[];
}

// API权限接口定义
export interface ApiEndpointInfo {
    applicationCode?: string;
    controllerName?: string;
    controllerDescription?: string;
    actionName?: string;
    actionDescription?: string;
    routeTemplate?: string;
    httpMethods?: string[];
    public: boolean;
    namedCode?: string;
}
// 应用信息接口
export interface AppInfo {
    appCode: string;
    appName: string;
    apiUrl: string;
    apiTarget: string;
}
export * from './rbac-mgt.dto';
export * from './rbac.dto';
export * from './common';
export * from './files-mgt.dto';
export * from './logging.dto';
