import {FormInstanceStatus} from "@/enums";
import {AnnotationValue} from "@/dtos/itmctr";


export interface FormRejectDto {
    description: string;
    annotationValues: Record<string, AnnotationValue | null>;
}

export interface SendNumberDto {
    prefix: string;
    year: number;
    number: string;
    description: string | null;
}

// 表单DTO
export interface FormDto {
    key: string; // 使用string类型避免精度丢失
    code: string; // 表单唯一编码
    name: string; // 表单名称
    version: string; // 表单版本
    description?: string; // 表单描述
    jsonConfig?: string; // 全局配置（json）
}

// 表单操作DTO
export interface FormOperationDto {
    code: string; // 表单唯一编码
    name: string; // 表单名称
    version: string; // 表单版本
    description?: string; // 表单描述
    jsonConfig?: string; // 全局配置（json）
}

// 表单字段DTO
export interface FormFieldDto {
    key: string; // 使用string类型避免精度丢失
    formId: string; // 表单ID
    parentId?: string; // 父字段ID
    groupCode?: string; // 分组编码
    code: string; // 字段唯一编码
    labelZh?: string; // 中文标签
    labelEn?: string; // 英文标签
    type: string; // 控件类型
    required: boolean; // 是否必填
    options?: string; // 选项（json）
    defaultValue?: string; // 默认值
    colspan: number; // 控件占用列数
    sortOrder: number; // 排序
    extraConfig?: string; // 额外配置（json）
}

// 表单字段操作DTO
export interface FormFieldOperationDto {
    formId: string; // 表单ID
    parentId?: string; // 父字段ID
    groupCode?: string; // 分组编码
    code: string; // 字段唯一编码
    labelZh?: string; // 中文标签
    labelEn?: string; // 英文标签
    type: string; // 控件类型
    required: boolean; // 是否必填
    options?: string; // 选项（json）
    defaultValue?: string; // 默认值
    colspan: number; // 控件占用列数
    sortOrder: number; // 排序
    extraConfig?: string; // 额外配置（json）
}

// 表单字段组DTO
export interface FormFieldGroupDto {
    key: string; // 使用string类型避免精度丢失
    formId: string; // 表单ID
    code: string; // 分组编码
    nameZh?: string; // 中文名称
    nameEn?: string; // 英文名称
    sortOrder: number; // 排序
}

// 表单字段组操作DTO
export interface FormFieldGroupOperationDto {
    formId: string; // 表单ID
    code: string; // 分组编码
    nameZh?: string; // 中文名称
    nameEn?: string; // 英文名称
    sortOrder: number; // 排序
}

// 表单查询参数
export interface FormQueryParams {
    $pageIndex: number;
    $pageSize: number;
    $sortBy?: string;
    $orderBy?: string;
    Code?: string;
    Name?: string;
    Version?: string;
}

// 表单字段查询参数
export interface FormFieldQueryParams {
    $pageIndex: number;
    $pageSize: number;
    $sortBy?: string;
    $orderBy?: string;
    FormId?: string;
    ParentId?: string;
    GroupCode?: string;
    Code?: string;
    Type?: string;
}

// 表单字段组查询参数
export interface FormFieldGroupQueryParams {
    $pageIndex: number;
    $pageSize: number;
    $sortBy?: string;
    $orderBy?: string;
    FormId?: string;
    Code?: string;
}

/**
 * 表单实例分页查询参数
 */
export interface FormInstancePageQueryParams {
    formCode?: string
    status?: FormInstanceStatus[]
    $pageIndex?: number
    $pageSize?: number
    $sortBy?: string
    $orderBy?: 'asc' | 'desc'
}


export interface AvailableRegistrationNumbersResponse {
    prefix: string
    year: string
    usedNumbers: number[]
    availableNumberRanges: { start: number; end: number }[]
}