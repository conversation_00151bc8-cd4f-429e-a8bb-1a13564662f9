// FormRecognitionContext
export interface FormRecognitionContext {
    ethics?: FileDto;
    protocol?: FileDto;
    consent?: FileDto;
}

export interface FileDto {
    fileId?: string;
    fileName?: string;
    fileType?: string;
    fileSize?: number;
}

// PreCheckResult
export interface PreCheckResult {
    taskId?: string;
    protocol: boolean;
    ethics: boolean;
    consent: boolean;
}

// FormCompareFieldDto
export interface FormCompareFieldDto {
    //字段编码
    fieldCode?: string;
    //字段中文名称
    fieldName?: string;
    //是否为嵌套字段（即是否为子表单）
    nested: boolean;
    //字段中的值范围 如果为非子表单 则只有一行记录 且Item中的rowIndex为null 如果为子表单 则会有多条记录 这两种字段情况在数据结构上没有差别，而标识nested的原因主要是因为页面中需要针对子表单情况做嵌套的行显示
    items?: FormCompareItemDto[];
}

export interface FormCompareItemDto {
    //数据行索引 如果为非子表单 该值为null
    rowIndex?: number;
    //用户输入
    userInput?: string;
    //是否匹配  "true","false","not_found"
    match?: string;
    //相似度分数 满分为1 显示时需要转为百分比
    score: number;
    //综合说明
    summary?: string;
    //源文件引用数量
    referenceCount: number;
    //源文件引用详情
    references?: FormCompareReferenceDto[];
}

export interface FormCompareReferenceDto {
    //固定对应的文件类型 Ethics=伦理委员会审批件 Protocol=研究方案 Consent=知情同意书
    fileCode?: string;
    //引用内容
    referenceContent?: string;
    //源文件引用位置 对应pdf文件中的距离上下左右的内边距
    areas?: FormCompareReferenceAreaDto[];
}

export interface FormCompareReferenceAreaDto {
    //第几页
    pageNumber: number;
    //距离pdf文件左边向内偏移
    x0: number;
    //距离pdf文件右边向内偏移
    x1: number;
    //距离pdf文件上边向内偏移
    y0: number;
    //距离pdf文件下边向内偏移
    y1: number;
}

// AnnotationValue
export interface AnnotationValue {
    // keyValue?: { [key: string]: string | null };
    // objectArray?: Array<{ [key: string]: string }>;
}

export interface JudgeProjectDto {
    userId?: number;
    traditionalProject?: boolean;
}


export interface AssignProjectDto {
    userId?: number;
    positionCode?: string;
    organizationCode?: string;
    description?: string;
}

export interface AssignReviewProjectDto {
    userId?: number;
}

export interface ApprovalHistoryDto {
    nodeType: string;
    action: string;
    description: string;
    operateTime: string;
    operatorId: number;
    operatorAccount: string;
    operatorName: string;
}

export interface IndexProjectCountRequestDto {
    formCode: string;
    // PositionCodes?: string[];
    // userId: string;
    status: number;
}

export interface TrialSearchPageQueryParams {
    formCode?: string
    $pageIndex?: number
    $pageSize?: number
    $sortBy?: string
    dynamicQueries?: Record<string, any>
    $orderBy?: 'asc' | 'desc'
}