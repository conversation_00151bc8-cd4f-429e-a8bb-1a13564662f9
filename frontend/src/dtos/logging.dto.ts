export interface ApplicationLogDto {
    /** 主键 */
    key: number;

    /** 应用名称 */
    application?: string;

    /** 日志分类 */
    category?: string;

    /** 日志级别 */
    level?: string;

    /** 客户端IP */
    clientIp?: string;

    /** 控制器 */
    controller?: string;

    /** 来源 */
    sourceContext?: string;

    /** 动作/方法 */
    action?: string;

    /** 路由模板 */
    routeTemplate?: string;

    /** 当前用户 */
    currentUser?: string;

    /** 关联ID/链路追踪ID */
    correlationId?: string;

    /** 日志时间 */
    timestamp: string;

    /** 日志内容 */
    message?: string;

    /** 异常堆栈 */
    exception?: string;
}

// 日志查询参数
export interface ApplicationLogQueryParams {
    $pageIndex?: number;
    $pageSize?: number;
    $sortBy?: string;
    $orderBy?: 'asc' | 'desc';
    application?: string;
    category?: string;
    level?: string;
    startTime?: string;
    endTime?: string;
    clientIp?: string;
    controller?: string;
    action?: string;
    currentUser?: string;
    correlationId?: string;
    message?: string;
    timestamp?: string[] | null;
}