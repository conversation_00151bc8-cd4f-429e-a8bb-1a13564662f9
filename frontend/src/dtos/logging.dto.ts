export interface ApplicationLogDto {
    /** 主键 */
    Key: Number;

    /** 应用名称 */
    Application: string;

    /** 日志分类 */
    Category?: string;

    /** 日志级别 */
    Level: string;

    /** 客户端IP */
    ClientIp?: string;

    /** 控制器 */
    Controller?: string;

    /** 来源 */
    SourceContext?: string;

    /** 动作/方法 */
    Action?: string;

    /** 路由模板 */
    RouteTemplate?: string;

    /** 当前用户 */
    CurrentUser?: string;

    /** 关联ID/链路追踪ID */
    CorrelationId?: string;

    /** 日志时间 */
    Timestamp: Date;

    /** 日志内容 */
    Message: string;

    /** 异常堆栈 */
    Exception?: string;

}