// 分页结果
export interface PageResult<T> {
  rows: T[]
  totals: number
}

// 上传响应
export interface UploadResult {
  fileId: string
  fileName: string
  fileSize: number
  fileType: string
  url?: string // 图片/文件下载地址
}

// 文件上传响应 DTO
export interface FileUploadResponseDto {
  uploadedChunks: number
  totalChunks: number
  uploadedSize: number
  isCompleted: boolean
  fileInfo: FileInfoDto
}

// 后端文件信息 DTO
export interface FileInfoDto {
  key: string
  fileName?: string
  fileSize: number
  chunkCount: number
  fileTypeCode?: string
  uploaderId?: number
  status: number // 0:Uploading, 1:Completed, 2:Deleted
}

// 前端展示用文件信息
export interface FileInfo {
  fileId: string
  fileName: string
  fileSize: number
  fileType: string
  url?: string
}
// 文件类型DTO
export interface FileTypeDto {
  key: string; // 使用string类型避免精度丢失
  typeCode?: string; // 类型编码
  typeName?: string; // 类型名称
  extension?: string; // 允许的文件后缀（如.jpg,.png)
  sizeLimit: number; // 大小限制（字节）
  chunkLimit: number; // 分块大小限制（字节）
  remark?: string; // 备注
  permissionRequired?: string; // 所需权限类型 none=无 user=用户 role=角色
}