import {
    CommonStatus,
    HttpMethod,
    OrganizationStatus,
    OrganizationType,
    PermissionType,
    PositionStatus,
    RoleType,
    UserStatus,
    UserType
} from "@/enums";

// 用户DTO
export interface UserDto {
    key: string;
    username: string;
    realName?: string;
    email?: string;
    mobile?: string;
    userType: UserType;
    status: UserStatus;
    avatar?: string;
    description?: string;
}

export interface OrganizationUserDto {
    organization: OrganizationDto;
    users: UserDto[];
}

// 用户操作DTO
export interface UserOperationDto {
    key?: string;
    username: string;
    realName?: string;
    email?: string;
    mobile?: string;
    avatar?: string;
    userType: UserType;
    status: UserStatus;
    description?: string;
    password: string;
    confirmPassword: string;
}

// 角色DTO
export interface RoleDto {
    key: string;
    name: string;
    code: string;
    type: RoleType;
    status: CommonStatus;
    description?: string;
}

// 角色操作DTO
export interface RoleOperationDto {
    key?: string;
    name: string;
    code: string;
    type: RoleType;
    status: CommonStatus;
    description?: string;
}

// 用户角色DTO
export interface UserRoleDto {
    key: string;
    userId: string;
    roleId: string;
    status: CommonStatus;
}

// 用户角色操作DTO
export interface UserRoleOperationDto {
    key?: string;
    userId: string;
    roleId: string;
    status: CommonStatus;
}

// 组织机构DTO
export interface OrganizationDto {
    key: string;
    parentId?: string;
    name: string;
    namePath: string;
    code: string;
    level: number;
    path: string;
    orgType: OrganizationType;
    status: CommonStatus;
    sortOrder: number;
    description?: string;
    children?: OrganizationDto[];
}

// 组织机构操作DTO
export interface OrganizationOperationDto {
    key?: string;
    parentId?: string;
    name: string;
    code: string;
    level: number;
    path: string;
    orgType: OrganizationType;
    status: CommonStatus;
    sortOrder: number;
    description?: string;
}

// 用户组织机构DTO
export interface UserOrganizationDto {
    key: string;
    userId: string;
    organizationId: string;
    status: CommonStatus;
    isPrimary: boolean;
}

// 用户组织机构操作DTO
export interface UserOrganizationOperationDto {
    key?: string;
    userId: string;
    organizationId: string;
    status: CommonStatus;
    isPrimary: boolean;
}

// 职位DTO
export interface PositionDto {
    key: string;
    name: string;
    code: string;
    roleId: string;
    status: PositionStatus;
    description?: string;
}

// 职位操作DTO
export interface PositionOperationDto {
    key?: string;
    name: string;
    code: string;
    roleId: string;
    status: PositionStatus;
    description?: string;
}

// 用户职位DTO
export interface UserPositionDto {
    key: string;
    userId: string;
    positionId: string;
    organizationId: string;
    isPrimary: boolean;
    status: CommonStatus;
    startTime?: Date;
    endTime?: Date;
}

// 用户职位操作DTO
export interface UserPositionOperationDto {
    key?: string;
    userId: string;
    positionId: string;
    organizationId: string;
    isPrimary: boolean;
    status: CommonStatus;
    startTime?: Date;
    endTime?: Date;
}

// 权限操作DTO
export interface PermissionOperationDto {
    key?: string;
    appId?: string;
    appCode?: string;
    parentId?: string;
    code: string;
    name: string;
    type: PermissionType;
    path?: string;
    component?: string;
    redirect?: string;
    icon?: string;
    method?: HttpMethod;
    sortOrder: number;
    status: CommonStatus;
    description?: string;
}

// 角色权限DTO
export interface RolePermissionDto {
    key: string;
    roleId: string;
    permissionId: string;
}

// 角色权限操作DTO
export interface RolePermissionOperationDto {
    key?: string;
    roleId: string;
    permissionId: string;
}


// 角色查询条件
export interface RoleQueryParams {
    $pageIndex?: number
    $pageSize?: number
    $sortBy?: string
    $orderBy?: 'asc' | 'desc'
    name?: string
    code?: string
    type?: RoleType
    status?: CommonStatus
}

// 用户角色查询条件
export interface UserRoleQueryParams {
    userId?: string
    roleId?: string
    $pageIndex?: number
    $pageSize?: number
    $sortBy?: string
    $orderBy?: 'asc' | 'desc'
}

// 用户查询条件
export interface UserQueryParams {
    Username?: string
    Email?: string
    Mobile?: string
    RealName?: string
    Status?: UserStatus | null
    $pageIndex?: number
    $pageSize?: number
    $sortBy?: string
    $orderBy?: 'asc' | 'desc'
}


// 组织机构查询条件
export interface OrganizationQueryParams {
    $pageIndex?: number
    $pageSize?: number
    $sortBy?: string
    $orderBy?: 'asc' | 'desc'
    name?: string
    code?: string
    status?: OrganizationStatus
    parentId?: string
}

// 职位查询条件
export interface PositionQueryParams {
    $pageIndex?: number
    $pageSize?: number
    $sortBy?: string
    $orderBy?: 'asc' | 'desc'
    name?: string
    code?: string
    status?: PositionStatus
}

// 权限查询条件
export interface PermissionQueryParams {
    $pageIndex?: number
    $pageSize?: number
    $sortBy?: string
    $orderBy?: 'asc' | 'desc'
    appCode?: string
    name?: string
    code?: string
    type?: PermissionType
    status?: CommonStatus
    parentId?: string
}
