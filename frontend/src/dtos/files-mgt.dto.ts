import { FileStatus, StorageProtocol } from "@/enums";

// 文件信息DTO
export interface FileInfoDto {
  key: string; // UUID
  fileName?: string; // 文件原始名称
  fileSize: string; // 文件总大小（字节）
  chunkCount: number; // 总计分块数量
  fileTypeCode?: string; // 文件类型code
  uploaderId?: string; // 上传用户ID
  status: FileStatus; // 文件状态
}

// 文件信息查询条件
export interface FileInfoQueryCriteria {
  // 可以根据需要添加查询条件
}



// 文件类型操作DTO
export interface FileTypeOperationDto {
  typeCode?: string; // 类型编码
  typeName?: string; // 类型名称
  extension?: string; // 允许的文件后缀（如.jpg,.png)
  sizeLimit: string; // 大小限制（字节）
  chunkLimit: string; // 分块大小限制（字节）
  remark?: string; // 备注
  permissionRequired?: string; // 所需权限类型 none=无 user=用户 role=角色
}

// 文件类型存储关系DTO
export interface FileTypeStorageRelDto {
  key: string; // 使用string类型避免精度丢失
  fileTypeCode?: string; // 文件类型code
  storageCode: string; // 存储信息code
  priority: number; // 优先级，数字越小优先级越高
}

// 文件类型存储关系操作DTO
export interface FileTypeStorageRelOperationDto {
  fileTypeCode?: string; // 文件类型code
  storageCode: string; // 存储信息code
  priority: number; // 优先级，数字越小优先级越高
}

// 存储DTO
export interface StorageDto {
  key: string; // 使用string类型避免精度丢失
  storageCode?: string; // 存储编码
  storageName?: string; // 存储名称
  endpoint?: string; // 存储服务地址
  protocol: StorageProtocol; // 存储协议
  accessKey?: string; // 访问key
  secretKey?: string; // 访问secret
  bucket?: string; // 桶/容器名
  remark?: string; // 备注
}

// 存储操作DTO
export interface StorageOperationDto {
  storageCode: string; // 存储编码
  storageName?: string; // 存储名称
  endpoint?: string; // 存储服务地址
  protocol: StorageProtocol; // 存储协议
  accessKey?: string; // 访问key
  secretKey?: string; // 访问secret
  bucket?: string; // 桶/容器名
  remark?: string; // 备注
}

// 查询参数
export interface StorageQueryParams {
  $pageIndex: number;
  $pageSize: number;
  $sortBy?: string;
  $orderBy?: string;
  Code?: string;
  Name?: string;
}

export interface FileTypeQueryParams {
  $pageIndex: number;
  $pageSize: number;
  $sortBy?: string;
  $orderBy?: string;
  Name?: string;
}

export interface FileTypeStorageRelQueryParams {
  $pageIndex: number;
  $pageSize: number;
  $sortBy?: string;
  $orderBy?: string;
  StorageCode?: string;
}