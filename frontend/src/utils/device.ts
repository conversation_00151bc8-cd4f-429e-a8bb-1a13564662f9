/**
 * 获取设备信息
 * @returns 设备信息对象
 */
export function getDeviceInfo() {
  const userAgent = navigator.userAgent;
  const platform = navigator.platform;
  const language = navigator.language;
  const screenWidth = window.screen.width;
  const screenHeight = window.screen.height;
  
  return {
    userAgent,
    platform,
    language,
    screenSize: `${screenWidth}x${screenHeight}`,
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
  };
}

/**
 * 获取设备ID
 * @returns 设备ID字符串
 */
export function getDeviceId() {
  // 从localStorage获取设备ID，如果不存在则创建一个新的
  let deviceId = localStorage.getItem('device_id');
  
  if (!deviceId) {
    // 生成一个简单的UUID
    deviceId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
    localStorage.setItem('device_id', deviceId);
  }
  
  return deviceId;
}

/**
 * 获取设备信息字符串
 * @returns 设备信息JSON字符串
 */
export function getDeviceInfoString() {
  return JSON.stringify(getDeviceInfo());
}
