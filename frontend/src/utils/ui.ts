
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

// 获取图标组件
const getIconComponent = (icon: string) => {
    // 处理 el-icon- 前缀
    if (icon.startsWith('el-icon-')) {
        const iconName = icon.replace('el-icon-', '');
        // 将 kebab-case 转换为 PascalCase
        const pascalCaseName = iconName
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('');

        // 返回对应的 Element Plus 图标组件
        return ElementPlusIconsVue[pascalCaseName as keyof typeof ElementPlusIconsVue] || 'Menu';
    }

    // 直接返回图标名称
    return icon;
};
export default getIconComponent