import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import router from '@/router'

// 配置进度条
NProgress.configure({ showSpinner: false })

// 初始化路由进度条
export function initRouterProgress() {
    router.beforeResolve((to, from, next) => {
        NProgress.start()
        next()
    })

    router.afterEach(() => {
        NProgress.done()
    })
}

// 手动控制进度条的方法
export const progress = {
    start: () => NProgress.start(),
    done: () => NProgress.done(),
    set: (n: number) => NProgress.set(n)
} 