import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * 将 ISO 字符串或 Date 对象格式化为 yyyy-MM-dd HH:mm:ss
 * @param input ISO字符串或Date对象
 * @returns 格式化后的字符串
 */
export function formatDateTime(input: string | Date | undefined | null): string {
  if (!input) return "";
  const date = typeof input === "string" ? new Date(input) : input;
  if (isNaN(date.getTime())) return "";
  const pad = (n: number) => n.toString().padStart(2, "0");
  return (
    date.getFullYear() +
    "-" +
    pad(date.getMonth() + 1) +
    "-" +
    pad(date.getDate()) +
    " " +
    pad(date.getHours()) +
    ":" +
    pad(date.getMinutes()) +
    ":" +
    pad(date.getSeconds())
  );
}

export function formatToLocal(input: string | Date | undefined | null, fmt = "YYYY-MM-DD HH:mm:ss") {
  if (!input) return "";
  return dayjs(input).tz(dayjs.tz.guess()).format(fmt);
}

/**
 * 将时间戳（秒或毫秒）格式化为本地时间字符串
 * @param timestamp 时间戳（number，支持秒或毫秒）
 * @param fmt 格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 本地时间字符串
 */
export function formatTimestampToLocal(timestamp: number | undefined | null, fmt = "YYYY-MM-DD HH:mm:ss") {
  if (!timestamp && timestamp !== 0) return "";
  // 判断是否为秒级时间戳（10位），自动转为毫秒
  const ts = timestamp < 1e12 ? timestamp * 1000 : timestamp;
  return dayjs(ts).tz(dayjs.tz.guess()).format(fmt);
}
export function formatTimestampToLocalString(timestamp: string | undefined | null, fmt = "YYYY-MM-DD HH:mm:ss") {
  if (!timestamp) return "";
  const num = Number(timestamp);
  if (isNaN(num)) return "";
  return formatTimestampToLocal(num, fmt);
}

export function formatDateString(input: string | Date | undefined | null, fmt = "YYYY-MM-DD") {
  if (!input) return "";
  return dayjs(input).format(fmt);
}
export function formatDateTimeString(input: string | Date | undefined | null, fmt = "YYYY-MM-DD HH:mm:ss") {
  if (!input) return "";
  return dayjs(input).format(fmt);
}