
import { FieldDto, FormDefinitionDto } from "@/dtos/dynamic-form.dto";
import { FormInstanceStatus } from "@/enums";
import { fieldTypeMap } from "@/components/dynamic-form/form-fields";

// 获取状态文本
export function getStatusText(status: FormInstanceStatus) {
    switch (status) {
        case FormInstanceStatus.Draft:
            return "草稿";
        case FormInstanceStatus.Submitted:
            return "已提交";
        case FormInstanceStatus.Confirmed:
            return "已确认";
        case FormInstanceStatus.Cancelled:
            return "已作废";
        case FormInstanceStatus.Rejected:
            return "已驳回";
        default:
            return "未知";
    }
};
// 获取状态标签类型
export function getStatusTagType(status: FormInstanceStatus) {
    switch (status) {
        case FormInstanceStatus.Draft:
            return "info";
        case FormInstanceStatus.Submitted:
            return "warning";
        case FormInstanceStatus.Confirmed:
            return "success";
        case FormInstanceStatus.Cancelled:
            return "danger";
        case FormInstanceStatus.Rejected:
            return "danger";
        default:
            return "info";
    }


};

// getDisplay方法
export function getDisplay(field: FieldDto, row: any, fieldCode: string): string {

    if (!field) return "";
    // 这里假设有fieldTypeMap
    return fieldTypeMap[field.type].getDisplay(row.value[fieldCode], field);
}

export function getDisplayByDefinition(formDefinition: FormDefinitionDto, row: any, fieldCode: string): string {
    if (!formDefinition || !formDefinition.groups) return "";
    const field = formDefinition.groups
        .map((q: any) => q.fields)
        .flat()
        .find((q: any) => q.code === fieldCode);
    if (!field) return "";

    return getDisplay(field, row, fieldCode);
}

export function getFormDataValue(formData: Record<string, string>, code: string) {

    if (!formData || !formData[code])
        return "";

    return formData[code];
}