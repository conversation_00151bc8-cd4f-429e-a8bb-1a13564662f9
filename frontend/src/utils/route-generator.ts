import { RouteRecordRaw } from 'vue-router'
// 使用函数导入Layout组件，避免循环依赖
const getLayout = () => import('@/components/layout/index.vue')
import { PermissionType } from '@/enums'
import { PermissionDto } from '@/dtos'

/**
 * 动态导入组件
 * @param component 组件路径
 * @returns 组件
 */
export function loadComponent(component: string) {
  // 如果是Layout组件，直接返回
  if (component === 'Layout') {
    return getLayout
  }

  try {
    // 移除 .vue 后缀
    const componentPath = component.replace(/\.vue$/, '')
    // 构建完整的导入路径，使用相对路径
    const fullPath = `../views/${componentPath.replace('views/', '')}.vue`
    
    // 使用动态导入
    return () => import(/* @vite-ignore */ fullPath)
      .catch(error => {
        console.error(`加载组件失败: ${fullPath}`, error)
        return import('../views/error/404.vue')
      })
  } catch (error) {
    console.error(`组件路径解析失败: ${component}`, error)
    return () => import('../views/error/404.vue')
  }
}

/**
 * 将后端返回的权限数据转换为路由配置
 *
 * 注意：后端返回的权限数据已经过权限过滤，只包含当前用户有权限访问的菜单
 * 前端不需要再次进行权限过滤
 *
 * @param permissions 权限数据（只包含目录和菜单类型）
 * @returns 路由配置
 */
export function generateRoutesFromPermissions(permissions: PermissionDto[]): RouteRecordRaw[] {
  const routes: RouteRecordRaw[] = []

  permissions.forEach(permission => {
    // 只处理目录和菜单类型
    if (permission.type !== PermissionType.Directory && permission.type !== PermissionType.Menu) {
      return
    }

    // 如果权限被禁用，则跳过
    if (permission.status === 0) {
      return
    }

    // 创建路由对象
    const route: any = {
      path: permission.path || '',
      name: permission.code,
      meta: {
        title: permission.name,
        icon: permission.icon,
        permissions: [permission.code]
      }
    }

    // 处理组件
    if (permission.component) {
      route.component = loadComponent(permission.component)
    }

    // 处理重定向
    if (permission.redirect) {
      route.redirect = permission.redirect
    }

    // 处理子菜单
    if (permission.children && permission.children.length > 0) {
      // 为子路由添加标记
      const childRoutes = generateRoutesFromPermissions(permission.children)

      // 标记子路由
      childRoutes.forEach(childRoute => {
        if (childRoute.meta) {
          childRoute.meta.isChildRoute = true
        } else {
          childRoute.meta = { isChildRoute: true }
        }
      })

      route.children = childRoutes
    }

    routes.push(route)
  })

  return routes
}

// 为了保持向后兼容，保留原函数名但调用新函数
export function generateRoutesFromMenus(menus: PermissionDto[]): RouteRecordRaw[] {
  return generateRoutesFromPermissions(menus)
}

/**
 * 格式化路由，确保一级路由使用Layout组件
 * @param routes 路由配置
 * @returns 格式化后的路由配置
 */
export function formatRoutes(routes: RouteRecordRaw[]): RouteRecordRaw[] {
  const formattedRoutes: RouteRecordRaw[] = []

  routes.forEach(route => {
    // 检查是否为一级路由（没有父路由）
    // 我们通过检查自定义属性 meta.isChildRoute 来判断
    const isTopLevelRoute = !(route.meta && route.meta.isChildRoute)

    // 如果是一级路由且没有使用Layout组件，则包装一层
    if (isTopLevelRoute && route.component !== getLayout) {
      const wrapperRoute: RouteRecordRaw = {
        path: route.path,
        component: getLayout,
        children: [{ ...route, path: '' }]
      }
      formattedRoutes.push(wrapperRoute)
    } else {
      formattedRoutes.push(route)
    }
  })

  return formattedRoutes
}
