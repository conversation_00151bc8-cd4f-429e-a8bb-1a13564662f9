const TokenKey = 'Admin-Token'
const RefreshTokenKey = 'Admin-Refresh-Token'
const Menus<PERSON><PERSON> = 'Admin-Menus'
const PermissionsKey = 'Admin-Permissions'
const PositionsKey = 'Admin-Positions'
const RolesKey = 'Admin-Roles'

export function getToken(): string {
  return localStorage.getItem(TokenKey) || ''
}

export function setToken(token: string) {
  return localStorage.setItem(TokenKey, token)
}

export function removeToken() {
  return localStorage.removeItem(TokenKey)
}

export function getRefreshToken() {
  return localStorage.getItem(RefreshTokenKey)
}

export function setRefreshToken(token: string) {
  return localStorage.setItem(RefreshTokenKey, token)
}

export function removeRefreshToken() {
  localStorage.removeItem(RefreshTokenKey)
}

export function getMenus() {
  const menusStr = localStorage.getItem(MenusKey)
  if (menusStr) {
    try {
      return JSON.parse(menusStr)
    } catch (error) {
      console.error('解析菜单数据失败:', error)
      return []
    }
  }
  return []
}

export function setMenus(menus: any[]) {
  return localStorage.setItem(MenusKey, JSON.stringify(menus))
}

export function removeMenus() {
  localStorage.removeItem(MenusKey)
}

export function getPermissions() {
  const permissionsStr = localStorage.getItem(PermissionsKey)
  if (permissionsStr) {
    try {
      return JSON.parse(permissionsStr)
    } catch (error) {
      console.error('解析权限数据失败:', error)
      return []
    }
  }
  return []
}

export function setPermissions(permissions: string[]) {
  return localStorage.setItem(PermissionsKey, JSON.stringify(permissions))
}

export function removePermissions() {
  localStorage.removeItem(PermissionsKey)
}

export function getPositions() {
  const positionsStr = localStorage.getItem(PositionsKey)
  // console.log('获取岗位数据:', positionsStr)
  if (positionsStr) {
    try {
      return JSON.parse(positionsStr)
    } catch (error) {
      console.error('解析岗位数据失败:', error)
      return []
    }
  }
  return []
}


export function getRoles() {
  const rolesStr = localStorage.getItem(RolesKey)
  if (rolesStr) {
    try {
      return JSON.parse(rolesStr)
    } catch (error) {
      console.error('解析角色数据失败:', error)
      return []
    }
  }
  return []
}


export function setPositions(positions: any[]) {
  console.log('设置岗位数据:', positions)
  return localStorage.setItem(PositionsKey, JSON.stringify(positions))
}

export function setRoles(roles: any[]) {
  console.log('设置角色数据:', roles)
  return localStorage.setItem(RolesKey, JSON.stringify(roles))
}

export function removePositions() {
  localStorage.removeItem(PositionsKey)
}

export function clearAuthData() {
  removeToken()
  removeRefreshToken()
  removeMenus()
  removePermissions()
  removePositions()
}