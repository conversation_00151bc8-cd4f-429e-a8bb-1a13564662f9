import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getDeviceId, getDeviceInfoString } from './device'

// 添加一个全局变量，用于跟踪刷新令牌的状态
let isRefreshing = false
// 添加一个队列，存储等待刷新令牌的请求
let refreshQueue: Array<{
  resolve: (value: unknown) => void
  reject: (reason?: any) => void
  config: any
}> = []

const service = axios.create({
  baseURL: '',  // 使用相对路径，让代理生效
  timeout: 15000
})

// 获取 Vite 配置的 base 路径
const BASE_URL = import.meta.env.BASE_URL || '/';

// 请求拦截器
service.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`
    }

    // 添加设备信息到请求头
    config.headers['X-Device-Info'] = getDeviceInfoString()
    config.headers['X-Device-ID'] = getDeviceId()

    return config
  },
  error => {
    console.error(error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data

    // 如果是二进制数据，直接返回
    if (response.request.responseType === 'blob' || response.request.responseType === 'arraybuffer') {
      return response
    }

    // 处理API返回的标准格式 {data, code, message, detail, correlationId}
    if (res.code !== undefined) {
      if (res.code === 200) {

        if (res.data.totals !== undefined && res.data.totals !== null) {
          if (typeof res.data.totals === "number") {
            res.data.totals = res.data.totals;
          } else if (typeof res.data.totals === "string") {
            // 尝试将字符串转换为数字
            res.data.totals = parseInt(res.data.totals, 10) || 0;
          }
        }

        // 请求成功，返回data
        return {
          data: res.data,
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          config: response.config,
          // 添加额外的响应信息，以便业务代码可以访问
          message: res.message,
          detail: res.detail,
          correlationId: res.correlationId
        }
      } else {
        // 处理业务错误，显示错误消息
        const errorMsg = res.message || '请求失败'
        ElMessage.error(errorMsg)

        // 创建一个包含完整错误信息的Error对象
        const error = new Error(errorMsg)
        // @ts-ignore
        error.code = res.code
        // @ts-ignore
        error.detail = res.detail
        // @ts-ignore
        error.correlationId = res.correlationId
        // @ts-ignore
        error.data = res.data

        return Promise.reject(error)
      }
    }

    // 直接返回数据，由业务代码处理具体的成功/失败逻辑
    return {
      data: res,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      config: response.config
    }
  },
  async error => {
    if (error.response) {
      const { status, config } = error.response
      const url = config.url || ''

      const isRefreshTokenRequest = url.includes(`/refresh-token`);
      const isLoginRequest = url.includes(`/User/login`);

      // 401: 未登录或Token过期
      if (status === 401) {
        const userStore = useUserStore()

        // 登录请求返回401，只提示，不跳转
        if (isLoginRequest) {
          let errorMsg = '用户名或密码错误'
          if (error.response.data) {
            const { message, detail } = error.response.data
            errorMsg = message || detail || errorMsg
          }
          ElMessage({
            message: errorMsg,
            type: 'error',
            duration: 5 * 1000
          })
          return Promise.reject(error)
        }

        // 如果是 refresh-token 请求本身，直接跳转登录
        if (isRefreshTokenRequest) {
          userStore.resetToken()
          ElMessage({
            message: '登录状态已过期，请重新登录',
            type: 'error',
            duration: 3 * 1000
          })
          window.location.replace(BASE_URL + 'login')
          setTimeout(() => {
            window.location.href = BASE_URL + 'login'
          }, 100)
          return Promise.reject(error)
        }

        // 检查是否已经重试过
        if (config._retry) {
          userStore.resetToken()
          ElMessage({
            message: '登录状态已过期，请重新登录',
            type: 'error',
            duration: 3 * 1000
          })
          window.location.replace(BASE_URL + 'login')
          setTimeout(() => {
            window.location.href = BASE_URL + 'login'
          }, 100)
          return Promise.reject(error)
        }

        // 如果有刷新令牌，尝试刷新Token
        if (userStore.refreshToken) {
          const originalRequest = error.config

          if (!isRefreshing) {
            console.log('开始刷新令牌')
            isRefreshing = true
            userStore.refreshUserToken()
              .then(result => {
                console.log('刷新令牌成功，处理队列中的请求')

                // 刷新成功，处理队列中的请求
                refreshQueue.forEach(({ config, resolve }) => {
                  config.headers['Authorization'] = `Bearer ${result.accessToken}`
                  config._retry = true
                  resolve(service(config))
                })

                // 清空队列
                refreshQueue = []

                // 重试当前请求
                originalRequest.headers['Authorization'] = `Bearer ${result.accessToken}`
                originalRequest._retry = true
                service(originalRequest)
                  .then(() => {
                    console.log('重试请求成功')
                  })
                  .catch(err => {
                    console.error('重试请求失败:', err)
                  })
              })
              .catch(refreshError => {
                console.error('刷新令牌失败:', refreshError)

                // 刷新失败，拒绝所有队列中的请求
                refreshQueue.forEach(({ reject }) => {
                  reject(refreshError)
                })

                // 清空队列
                refreshQueue = []

                // 清空token并跳转到登录页
                userStore.resetToken()

                ElMessage({
                  message: '登录状态已过期，请重新登录',
                  type: 'error',
                  duration: 3 * 1000
                })

                console.log('刷新令牌失败，强制跳转到登录页')
                // 使用replace而不是href，强制刷新页面
                window.location.replace(BASE_URL + 'login')

                // 添加一个延迟执行的备份跳转，以防第一次跳转失败
                setTimeout(() => {
                  console.log('执行备份跳转')
                  window.location.href = BASE_URL + 'login'
                }, 100)
              })
              .finally(() => {
                // 重置刷新状态
                isRefreshing = false
              })

            // 返回一个Promise，等待刷新令牌的结果
            return new Promise((resolve, reject) => {
              refreshQueue.push({
                resolve,
                reject,
                config: originalRequest
              })
            })
          } else {
            // 如果已经有刷新请求在进行中，将当前请求加入队列
            console.log('已有刷新令牌请求在进行中，加入队列')
            return new Promise((resolve, reject) => {
              refreshQueue.push({
                resolve,
                reject,
                config: { ...originalRequest, _retry: true }
              })
            })
          }
        } else {
          // 没有刷新令牌，清空token并直接跳转到登录页
          console.log('没有刷新令牌，直接跳转到登录页')
          userStore.resetToken()

          ElMessage({
            message: '登录状态已过期，请重新登录',
            type: 'error',
            duration: 3 * 1000
          })

          // 使用replace而不是href，强制刷新页面
          window.location.replace(BASE_URL + 'login')

          // 添加一个延迟执行的备份跳转，以防第一次跳转失败
          setTimeout(() => {
            console.log('执行备份跳转')
            window.location.href = BASE_URL + 'login'
          }, 100)

          // 确保不会继续处理
          return Promise.reject(error)
        }
      } else if (status === 403) {
        const { message } = error.response.data
        ElMessage({
          message: message,
          type: 'error',
          duration: 5 * 1000
        })
      } else if (status === 500) {
        // 处理500内部服务器错误
        let errorMsg = '服务器内部错误'
        if (error.response.data) {
          const { message } = error.response.data
          // 只显示消息，不显示详细的堆栈跟踪
          errorMsg = message || errorMsg

          // 如果是开发环境，可以在控制台输出详细信息
          if (import.meta.env.DEV) {
            console.error('Server Error Details:', error.response.data)
          }
        }

        ElMessage({
          message: errorMsg,
          type: 'error',
          duration: 5 * 1000
        })
      } else {
        // 处理其他错误
        let errorMsg = '请求失败'
        if (error.response.data) {
          const { message } = error.response.data
          errorMsg = message || errorMsg
        }

        ElMessage({
          message: errorMsg,
          type: 'error',
          duration: 5 * 1000
        })
      }
    } else {
      ElMessage({
        message: '网络错误，请检查您的网络连接',
        type: 'error',
        duration: 5 * 1000
      })
    }

    return Promise.reject(error)
  }
)

export default service