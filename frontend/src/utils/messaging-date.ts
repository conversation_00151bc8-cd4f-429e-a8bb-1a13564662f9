/**
 * 消息系统专用时间处理工具
 * 处理 DateTimeOffset 类型的时间转换
 */

/**
 * 格式化 DateTimeOffset 时间为本地时间
 * @param dateTimeOffset DateTimeOffset 字符串，如 "2024-01-01T10:30:00+08:00"
 * @param format 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的本地时间字符串
 */
export function formatDateTimeOffset(dateTimeOffset: string | null | undefined, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!dateTimeOffset) return ''
  
  try {
    // 解析 DateTimeOffset 字符串为 Date 对象
    // JavaScript 的 Date 构造函数可以正确解析 ISO 8601 格式的时间字符串
    const date = new Date(dateTimeOffset)
    
    if (isNaN(date.getTime())) return ''
    
    // 转换为本地时间
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  } catch (error) {
    console.warn('Failed to parse DateTimeOffset:', dateTimeOffset, error)
    return ''
  }
}

/**
 * 格式化 DateTimeOffset 为本地日期
 * @param dateTimeOffset DateTimeOffset 字符串
 * @returns 格式化后的日期字符串 YYYY-MM-DD
 */
export function formatDateOffset(dateTimeOffset: string | null | undefined): string {
  return formatDateTimeOffset(dateTimeOffset, 'YYYY-MM-DD')
}

/**
 * 格式化 DateTimeOffset 为本地时间
 * @param dateTimeOffset DateTimeOffset 字符串
 * @returns 格式化后的时间字符串 HH:mm:ss
 */
export function formatTimeOffset(dateTimeOffset: string | null | undefined): string {
  return formatDateTimeOffset(dateTimeOffset, 'HH:mm:ss')
}

/**
 * 获取 DateTimeOffset 的相对时间描述
 * @param dateTimeOffset DateTimeOffset 字符串
 * @returns 相对时间描述，如 "刚刚"、"5分钟前"、"2小时前"等
 */
export function getRelativeTimeOffset(dateTimeOffset: string | null | undefined): string {
  if (!dateTimeOffset) return ''
  
  try {
    const date = new Date(dateTimeOffset)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    
    if (diff < 0) return '未来时间'
    
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (seconds < 60) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    
    return formatDateTimeOffset(dateTimeOffset, 'YYYY-MM-DD')
  } catch (error) {
    console.warn('Failed to get relative time for DateTimeOffset:', dateTimeOffset, error)
    return ''
  }
}

/**
 * 将本地时间转换为 DateTimeOffset 字符串（用于发送到服务器）
 * @param date Date 对象或时间字符串
 * @returns DateTimeOffset 格式的字符串
 */
export function toDateTimeOffset(date: Date | string): string {
  try {
    const d = typeof date === 'string' ? new Date(date) : date
    
    if (isNaN(d.getTime())) return ''
    
    // 使用 toISOString() 获取 UTC 时间，然后添加本地时区偏移
    return d.toISOString()
  } catch (error) {
    console.warn('Failed to convert to DateTimeOffset:', date, error)
    return ''
  }
}

/**
 * 获取当前时间的 DateTimeOffset 字符串
 * @returns 当前时间的 DateTimeOffset 格式字符串
 */
export function nowDateTimeOffset(): string {
  return toDateTimeOffset(new Date())
}

/**
 * 检查 DateTimeOffset 字符串是否有效
 * @param dateTimeOffset DateTimeOffset 字符串
 * @returns 是否为有效的时间格式
 */
export function isValidDateTimeOffset(dateTimeOffset: string | null | undefined): boolean {
  if (!dateTimeOffset) return false
  
  try {
    const date = new Date(dateTimeOffset)
    return !isNaN(date.getTime())
  } catch {
    return false
  }
}

/**
 * 比较两个 DateTimeOffset 时间
 * @param date1 第一个时间
 * @param date2 第二个时间
 * @returns 比较结果：-1(date1 < date2), 0(相等), 1(date1 > date2)
 */
export function compareDateTimeOffset(
  date1: string | null | undefined, 
  date2: string | null | undefined
): number {
  if (!date1 && !date2) return 0
  if (!date1) return -1
  if (!date2) return 1
  
  try {
    const d1 = new Date(date1)
    const d2 = new Date(date2)
    
    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) return 0
    
    if (d1.getTime() < d2.getTime()) return -1
    if (d1.getTime() > d2.getTime()) return 1
    return 0
  } catch {
    return 0
  }
}

/**
 * 格式化时间间隔
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 时间间隔描述，如 "2小时30分钟"
 */
export function formatDurationOffset(
  startTime: string | null | undefined,
  endTime: string | null | undefined
): string {
  if (!startTime || !endTime) return ''
  
  try {
    const start = new Date(startTime)
    const end = new Date(endTime)
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) return ''
    
    const diff = Math.abs(end.getTime() - start.getTime())
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (days > 0) {
      const remainingHours = hours % 24
      return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`
    }
    
    if (hours > 0) {
      const remainingMinutes = minutes % 60
      return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
    }
    
    if (minutes > 0) {
      const remainingSeconds = seconds % 60
      return remainingSeconds > 0 ? `${minutes}分钟${remainingSeconds}秒` : `${minutes}分钟`
    }
    
    return `${seconds}秒`
  } catch {
    return ''
  }
}
