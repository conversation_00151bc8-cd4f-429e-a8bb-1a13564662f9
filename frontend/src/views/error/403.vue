<template>
  <div class="error-container">
    <div class="error-code">403</div>
    <div class="error-message">{{ $t('error.pageNoRight') }}</div>

    <el-button type="primary" @click="goHome">{{ $t('error.backToHome') }}</el-button>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
const router = useRouter()
function goHome() {
  router.push('/')
}
</script>

<style scoped>
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}
.error-code {
  font-size: 80px;
  font-weight: bold;
  color: #f56c6c;
  margin-bottom: 16px;
}
.error-message {
  font-size: 20px;
  color: #666;
  margin-bottom: 24px;
}
</style> 