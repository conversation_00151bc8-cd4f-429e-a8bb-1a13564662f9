<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="not-found">
    <div class="not-found-content">
      <h1>404</h1>
      <h2>{{ $t('error.pageNotFound') }}</h2>
      <div class="actions">
        <el-button type="primary" @click="goHome">{{ $t('error.backToHome') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style lang="scss" scoped>
.not-found {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
  
  .not-found-content {
    text-align: center;
    
    h1 {
      font-size: 120px;
      color: #409eff;
      margin: 0;
      line-height: 1.2;
    }
    
    h2 {
      font-size: 24px;
      color: #606266;
      margin: 20px 0 30px;
    }
    
    .actions {
      margin-top: 30px;
    }
  }
}
</style>
