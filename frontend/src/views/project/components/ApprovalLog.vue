<template>
  <el-card class="approval-log" v-if="logs && logs.length > 0">
    <template #header>
      <h3 class="approval-log-header">审核日志</h3>
    </template>
    <el-table :data="logs" border style="width: 100%">
      <el-table-column prop="NodeType" label="节点类型" width="120">
        <template #default="{ row }">
          {{
            row.NodeType === 'FourthApproval' ? '初级审核员' :
                row.NodeType === 'ThirdApproval' ? '中级审核员' :
                    row.NodeType === 'SecondApproval' ? '高级审核员' :
                        row.NodeType === 'FirstApproval' ? '总审核员' :
                            row.NodeType === 'User' ? '用户' :
                                row.NodeType
          }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          {{
            row.Action === 'Approval' ? '通过' :
                row.Action === 'Reject' ? '驳回' :
                    row.Action === 'Return' ? '退回' :
                        row.Action === 'Submit' ? '用户提交' :
                            row.Action === 'EditApply' ? '用户提交再修改申请' :
                                row.Action === 'RejectedEditApply' ? '驳回再修改申请' :
                                    row.Action === 'Recall' ? '召回' :
                                        row.Action
          }}
        </template>
      </el-table-column>
      <el-table-column prop="OperateTime" label="操作时间" width="180">
        <template #default="{ row }">
          {{ formatToLocal(row.OperateTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="OperatorName" label="操作人" width="260">
        <template #default="{ row }">
          {{ row.OperatorName }}（{{ row.OperatorAccount }}）
        </template>
      </el-table-column>
      <el-table-column prop="Description" label="审核意见"/>
    </el-table>
  </el-card>
</template>

<script lang="ts" setup>
import {defineProps} from 'vue'
import type {ApprovalHistoryDto} from '@/dtos/itmctr'
import {formatToLocal} from '@/utils/date'

const props = defineProps<{
  logs: ApprovalHistoryDto[]
}>()
</script>

<style scoped>
.approval-log {
  margin: 16px 0;
}

.approval-log-header {
  margin-bottom: 0;
  font-size: 18px;
  font-weight: bold;
}
</style>
