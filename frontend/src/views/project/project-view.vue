<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div>
    <div class="app-container">
      <el-card class="table-card">
        <template #header>
          <div class="card-header">
            <span>查看项目信息/View project</span>
          </div>
        </template>
        <FormCanvas :formSchema="formSchema" render-mode="view" />
      </el-card>
    </div>
    <!-- 悬浮操作按钮 -->
    <div class="floating-actions">
      <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElLoading } from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";

import { getProject } from "@/api/itmctr";
import { FormDefinitionDto } from "@/dtos/dynamic-form.dto";

const router = useRouter();
const route = useRoute();

// 表单数据
const formSchema = ref<FormDefinitionDto>();

const loading = ref<boolean>(false);

const businessId = computed(() => {
  return route.params.businessId;
});

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = await getProject(String(businessId.value));
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;

      loading.value = false;
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    // ElMessage.error(`获取表单定义失败: ${error.message || error}`);
  } finally {
    if (loadingInstance) loadingInstance.close();
  }
};
const handleBack = () => {
  router.go(-1);
};

// 组件挂载时加载表单
onMounted(loadProject);
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}
.card-header {
  color: #d77680;
  font-weight: bolder;
}
// floating-actions样式已移至全局样式 @/styles/index.scss
</style>
