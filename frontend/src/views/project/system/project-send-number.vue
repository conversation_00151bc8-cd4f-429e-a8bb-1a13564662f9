<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="page-with-sticky-actions">
    <div class="app-container page-content">
      <ApprovalLog :logs="approvalLogs"/>
      <el-card class="table-card">
        <template #header>
          <div class="card-header">
            <span>审核项目信息/Review project</span>
          </div>
        </template>
        <FormCanvas :formSchema="formSchema" render-mode="view"/>
        <el-form
            scroll-to-error
            :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
            ref="formRef"
            :model="sendNumberDto"
            :rules="rules"
            label-width="200px"
        >

          <el-form-item
              label="请输入要分配的注册号" prop="number"
              v-if="initialized&&!formSchema?.formData?.RegistrationNumber">
            <div style="display: flex; align-items: center; gap: 10px;">
              <el-input
                  type="text"
                  v-model="sendNumberDto.number"
                  maxlength="6"
                  @input="onNumberInput"
                  @blur="onNumberBlur"
                  style="width:300px !important"
                  placeholder="请输入要分配的注册号后六位"
              >
                <template #prepend>
                  <span class="el-input__prefix">{{ sendNumberDto.prefix }}{{ sendNumberDto.year }}</span>
                </template>
                <template #suffix>
                  <el-popover
                      trigger="hover"
                      title="可用范围/Available Ranges"
                      content="Top Left prompts info"
                      placement="bottom"
                      width="300px"
                      :hide-after="0"
                      v-if="availableRanges.length"
                  >
                    <template #reference>
                      <el-icon style="cursor: pointer;">
                        <Warning/>
                      </el-icon>
                    </template>
                    <template #default>
                      <div>
                        <div v-for="range in availableRanges" :key="range.start + '-' + range.end">
                          {{ String(range.start).padStart(6, '0') }} - {{ String(range.end).padStart(6, '0') }}
                        </div>
                      </div>
                    </template>
                  </el-popover>
                </template>
              </el-input>
              <el-button type="primary" plain :loading="generating" @click="handleGenerateNumber">
                生成/Generate
              </el-button>
              <el-button type="primary" plain @click="handleValidateNumber">
                校验/Validate
              </el-button>
            </div>
            <div style="display: flex; align-items: center; gap: 10px;">
              <el-text type="info">
                请输入6位数字，系统会自动补全前缀和年份，您亦可以点击"生成"按钮自动分配。
              </el-text>
            </div>
          </el-form-item>
          <el-form-item label="审核意见" prop="description">
            <el-input
                type="textarea"
                v-model="sendNumberDto.description"
                :rows="8"
                clearable
                placeholder="请填写审核意见"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 浮动操作按钮 -->
      <div class="floating-actions">
        <el-button type="primary" @click="handApproval" :loading="loading">审核通过/Approve</el-button>
        <el-button type="danger" @click="handleReject" :loading="loading"
        >审核驳回/Reject
        </el-button
        >
        <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import {ref, onMounted, computed, defineExpose, nextTick} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElMessage, ElLoading, FormInstance, FormRules} from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";
import {getProject} from "@/api/itmctr";
import {rejectProjectLevel1, approvalProjectLevel1} from "@/api/itmctr-mgt";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";
import {SendNumberDto} from "@/dtos/dynamic-form-mgt.dto";
import ApprovalLog from '@/views/project/components/ApprovalLog.vue'
import {getAvailableRegistrationNumbers} from "@/api/dynamic-form-mgt";
import type {AvailableRegistrationNumbersResponse} from "@/dtos/dynamic-form-mgt.dto";
import {Warning} from "@element-plus/icons-vue";
import {existFormDataValue} from "@/api/dynamic-form-mgt";

const router = useRouter();
const route = useRoute();


// 表单数据
const formSchema = ref<FormDefinitionDto>();

const loading = ref<boolean>(false);

const sendNumberDto = ref<SendNumberDto>({
  number: '',
  description: "",
  year: new Date().getFullYear(),
  prefix: "ITMCTR",
});

const availableRanges = ref<{ start: number; end: number }[]>([]);
const generating = ref(false);

const formRef = ref<FormInstance>();
const rejectRules: FormRules = {
  description: [{required: true, message: "请填写审核意见", trigger: "blur"}],
};
const approvalRules: FormRules = {
  number: [
    {required: true, message: "请填写要分配的注册号后六位", trigger: "blur"},
    {pattern: /^\d{6}$/, message: "注册号必须为六位数字", trigger: "blur"},
    {min: 1, max: 999999, message: "注册号必须介于1-999999", trigger: "blur"},
  ],
};
const rules = ref(approvalRules);

const currentRangeIndex = ref(0);
const currentNumberInRange = ref(0);

const businessId = computed(() => {
  return route.params.businessId;
});

const approvalLogs = computed(() => {
  try {
    return JSON.parse(formSchema.value?.formData?.ApprovalHistory || '[]')
  } catch {
    return []
  }
})

const initialized = ref(false);

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = await getProject(String(businessId.value));
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;

      initialized.value = true;
      loading.value = false;
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    ElMessage.error(`获取表单定义失败: ${error.message || error}`);
  } finally {
    if (loadingInstance) loadingInstance.close();
  }
};


const handleBack = () => {
  router.go(-1);
};

const handApproval = async () => {
  rules.value = approvalRules;
  await nextTick();

  if (!formRef.value) {
    return;
  }

  try {
    await formRef.value.validate();
  } catch (error) {
    // Validation failed. The form will display errors.
    return;
  }
  
  loading.value = true;
  try {
    await approvalProjectLevel1(String(businessId.value), sendNumberDto.value);
    ElMessage.success("审核成功");
    await router.push({path: "/project/system/pending-send-number-list"});
  } catch (error: any) {
    ElMessage.error(`审核失败: ${error.message || error}`);
  } finally {
    loading.value = false;
  }
}

const handleReject = async () => {
  rules.value = rejectRules;
  await nextTick();

  if (!formRef.value) {
    return;
  }

  try {
    await formRef.value.validate();
  } catch (error) {
    // Validation failed.
    return;
  }

  loading.value = true;
  try {
    await rejectProjectLevel1(String(businessId.value), sendNumberDto.value);
    ElMessage.success("驳回成功");
    await router.push({path: "/project/system/pending-send-number-list"});
  } catch (error: any) {
    ElMessage.error(`驳回失败: ${error.message || error}`);
  } finally {
    loading.value = false;
  }
};

const onNumberInput = (val: string) => {
  // 只保留数字，限制最大长度为6
  sendNumberDto.value.number = val.replace(/\D/g, '').slice(0, 6);
};

const onNumberBlur = () => {
  sendNumberDto.value.number = (sendNumberDto.value.number || '').padStart(6, '0');
};

const handleGenerateNumber = async () => {
  generating.value = true;
  try {
    // 如果是第一次调用，获取可用范围
    if (availableRanges.value.length === 0) {
      const res = await getAvailableRegistrationNumbers(sendNumberDto.value.prefix, sendNumberDto.value.year);
      const data: AvailableRegistrationNumbersResponse = res.data ?? res;
      availableRanges.value = data.availableNumberRanges || [];

      if (availableRanges.value.length === 0) {
        ElMessage.warning("没有可用的注册号区间！");
        return;
      }

      // 初始化第一个范围
      currentRangeIndex.value = 0;
      currentNumberInRange.value = availableRanges.value[0].start;
    } else {
      // 在现有范围内寻找下一个可用号码
      const currentRange = availableRanges.value[currentRangeIndex.value];
      currentNumberInRange.value++;

      // 如果当前范围已用完，移动到下一个范围
      if (currentNumberInRange.value > currentRange.end) {
        currentRangeIndex.value++;
        if (currentRangeIndex.value >= availableRanges.value.length) {
          // 所有范围都已用完，重新开始
          currentRangeIndex.value = 0;
          currentNumberInRange.value = availableRanges.value[0].start;
        } else {
          currentNumberInRange.value = availableRanges.value[currentRangeIndex.value].start;
        }
      }
    }

    // 设置当前号码
    sendNumberDto.value.number = String(currentNumberInRange.value).padStart(6, "0");
  } catch (e) {
    ElMessage.error("获取可用注册号失败");
  } finally {
    generating.value = false;
  }
};

const handleValidateNumber = async () => {
  const fullNumber = `${sendNumberDto.value.prefix}${sendNumberDto.value.year}${sendNumberDto.value.number}`;
  try {
    const exists = (await existFormDataValue('PROJECT', 'RegistrationNumber', fullNumber)).data;
    if (exists) {
      ElMessage.error(`注册号 ${fullNumber} 已被占用！`);
    } else {
      ElMessage.success(`注册号 ${fullNumber} 可用。`);
    }
  } catch (e) {
    ElMessage.error('校验注册号时发生错误');
  }
};

// 组件挂载时加载表单
onMounted(async () => {
  sendNumberDto.value.number = (sendNumberDto.value.number || '').padStart(6, '0');
  await loadProject();
  // 页面初始化时自动获取可用范围并设置默认值
  await handleGenerateNumber();
});

// 暴露需要给模板使用的方法和变量
defineExpose({});
</script>

<style lang="scss" scoped>


.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}

// 浮动操作按钮样式已移至全局样式

// 悬浮操作按钮样式 (参考 project-add.vue)
.side-affix {
  position: fixed;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 200;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.affix-btn-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.affix-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 0;
}

.affix-btn-label {
  margin-top: 2px;
  margin-bottom: 4px;
  font-size: 12px;
  color: #fff;
  background: rgba(0, 0, 0, 0.18);
  border-radius: 10px;
  padding: 1px 8px;
  text-align: center;
  font-weight: 400;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  letter-spacing: 0;
  line-height: 1.2;
}

.ai-audit-btn {
  background: #409eff; /* 改为蓝色 */
  color: #fff;
  box-shadow: 0 0 12px 2px rgba(64, 158, 255, 0.5), 0 1px 4px rgba(0, 0, 0, 0.1); /* 调整阴影颜色 */
  transition: box-shadow 0.2s;
}

.ai-audit-btn:hover {
  box-shadow: 0 0 24px 6px rgba(64, 158, 255, 0.8),
  0 2px 8px rgba(0, 0, 0, 0.15); /* 调整阴影颜色 */
}

.ai-audit-dialog-content {
  font-size: 16px;
  color: #333;
  line-height: 1.8;
  padding: 12px 18px 0 18px;
  text-align: left;
}

.ai-audit-dialog-content p {
  margin-bottom: 10px;
}

.el-dialog {
  border-radius: 14px !important;
}

.affix-btn svg {
  display: block; /* 确保 SVG 是一个块级元素 */
  width: 100%; /* SVG 宽度填充容器 */
  height: 100%; /* SVG 高度填充容器 */
  padding: 8px; /* 添加一些内边距，让图标小一点，不顶满 */
  box-sizing: border-box; /* 让内边距包含在元素的总尺寸内 */
}

.app-container.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2100; /* 保留提高的 z-index 以覆盖头部 */
  /* 允许内部元素flex grow */
  display: flex;
  flex-direction: column;
  background-color: #fff; /* 保留白色背景 */
}

.ai-compare-area {
  display: flex; /* 设置为 Flex 容器 */
  gap: 20px;
  margin-top: 20px; /* 添加一些顶部外边距 */
  flex-grow: 1; /* 弹性填充剩余垂直空间 */
  min-height: 0; /* 允许缩小 */
  overflow: hidden; /* 隐藏自身的滚动条，滚动由子元素控制 */
}

.compare-left {
  flex: 1; /* 弹性填充宽度 */
  min-width: 0;
  display: flex; /* 设置为 Flex 容器 */
  flex-direction: column; /* 子元素垂直布局 */
  max-width: 50%; /* 可以设置最大宽度 */
  overflow-y: hidden; /* 隐藏自身的滚动条，滚动由内部控制 */
  padding-right: 10px; /* 为滚动条留出空间 */
}

.compare-right {
  flex: 1; /* 弹性填充宽度 */
  min-width: 0;
  overflow-y: auto; /* 右侧内容区域垂直滚动 */
  padding: 10px; /* 保持内边距 */
  background-color: #f8f8f8; /* 添加一个浅灰色背景，与左侧区分 */
  font-size: 14px;
}

.pdf-viewer-wrapper {
  /* 移除 flex-grow，设置明确高度 */
  /* flex-grow: 1; */
  min-height: 0; /* 保留 min-height */
  height: 100%; /* 填充父容器高度 */
  /* 滚动条现在由 PdfViewer 内部控制 */
  overflow-y: visible; /* 或者 auto，取决于 PdfViewer 内部容器的设置 */
  padding: 10px 0;
  /* 添加一个背景色以便观察其区域 */
  /* background-color: rgba(0, 255, 0, 0.1); */
}

.compare-results {
  /* 右侧结果内容的容器样式 */
  display: flex; /* 设置为 Flex 容器 */
  flex-direction: column; /* 子元素垂直排列 */
  gap: 20px; /* 结果部分之间的间距 */
  padding-bottom: 50px; /* 为底部浮动按钮留出空间 */
}

.result-section {
  background-color: #fff; /* 每个结果部分的背景色 */
  border: 1px solid #eee; /* 边框 */
  border-radius: 8px; /* 圆角 */
  padding: 15px; /* 内边距 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 轻微阴影 */

  h3 {
    margin-top: 0; /* 移除顶部外边距 */
    margin-bottom: 10px; /* 底部外边距 */
    color: #333; /* 标题颜色 */
    font-size: 18px; /* 字体大小 */
    border-bottom: 1px solid #eee; /* 底部边框 */
    padding-bottom: 8px; /* 底部内边距 */
  }
}

.compare-item {
  margin-bottom: 15px; /* 每个对比项之间的间距 */
  padding-bottom: 15px; /* 底部内边距 */
  border-bottom: 1px dashed #ddd; /* 虚线分隔 */

  &:last-child {
    margin-bottom: 0; /* 最后一个项移除底部外边距 */
    padding-bottom: 0; /* 最后一个项移除底部内边距 */
    border-bottom: none; /* 最后一个项移除底部边框 */
  }

  h4 {
    margin-top: 0; /* 移除顶部外边距 */
    margin-bottom: 8px; /* 底部外边距 */
    color: #555; /* 标题颜色 */
    font-size: 16px; /* 字体大小 */
  }

  p {
    margin-bottom: 5px; /* 段落之间的间距 */
    line-height: 1.6; /* 行高 */
    color: #666; /* 文本颜色 */
  }
}

.nested-item {
  margin-left: 20px; /* 嵌套项左侧缩进 */
  border-left: 3px solid #007bff; /* 左侧强调边框 */
  padding-left: 15px; /* 左侧内边距 */
}

/* 相似度高亮样式 */
.high-similarity {
  font-weight: bold; /* 字体加粗 */
  color: #008000; /* 绿色文本 */
  background-color: #e9f7ef; /* 浅绿色背景 */
  padding: 2px 5px; /* 内边距 */
  border-radius: 4px; /* 圆角 */
}

.low-similarity {
  font-weight: bold; /* 字体加粗 */
  color: #dc3545; /* 红色文本 */
  background-color: #f8d7da; /* 浅红色背景 */
  padding: 2px 5px; /* 内边距 */
  border-radius: 4px; /* 圆角 */
}

.references-container {
  margin-top: 15px; /* 引用容器顶部外边距 */
  border-top: 1px dashed #ddd; /* 顶部虚线分隔 */
  padding-top: 15px; /* 顶部内边距 */

  h5 {
    margin-top: 0; /* 移除顶部外边距 */
    margin-bottom: 8px; /* 底部外边距 */
    color: #777; /* 标题颜色 */
    font-size: 14px; /* 字体大小 */
  }
}

.reference-detail {
  margin-bottom: 10px; /* 每个引用详情之间的间距 */
  background-color: #f2f2f2; /* 引用详情背景色 */
  padding: 10px; /* 内边距 */
  border-radius: 4px; /* 圆角 */
  border: 1px solid #eee; /* 边框 */

  p {
    margin-bottom: 3px; /* 引用详情段落间距 */
    line-height: 1.5; /* 行高 */
    font-size: 14px; /* 字体大小 */
    color: #555; /* 文本颜色 */
    margin-top: 0px;
  }
}

.close-compare-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 99999; /* 确保在全屏内容和浮动按钮上方 */
  background-color: rgba(255, 255, 255, 0.8); /* 半透明白色背景 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
