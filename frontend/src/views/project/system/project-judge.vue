<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>判断项目信息/Judge project</span>
        </div>
      </template>

      <ApprovalLog :logs="approvalLogs" v-if="approvalLogs!=null&&approvalLogs.length>0" />
      <FormCanvas :formSchema="formSchema" render-mode="view" />
      <el-form
        scroll-to-error
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
        ref="formRef"
        :model="judgeProjectDto"
        :rules="rules"
        label-width="400px"
      >
        <el-form-item label="是否为传统医学项目" prop="traditionalProject">
          <el-select
            placeholder="请选择"
            clearable
            v-model="judgeProjectDto.traditionalProject"
          >
            <el-option label="是/Yes" :value="true" />
            <el-option label="否/No" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="请指派高级审核员"
          prop="userId"
          v-if="judgeProjectDto.traditionalProject"
        >
          <el-select
            v-model="judgeProjectDto.userId"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="user in secondaryAuditors"
              :key="user.key+'-'+user.positionCode"
              :label="user.realName + '[' + user.username + ']'+'['+user.organizationNamePath+']'+'['+user.positionName+']'"
              :value="user.key"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
  <!-- 悬浮操作按钮 -->
  <div class="floating-actions">
    <el-button @click="handleSave" type="primary" :loading="loading"
    >保存/Save
    </el-button
    >
    <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, computed} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElMessage, ElLoading, FormInstance, FormRules} from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";

import {getProject} from "@/api/itmctr";
import {judgeProject} from "@/api/itmctr-mgt";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";

import {UserDto} from "@/dtos/rbac-mgt.dto";

import {getManagedPositionUsers} from "@/api/rbac-mgt";
import {JudgeProjectDto} from "@/dtos/itmctr";
import ApprovalLog from "@/views/project/components/ApprovalLog.vue";

const router = useRouter();
const route = useRoute();

// 表单数据
const formSchema = ref<FormDefinitionDto>();

const loading = ref<boolean>(false);

const secondaryAuditors = ref<UserDto[]>([]);

const judgeProjectDto = ref<JudgeProjectDto>({
  traditionalProject: undefined,
  userId: undefined,
});

const formRef = ref<FormInstance>();
const rules: FormRules = {
  traditionalProject: [
    {required: true, message: "请选择是否为传统项目", trigger: "blur"},
  ],
  userId: [{required: true, message: "请选择高级审核员", trigger: "blur"}],
};

const businessId = computed(() => {
  return route.params.businessId;
});

const loadSecondaryAuditors = async () => {
  try {
    const response = await getManagedPositionUsers("CHECKER_LEVEL_2");
    secondaryAuditors.value = response.data.flatMap(q => q.users.map(u => {
      //如果namePath开头有"/"则去掉开头的"/"
      u.organizationNamePath = q.organization.namePath.startsWith("/") ? q.organization.namePath.substring(1) : q.organization.namePath;
      u.positionName = "高级审核员";
      u.positionCode = "CHECKER_LEVEL_2";
      return u;
    }));
  } catch (error) {
    console.error("获取高级审核员失败", error);
    ElMessage.error("获取高级审核员失败");
  }
};

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = await getProject(String(businessId.value));
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;

      loading.value = false;
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    ElMessage.error(`获取表单定义失败: ${error.message || error}`);
  } finally {
    if (loadingInstance) loadingInstance.close();
  }
};
const handleBack = () => {
  router.go(-1);
};
const handleSave = async () => {
  loading.value = true;
  if (!formRef.value) {
    return;
  }

  try {
    await formRef.value.validate();
  } catch (error) {
    loading.value = false;
    return;
  }

  try {
    await judgeProject(String(businessId.value), judgeProjectDto.value);
    router.go(-1);
  } catch (error) {
    console.error("判断项目失败", error);
    ElMessage.error("判断项目失败");
  } finally {
    loading.value = false;
  }
};
const approvalLogs = computed(() => {
  try {
    return JSON.parse(formSchema.value?.formData?.ApprovalHistory || "[]");
  } catch {
    return [];
  }
});
// 组件挂载时加载表单
onMounted(async () => {
  await loadProject();
  await loadSecondaryAuditors();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}

// 悬浮操作按钮样式
.floating-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 50px;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  padding: 12px 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
