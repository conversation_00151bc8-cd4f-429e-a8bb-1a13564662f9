<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      title="全部项目/All Projects"
      :data="formInstanceList"
      :columns="tableColumns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handleCurrentChange"
      @size-change="handleSizeChange"
      @sort-change="handleSortChange"
      :default-sort="{ prop: queryParams.$sortBy!, order: queryParams.$orderBy === 'asc' ? 'ascending' : 'descending' }"
    >
      <template #toolbar></template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        
        <el-button type="primary" link @click="handleView(row)">
          <el-icon>
            <View />
          </el-icon>
          查看
        </el-button>
        
        <el-button v-if="canRecall(row.formData)" type="primary" link @click="handleRecall(row)">
          <el-icon>
            <RefreshLeft />
          </el-icon>
          撤回
        </el-button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted} from "vue";
import SearchForm, {FormItem} from "@/components/common/SearchForm.vue";
import DataTable, {TableColumn} from "@/components/common/DataTable.vue";
import {View,RefreshLeft} from "@element-plus/icons-vue";
import {ElMessage} from "element-plus";
import {getNewestFormInstancePage} from "@/api/dynamic-form-mgt";
import {FormDataQueryOperator, FormInstanceStatus} from "@/enums";
import {
  FormInstanceDto,
  FormInstanceJsonDto,
  FormDefinitionDto,
} from "@/dtos/dynamic-form.dto";
import {
  getDisplayByDefinition,
  getStatusTagType,
  getStatusText,
  getFormDataValue,
} from "@/utils/dynamic-form";
import router from "@/router";
import {formatTimestampToLocalString} from "@/utils/date";

// props
const props = defineProps<{
  title: string;
  columns: Array<{
    prop: string;
    label: string;
    width?: number;
    placeholder?: string;
    search?: boolean;
  }>;
}>();

const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "注册题目/Public title",
    prop: "public_title",
    placeholder: "",
  },
  {
    type: "input",
    label: "注册号/Registration number",
    prop: "registration_number",
    placeholder: "",
  },
  {
    type: "input",
    label: "申请注册联系人/Applicant",
    prop: "applicant",
    placeholder: "",
  },
]);

const tableColumns = ref<TableColumn[]>([
      {
        prop: "registration_number",
        label: {zh: "注册号", en: "Registration number"},
        width: 120,
        formatter: (row: any) =>
            getDisplayByDefinition(formDefinition.value, row, "registration_number"),
      },
      {
        prop: "public_title",
        label: {zh: "注册题目", en: "Public title"},
        width: 320,
        formatter: (row: any) =>
            getDisplayByDefinition(formDefinition.value, row, "public_title"),
      },
      {
        prop: "status",
        label: {zh: "审核状态", en: "Review Status"},
        width: 150,
        formatter(row: any) {
          let processStatus = getFormDataValue(row.formData, "ProcessStatus");

          let traditionalProject = getFormDataValue(row.formData, "TraditionalProject");

          if (processStatus == null || processStatus == "") {
            return traditionalProject == "" ? "待判断" : "非传统项目"
          } else {
            let result = "未知";
            switch (processStatus) {
              case "Approved":
                result = "已发号";
                break;
              case "PendingFirstApproval":
                result = "待发号";
                break;
              case "PendingSecondAssignment":
                result = "等待高级审核员分配";
                break;
              case "PendingThirdAssignment":
                result = "等待中级审核员分配";
                break;
              case "PendingFourthApproval":
                result = "待初审";
                break;
              case "RejectToApply":
                result = "驳回到用户";
                break;
              case "PendingThirdApproval":
                result = "待复审";
                break;
              case "PendingSecondApproval":
                result = "待核审";
                break;
            }
            return result;
          }
        },
      },
      {
        prop: "firstSubmitTime",
        label:
            {
              zh: "首次提交时间", en:
                  "First Submission"
            }
        ,
        sortable: true,
        width: 150,
        formatter(row
                  :
                  any
        ) {
          return formatTimestampToLocalString(getFormDataValue(row.formData, "FirstSubmitTime"));
        }
        ,
      }
      ,
      {
        prop: "firstApprovalUserName",
        label:
            {
              zh: "总审核员", en:
                  "Send User"
            }
        ,
        width: 100,
        formatter(row
                  :
                  any
        ) {
          return getFormDataValue(row.formData, "FirstApprovalUserName");
        }
        ,
      }
      ,
      {
        prop: "secondApprovalUserName",
        label:
            {
              zh: "高级审核员", en:
                  "Execute User"
            }
        ,
        width: 100,
        formatter(row
                  :
                  any
        ) {
          return getFormDataValue(row.formData, "SecondApprovalUserName");
        }
        ,
      }
      ,
      {
        prop: "ThirdApprovalUserName",
        label:
            {
              zh: "中级审核员", en:
                  "Third Execute User"
            }
        ,
        width: 100,
        formatter(row
                  :
                  any
        ) {
          return getFormDataValue(row.formData, "ThirdApprovalUserName");
        }
        ,
      }
      ,
      {
        prop: "FourthApprovalUserName",
        label:
            {
              zh: "初级审核员", en:
                  "Fourth Execute User"
            }
        ,
        width: 100,
        formatter(row
                  :
                  any
        ) {
          return getFormDataValue(row.formData, "FourthApprovalUserName");
        }
        ,
      }
      ,
    ])
;

// 查询参数
const queryParams = reactive<any>({
  $pageIndex: 1,
  $pageSize: 10,
  $sortBy: "firstSubmitTime", // 初始化排序字段
  $orderBy: "desc", // 初始化排序方向
  formCode: "PROJECT",
  status: [FormInstanceStatus.Submitted, FormInstanceStatus.Rejected],
  dynamicQueries: {},
  formDataDynamicQueries: [
    {
      key: "TraditionalProject",
      operator: FormDataQueryOperator.NotEqual,
      value: "False",
    },

  ],
});

const loading = ref(false);
const total = ref(0);

const formInstanceList = ref<FormInstanceJsonDto[]>([]);
const formDefinition = ref<FormDefinitionDto>({
  language: "",
  groups: [],
});

onMounted(() => {
  getList();
});

const getList = async () => {
  try {
    loading.value = true;
    const response = await getNewestFormInstancePage(queryParams);
    const {data} = response;
    formInstanceList.value = data.rows || [];
    formDefinition.value = data.formDefinition;
    total.value = data.totals;
  } catch (error) {
    ElMessage.error("获取表单实例列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = (formData: Record<string, any>) => {
  const dynamicQueries: Record<string, any> = {};
  for (const key in formData) {
    if (formData[key] != "" && key != "status") {
      dynamicQueries[key] = formData[key];
    }
  }
  Object.assign(queryParams, {
    ...formData,
    // status: formData.status,
    dynamicQueries: dynamicQueries,
  });
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};


// 处理排序事件
const handleSortChange = (sort: { prop: string; order: "ascending" | "descending" | null } | undefined) => {
  if (sort && sort.prop && sort.order) {
    queryParams.$sortBy = sort.prop;
    queryParams.$orderBy = sort.order === "ascending" ? "asc" : "desc";
  } else {
    queryParams.$sortBy = "";
    queryParams.$orderBy = "asc";
  }
  getList();
};
const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

const handleView = (row: FormInstanceDto) => {
  router.push({path: `/project/user/project-view/${row.businessId}`});
};

const handleEdit = (row: FormInstanceDto) => {
  router.push({path: `/project/user/project-add/${row.businessId}`});
};

const canRecall = (formData: Record<string, string>) => {
  let processStatus = getFormDataValue(formData, "ProcessStatus");
  if(processStatus ==='PendingSecondAssignment' || 
        processStatus ==='PendingThirdAssignment' ||
        processStatus ==='PendingFourthApproval'      )
        return true;
        else
        return false;
}

const handleRecall = (row:FormInstanceDto) =>{
  router.push({path: `/project/system/project-recall/${row.businessId}`});
}
</script>

<style lang="scss" scoped>

</style>
