<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>分配项目信息/Assign project</span>
        </div>
      </template>
      <FormCanvas :formSchema="formSchema" render-mode="view" />
      <el-form
        scroll-to-error
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
        ref="formRef"
        :model="assignProjectDto"
        :rules="currentRules"
        label-width="400px"
      >
        <el-form-item label="当前中级审核员" prop="currentThirdApprovalUser" v-if="currentThirdApprovalUserId">
          <span>{{ currentThirdApprovalUserName }} ({{ currentThirdApprovalUserAccount }})</span>
        </el-form-item>
        <el-form-item
          label="请指派中级/初级审核员"
          prop="userIdAndPosition"
        >
          <el-select
            v-model="assignProjectDto.userIdAndPosition"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="user in auditors"
              :key="user.key"
              :label="user.realName + '[' + user.username + ']'+'['+user.organizationNamePath+']'+'['+user.positionName+']'"
              :value="user.key+'-'+user.positionCode+'-'+user.organizationCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="退回原因" prop="description">
          <el-input
            type="textarea"
            ref="descriptionRef"
            v-model="assignProjectDto.description"
            :rows="8"
            clearable
            placeholder="请填写退回原因"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
  <!-- 悬浮操作按钮 -->
  <div class="floating-actions">
    <el-button @click="handleSave" type="primary" :loading="loading"
    >保存/Save
    </el-button
    >
    <el-button @click="handleReturn" type="danger" :loading="loading"
    >退回/Return
    </el-button
    >
    <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElMessage, ElLoading, FormInstance, FormRules} from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";

import {getProject} from "@/api/itmctr";
import {assignProject,returnProjectLevel2} from "@/api/itmctr-mgt";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";


import {getManagedPositionUsers} from "@/api/rbac-mgt";
import {AssignProjectDto} from "@/dtos/itmctr";

const router = useRouter();
const route = useRoute();

// 表单数据
const formSchema = ref<FormDefinitionDto>();

const loading = ref<boolean>(false);

const auditors = ref<any[]>([]);

const currentThirdApprovalUserName = ref<string>("");
const currentThirdApprovalUserAccount = ref<string>("");
const currentThirdApprovalUserId = ref<string>("");


const assignProjectDto = ref<AssignProjectDto>({
  userId: undefined,
  userIdAndPosition: undefined,
  positionCode: undefined,
  description: undefined,
});

const formRef = ref<FormInstance>();


// 保存按钮的验证规则
const saveRules: FormRules = {
  userIdAndPosition: [
    { required: true, message: "请选择中级/初级审核员", trigger: "blur" },
  ],
};

// 退回按钮的验证规则
const returnRules: FormRules = {
  description: [
    { required: true, message: "请填写退回原因", trigger: "blur" },
  ],
};

const currentRules = ref<FormRules>(saveRules);



const businessId = computed(() => {
  return route.params.businessId;
});


const loadAuditors = async () => {

  let loadingInstance: any;
  try {

    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取三级/四级审核员
    const thirdAuditors = await getManagedPositionUsers("CHECKER_LEVEL_3");
    const fourthAuditors = await getManagedPositionUsers("CHECKER_LEVEL_4");

    // 合并三级和四级审核员
    auditors.value = [
      ...(thirdAuditors.data.flatMap(q => q.users.map(u => {
        u.organizationCode = q.organization.code;
        //如果namePath开头有"/"则去掉开头的"/"
        u.organizationNamePath = q.organization.namePath.startsWith("/") ? q.organization.namePath.substring(1) : q.organization.namePath;
        u.positionName = "中级审核员";
        u.positionCode = "CHECKER_LEVEL_3";
        return u;
      })) || []),
      ...(fourthAuditors.data.flatMap(q => q.users.map(u => {
        u.organizationCode = q.organization.code;
        u.organizationNamePath = q.organization.namePath.startsWith("/") ? q.organization.namePath.substring(1) : q.organization.namePath;
        u.positionName = "初级审核员";
        u.positionCode = "CHECKER_LEVEL_4";
        return u;
      })) || []),
    ];

    loading.value = false;
  } catch (error: any) {
    console.error("获取中级/初级审核员失败:", error);
    ElMessage.error(`获取中级/初级审核员失败: ${error.message || error}`);
  } finally {
    if (loadingInstance) loadingInstance.close();
  }
};

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = await getProject(String(businessId.value));
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;

      currentThirdApprovalUserName.value = formSchema.value.formData["ThirdApprovalUserName"];
      currentThirdApprovalUserAccount.value = formSchema.value.formData["ThirdApprovalUserAccount"];
      currentThirdApprovalUserId.value = formSchema.value.formData["ThirdApprovalUserId"];

      loading.value = false;
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    ElMessage.error(`获取表单定义失败: ${error.message || error}`);
  } finally {
    if (loadingInstance) loadingInstance.close();
  }
};
const handleBack = () => {
  router.go(-1);
};

const handleReturn =async()=>{
  loading.value = true;
  if (!formRef.value) {
    return;
  }

  currentRules.value = returnRules;
  try {
    await formRef.value.validate();
  } catch (error) {
    loading.value = false;
    return;
  }

  try {
    // const split = assignProjectDto.value.userIdAndPosition.split("-");
    // assignProjectDto.value.organizationCode = split[2];
    // assignProjectDto.value.positionCode = split[1];
    // assignProjectDto.value.userId = split[0];
    //
    await returnProjectLevel2(String(businessId.value), assignProjectDto.value);
    router.go(-1);
  } catch (error) {
    ElMessage.error("退回失败");
  } finally {
    loading.value = false;
  }
};
const handleSave = async () => {
  loading.value = true;
  if (!formRef.value) {
    return;
  }

  currentRules.value = saveRules;
  try {
    await formRef.value.validate();
  } catch (error) {
    loading.value = false;
    return;
  }

  try {
    const split = assignProjectDto.value.userIdAndPosition.split("-");
    assignProjectDto.value.organizationCode = split[2];
    assignProjectDto.value.positionCode = split[1];
    assignProjectDto.value.userId = split[0];

    await assignProject(String(businessId.value), assignProjectDto.value);
    router.go(-1);
  } catch (error) {
    console.error("分配项目失败", error);
    ElMessage.error("分配项目失败");
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载表单
onMounted(async () => {
  await loadAuditors();
  await loadProject();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}

// 悬浮操作按钮样式
.floating-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 50px;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  padding: 12px 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
