<template>
  <div class="project-extract-step-panel">
    <!-- 步骤条 -->
    <el-steps :active="activeStep" align-center>
      <el-step title="上传文件" />
      <el-step title="文件校验" />
      <el-step title="AI填充" />
    </el-steps>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 步骤1：上传文件 -->
      <div v-if="activeStep === 0">
        <div class="upload-block-center">
          <div class="upload-title-block">
            <div class="upload-main-title">上传文件</div>
            <div class="upload-sub-title">请上传以下必要文件：</div>
          </div>
          <div class="file-uploader-cards-custom">
            <div
              class="file-card-custom"
              v-for="item in fileTypes"
              :key="item.code"
            >
              <div class="file-type-title">
                {{ item.label }}<span class="required">*</span>
              </div>
              <el-icon class="upload-icon-big"><UploadFilled /></el-icon>
              <div class="upload-desc">点击或拖拽文件到此处上传</div>

              <FileUploader
                v-model:files="fileMap[item.code]"
                :multiple="false"
                language="zh"
                type="file"
                :disabled="false"
                :typeCode="item.typeCode"
              />
              <div v-if="latestFile(item.code)" class="uploaded-item-custom">
                <el-icon size="large"><Document /></el-icon>
                <span class="file-name-custom"
                  >{{ item.label }}: {{ latestFile(item.code).fileName }}</span
                >
                <span class="file-size-custom">{{
                  formatSize(latestFile(item.code).fileSize)
                }}</span>
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click="removeFile(item.code)"
                  >删除</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2：文件校验 -->
      <div v-else-if="activeStep === 1" class="check-block-center">
        <div class="check-title-block">
          <div class="check-main-title">文件校验</div>
          <div class="check-sub-title">系统正在验证您上传的文件：</div>
        </div>
        <div class="check-result-cards-row">
          <div
            v-for="item in fileTypes"
            :key="item.code"
            class="check-result-card"
            :class="precheckResult[item.code] ? 'success' : 'fail'"
          >
            <div class="icon-wrap">
              <el-icon v-if="precheckResult[item.code]" class="icon-success"
                ><CircleCheckFilled
              /></el-icon>
              <el-icon v-else class="icon-warning"><WarningFilled /></el-icon>
            </div>
            <div class="result-title">{{ item.label }}</div>
            <div
              class="result-status-label"
              :class="precheckResult[item.code] ? 'success' : 'fail'"
            >
              {{ precheckResult[item.code] ? "验证通过" : "验证不通过" }}
            </div>
            <div class="result-desc" v-if="precheckResult[item.code]">
              文件验证通过，可以进行信息提取。
            </div>
            <div class="result-desc" v-else>
              验证不通过，这可能不是一个{{
                item.label
              }}文件，或者请您重新上传清晰的扫描版附件。
            </div>
          </div>
        </div>
        <div
          class="check-bottom-hint"
          v-if="
            !precheckResult.ethics ||
            !precheckResult.protocol ||
            !precheckResult.consent
          "
        >
          <el-text type="primary"
            >您可以返回"上一步"重新上传文件，或者直接点击"下一步"按钮，大模型会根据可用附件尽可能的提取内容。</el-text
          >
        </div>
      </div>

      <!-- 步骤3：AI填充 -->
      <div v-else-if="activeStep === 2">
        <!-- <el-alert title="AI正在处理您的文件..." type="success" show-icon />
        <div v-if="extracting">
          <el-progress
            :percentage="extractProgressPercent"
            :status="extractDialogStatus==='done' ? 'success' : ''"
            style="margin: 24px 0"
          />
        </div>
        <div v-if="extractResult">
          <el-alert
            title="AI提取结果（JSON展示，实际可替换为表单）"
            type="info"
            :closable="false"
          />
          <pre style="background: #f8f8f8; padding: 16px; border-radius: 8px">{{
            JSON.stringify(extractResult, null, 2)
          }}</pre>
        </div> -->
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="step-footer-float">
      <div class="step-footer-inner">
        <el-button :disabled="activeStep === 0 || loading" @click="prevStep"
          >上一步</el-button
        >
        <el-button type="primary" :loading="loading" @click="nextStep">
          {{ activeStep === 2 ? "提交" : "下一步" }}
        </el-button>
      </div>
    </div>

    <el-dialog
      v-model="extractDialogVisible"
      width="420px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
      class="extract-progress-dialog"
    >
      <div class="extract-progress-content">
        <el-icon
          class="extract-progress-icon"
          style="font-size: 48px; margin-bottom: 8px"
        >
          <Loading />
        </el-icon>
        <div class="extract-progress-title">
          大语言模型正在推理您上传的相关附件，请您耐心等待
        </div>
        <div
          class="extract-progress-time"
          v-if="extractDialogStatus != 'timeout'"
        >
          预计时间{{
            Math.floor(EXTRACT_TOTAL_SECONDS / 60)
          }}分钟，当前剩余等待时间：{{ extractCountdownText }}
        </div>
        <el-progress
          v-if="extractDialogStatus != 'timeout'"
          :percentage="extractProgressPercent"
          :stroke-width="16"
          :show-text="false"
          style="margin: 24px 0 8px 0"
          color="#409eff"
          :status="extractDialogStatus === 'done' ? 'success' : ''"
        />
        <div
          class="extract-progress-percent"
          v-if="extractDialogStatus != 'timeout'"
        >
          {{ extractProgressPercent }}%
        </div>
        <div
          v-if="extractDialogStatus === 'timeout'"
          class="extract-progress-hint"
        >
          等待太久还没有结果？请尝试点击
          <el-button
            type="primary"
            size="small"
            :disabled="!extractDialogCanQuery"
            @click="handleExtractQuery"
            >去查询</el-button
          >
          获得最新进展。
          <span
            v-if="extractDialogQueryCooldown > 0"
            style="margin-left: 8px; color: #999"
            >{{ extractDialogQueryCooldown }}s后可再次查询</span
          >
        </div>
        <div v-if="extractDialogMsg" class="extract-progress-msg">
          {{ extractDialogMsg }}
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="extractConfirmDialogVisible"
      width="420px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
    >
      <div>
        <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px">
          提取完成
        </div>
        <div style="margin-bottom: 16px">
          大语言模型根据您上传的附件提取到了{{
            extractCount
          }}条内容，是否以此结果继续填报注册表单？
        </div>
        <el-alert
          type="warning"
          :closable="false"
          show-icon
          style="margin-bottom: 16px"
          title="大模型提取结果仅供参考，系统会将提取结果填充至对应输入框中，请您提交前务必仔细核对。"
        />
        <div style="text-align: right">
          <el-button @click="extractConfirmDialogVisible = false"
            >取消</el-button
          >
          <el-button type="primary" @click="handleExtractConfirm"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import FileUploader from "@/components/common/FileUploader.vue";
import {
  Document,
  CircleCheckFilled,
  WarningFilled,
  UploadFilled,
  Loading,
} from "@element-plus/icons-vue";
import { formatSize } from "@/utils/files";
import { createExtractProject } from "@/api/itmctr";
import { precheck, extract, extractProgress } from "@/api/form-recognition";
import { ElMessage, ElLoading } from "element-plus";
const router = useRouter();

const activeStep = ref(0);
const loading = ref(false);
const extractProgressPercent = ref(0);
const extractCountdown = ref(0); // 剩余秒数
const extractDialogVisible = ref(false);
const extractDialogStatus = ref<"waiting" | "timeout" | "done">("waiting");
const extractDialogMsg = ref("");
const extractDialogCanQuery = ref(true);
const extractDialogQueryCooldown = ref(0);
const precheckResult = ref<any>(null);
const taskId = ref<string | null>(null);
let extractTimer: any = null;
let extractQueryTimer: any = null;

// 新增：提取完成确认弹窗相关变量
const extractConfirmDialogVisible = ref(false);
const extractCount = ref(0);

// 变量化的时间
const EXTRACT_TOTAL_SECONDS = 8 * 60; // 8分钟
const EXTRACT_POLL_INTERVAL = 45; // 45秒

const fileTypes = [
  {
    label: "伦理委员会审批件",
    typeCode: "ethic_committee_approved_file",
    code: "ethics",
  },
  { label: "研究方案", typeCode: "study_protocol", code: "protocol" },
  { label: "知情同意书", typeCode: "informed_consent_file", code: "consent" },
];

const fileMap = ref<Record<string, any[]>>({
  ethics: [],
  protocol: [],
  consent: [],
});

function latestFile(code: string) {
  const arr = fileMap.value[code];
  return arr && arr.length > 0 ? arr[arr.length - 1] : null;
}

function removeFile(code: string) {
  fileMap.value[code] = [];
}

function prevStep() {
  if (activeStep.value > 0) activeStep.value--;
}

async function nextStep() {
  let loadingInstance: any;
  if (activeStep.value === 0) {
    // 校验是否所有文件都已上传
    const allUploaded = fileTypes.every((f) => latestFile(f.code));
    if (!allUploaded) {
      ElMessage.warning("请上传所有必要的文件");
      return;
    }
    // 调用precheck
    loading.value = true;
    try {
      loadingInstance = ElLoading.service({
        lock: true,
        text: "正在提交...",
        background: "rgba(0, 0, 0, 0.3)",
      });
      const context = {
        ethics: latestFile("ethics"),
        protocol: latestFile("protocol"),
        consent: latestFile("consent"),
      };
      const result = await precheck(context);
      const data = result.data ?? result;
      precheckResult.value = data;
      taskId.value = data.taskId ?? null;
      loadingInstance.close();
      // if (data.ethics && data.protocol && data.consent) {
      //   ElMessage.success("所有文件校验通过");
      // }
      activeStep.value++;
    } catch (e: any) {
      ElMessage.error("文件校验失败：" + (e?.message || e));
      if (loadingInstance) loadingInstance.close();
    } finally {
      loading.value = false;
    }
    return;
  }
  if (activeStep.value === 1) {
    // AI提取
    if (!taskId.value) {
      ElMessage.error("缺少任务ID，无法提取");
      return;
    }
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在发起请求...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    try {
      await extract(taskId.value);
      loadingInstance.close();
      showExtractDialog();
    } catch (e: any) {
      ElMessage.error("AI提取请求失败：" + (e?.message || e));
      if (loadingInstance) loadingInstance.close();
    }
    return;
  }
  if (activeStep.value === 2) {
    // 最后一步提交逻辑
    ElMessage.success("提交成功！");
  }
}
async function handleExtractProcess(taskId: any) {
  try {
    const res = await extractProgress(taskId);
    return res.data;
  } catch (e) {
    return -1;
  }
}

function showExtractDialog() {
  extractDialogVisible.value = true;
  extractDialogStatus.value = "waiting";
  extractDialogMsg.value = "";
  extractCountdown.value = EXTRACT_TOTAL_SECONDS;
  extractProgressPercent.value = 0;
  extractDialogCanQuery.value = true;
  extractDialogQueryCooldown.value = 0;
  clearInterval(extractTimer);
  clearInterval(extractQueryTimer);
  extractTimer = setInterval(async () => {
    extractCountdown.value--;
    extractProgressPercent.value = Math.round(
      ((EXTRACT_TOTAL_SECONDS - extractCountdown.value) /
        EXTRACT_TOTAL_SECONDS) *
        100
    );
    // 每45秒轮询一次getProgress
    if (
      (EXTRACT_TOTAL_SECONDS - extractCountdown.value) %
        EXTRACT_POLL_INTERVAL ===
        0 ||
      extractCountdown.value === 0
    ) {
      const count = await handleExtractProcess(taskId.value!);
      if (count >= 0) {
        extractDialogStatus.value = "done";
        closeExtractDialog(count);
        return;
      }
    }
    if (extractCountdown.value <= 0) {
      extractDialogStatus.value = "timeout";
      clearInterval(extractTimer);
    }
  }, 1000);
}

function closeExtractDialog(count?: number) {
  clearInterval(extractTimer);
  clearInterval(extractQueryTimer);
  extractDialogVisible.value = false;
  extractDialogStatus.value = "waiting";
  extractDialogMsg.value = "";
  extractDialogCanQuery.value = true;
  extractDialogQueryCooldown.value = 0;
  extractCountdown.value = 0;
  extractProgressPercent.value = 0;
  if (typeof count === "number") {
    extractCount.value = count;
    extractConfirmDialogVisible.value = true;
  }
}

async function handleExtractQuery() {
  if (!extractDialogCanQuery.value) return;
  extractDialogCanQuery.value = false;
  extractDialogMsg.value = "";

  const count = await handleExtractProcess(taskId.value!);
  if (count < 0) {
    extractDialogMsg.value = "未查询成功，请稍后重试";
    extractDialogQueryCooldown.value = EXTRACT_POLL_INTERVAL;
    extractQueryTimer = setInterval(() => {
      extractDialogQueryCooldown.value--;
      if (extractDialogQueryCooldown.value <= 0) {
        extractDialogCanQuery.value = true;
        clearInterval(extractQueryTimer);
      }
    }, 1000);
  } else {
    // 查询成功，直接关闭弹窗
    closeExtractDialog(count);
  }
}

// 新增：确认弹窗点击"确定"进入下一步
async function handleExtractConfirm() {
  // extractConfirmDialogVisible.value = false;
  // activeStep.value++;

  let loadingInstance = ElLoading.service({
    lock: true,
    text: "正在创建...",
    background: "rgba(0, 0, 0, 0.3)",
  });
  var businessId = await createExtractProject(taskId.value!);
  router.push({ path: `/project/user/project-add/${businessId.data}` });
  loadingInstance.close();
}

const extractCountdownText = computed(() => {
  const min = Math.floor(extractCountdown.value / 60)
    .toString()
    .padStart(2, "0");
  const sec = (extractCountdown.value % 60).toString().padStart(2, "0");
  if (min == "-1" || sec == "-1") {
    return "--";
  }
  return `${min}:${sec}`;
});
</script>

<style scoped>
.app-main {
  padding: 50px;
}
.project-extract-step-panel {
  width: 100%;
  padding: 32px 0;
}
.step-content {
  margin: 32px 0 24px 0;
}
.upload-block-center {
  max-width: 1200px;
  margin: 0 auto 32px auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.upload-title-block {
  width: 100%;
  text-align: left;
  margin-bottom: 32px;
}
.upload-main-title {
  font-size: 28px;
  font-weight: bold;
  color: #222;
  margin-bottom: 4px;
}
.upload-sub-title {
  font-size: 16px;
  color: #444;
  font-weight: 500;
}
.file-uploader-cards-custom {
  display: flex;
  justify-content: center;
  gap: 40px;
  width: 100%;
}
.file-card-custom {
  flex: 1;
  min-width: 320px;
  max-width: 400px;
  min-height: 260px;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  background: #fafbfc;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 32px 16px 16px 16px;
  position: relative;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.file-card-custom:hover {
  border-color: #409eff;
  background: #f4f8ff;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.08);
}
.uploaded-item-custom {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 4px;
  padding: 10px 14px;
  margin-top: 18px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  width: 100%;
  border: 1px solid #e0e0e0;
}
.file-name-custom {
  margin-left: 8px;
  font-weight: 500;
  flex: 1;
  font-size: 15px;
}
.file-size-custom {
  color: #909399;
  font-size: 13px;
  margin-left: 8px;
  margin-right: 8px;
}
.step-footer-float {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.06);
  padding: 18px 0 18px 0;
  z-index: 100;
  display: flex;
  justify-content: center;
}
.step-footer-inner {
  display: flex;
  gap: 32px;
  justify-content: center;
  align-items: center;
}
@media (max-width: 1100px) {
  .upload-block-center {
    max-width: 100%;
    padding: 0 8px;
  }
  .file-uploader-cards-custom {
    flex-direction: column;
    align-items: center;
    gap: 24px;
  }
}
/* 校验区整体居中块 */
.check-block-center {
  max-width: 1200px;
  margin: 0 auto 32px auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.check-title-block {
  width: 100%;
  text-align: left;
  margin-bottom: 32px;
}
.check-main-title {
  font-size: 28px;
  font-weight: bold;
  color: #222;
  margin-bottom: 4px;
}
.check-sub-title {
  font-size: 16px;
  color: #444;
  font-weight: 500;
}
.check-result-cards-row {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin: 32px 0 0 0;
}
.check-result-card {
  flex: 1;
  min-width: 320px;
  max-width: 400px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 36px 16px 28px 16px;
  border: 2px solid #e0e0e0;
  background: #fff;
  transition: box-shadow 0.2s, border-color 0.2s, background 0.2s;
}
.check-result-card.success {
  /* border: 2px solid #67c23a; */
  /* background: #f6fff4; */
}
.check-result-card.fail {
  /* border: 2px solid #f56c6c; */
  /* background: #fff6f6; */
}
.icon-wrap {
  margin-bottom: 12px;
}
.icon-success,
.icon-warning {
  font-size: 44px;
}
.icon-success {
  color: #67c23a;
}
.icon-warning {
  color: #f56c6c;
}
.result-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}
.result-status-label {
  display: inline-block;
  padding: 2px 18px;
  border-radius: 16px;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 10px;
  margin-top: 2px;
}
.result-status-label.success {
  /* background: #e6f4ea; */
  color: #1e8e3e;
  border: 1px solid #67c23a;
}
.result-status-label.fail {
  /* background: #fce8e6; */
  color: #d93025;
  border: 1px solid #f56c6c;
}
.result-desc {
  font-size: 14px;
  color: #666;
  margin-top: 6px;
  text-align: center;
  min-height: 36px;
}
.check-bottom-hint {
  margin-top: 32px;
  background: #f5f6fa;
  color: #333;
  border-radius: 8px;
  padding: 18px 24px;
  font-size: 15px;
  text-align: left;
  max-width: 1200px;
  box-sizing: border-box;
}
/* 上传卡片顶部类型名和图标 */
.file-type-title {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 12px;
  margin-top: 4px;
}
.required {
  color: #f56c6c;
  margin-left: 2px;
}
.upload-icon-big {
  display: block;
  margin: 0 auto 12px auto;
  font-size: 44px;
  color: #909399;
}
.upload-desc {
  color: #444;
  font-size: 15px;
  margin-bottom: 4px;
  text-align: center;
}
.upload-hint {
  color: #b0b0b0;
  font-size: 13px;
  margin-bottom: 12px;
  text-align: center;
}
.extract-progress-dialog >>> .el-dialog__header {
  display: none;
}
.extract-progress-content {
  text-align: center;
  padding: 8px 0 0 0;
}
.extract-progress-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  margin-top: 4px;
}
.extract-progress-time {
  font-size: 15px;
  color: #666;
  margin-bottom: 8px;
}
.extract-progress-percent {
  font-size: 16px;
  color: #409eff;
  font-weight: 600;
  margin-bottom: 8px;
}
.extract-progress-hint {
  margin-top: 12px;
  color: #666;
  font-size: 14px;
}
.extract-progress-msg {
  color: #d93025;
  margin-top: 8px;
  font-size: 14px;
}
</style>
