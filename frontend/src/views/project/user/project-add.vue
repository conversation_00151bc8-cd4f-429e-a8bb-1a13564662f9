<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="page-with-sticky-actions">
    <div class="app-container flex-layout page-content">
      <canvas class="annotation-canvas"></canvas>
      <!-- 左侧表单区 -->
      <div class="form-area">
        <el-card class="table-card">
          <el-row v-if="isReject">
            <el-col :span="4">
              <el-header>驳回原因：</el-header>
            </el-col>
            <el-col :span="20">
              <el-text style="white-space: pre-line;">{{ rejectReason }}</el-text>
            </el-col>
          </el-row>
          <template #header>
            <div class="card-header">
              <span>新项目注册/Create a new project</span>
            </div>
          </template>
          <FormCanvas
            ref="formCanvasRef"
            :formSchema="formSchema"
            render-mode="add"
            @update:field="onUpdateField"
          />
        </el-card>
        <!-- 悬浮智能填写/AI翻译按钮（fixed定位，始终左侧中间） -->
        <div
          class="side-affix"
          v-if="formSchema?.language != 'en' && unicomEnabled"
        >
          <el-tour
            v-model="openTour"
            @finish="clearFirstTour"
            @close="clearFirstTour"
          >

            <el-tour-step
              target="#smartFill"
              title="智能填写"
            >
              <slot>
                <p>
                  该服务基于
                  <b>DeepseekR1</b>
                  推理大模型。该服务会要求您先上传伦理委员会审批件、研究方案、知情同意书附件。
                </p>
                <p>
                  大语言模型会根据您上传的附件进行内容提取和整理，提取完成后会将信息帮您填充进注册表单中。
                </p>
              </slot>
            </el-tour-step>
            <el-tour-step
              target="#aiTranslate"
              title="智能翻译"
            >
              <slot>
                <p>
                  该服务基于DeepseekR1推理大模型，您可以在中文输入框中填写中文后点击右侧的"魔术棒"按钮来使用基于大模型的AI翻译服务，翻译完成后我们会将结果帮您填入对应的英文输入框中。
                </p>
                <p>
                  您亦可以使用"全文翻译"功能在填写完中文表单后，一键翻译英文表单结果。
                </p>
              </slot>
            </el-tour-step>
          </el-tour>
          <div class="affix-btn-group">
            <div id="smartFill">
              <el-button
                class="affix-btn smart-fill-btn"
                circle
                @click="showSmartFillDialog = true"
              >
                <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zm-7-2h2V7h-4v2h2v8z"
                    fill="currentColor"
                  />
                </svg>
              </el-button>
              <div class="affix-btn-label">智能填写</div>
            </div>
            <div id="aiTranslate">
              <el-button
                ref=""
                v-if="formSchema?.language === 'both'"
                class="affix-btn ai-translate-btn"
                circle
                style="margin-top: 8px"
                @click="showTranslateDialog = true"
              >
                <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"
                    fill="currentColor"
                  />
                </svg>
              </el-button>
              <div v-if="formSchema?.language === 'both'" class="affix-btn-label">
                AI翻译
              </div>
            </div>

          </div>
        </div>
      </div>
      <!-- 右侧批注区 -->
      <div
        class="annotation-panel"
        style="position: relative"
        v-if="
          formSchema != null &&
            formSchema.language === 'both' &&
            annotationList.length > 0
        "
      >
        <!-- <div class="annotation-title">批注信息</div> -->
        <div
          v-for="item in annotationList"
          :key="item.key"
          :id="'annotation-' + item.key"
          class="annotation-item"
          :style="{
            position: 'absolute',
            left: '0',
            width: '100%',
            top: (annotationBoxTops[item.key] || 0) + 'px',
          }"
        >
          <div class="annotation-header">
            <span class="annotation-label">{{ item.label }}</span>
            <el-button
              icon="Edit"
              type="primary"
              size="small"
              class="fill-btn"
              @click="handleFillAnnotation(item)"
            >填充
            </el-button>
          </div>
          <el-input
            type="textarea"
            v-model="annotationEditMap[item.key]"
            size="small"
            class="annotation-input"
            :placeholder="'请输入批注内容'"
          />
        </div>
      </div>
      <!-- 智能填写提示弹窗 -->
      <el-dialog
        v-model="showSmartFillDialog"
        width="420px"
        :show-close="false"
        align-center
      >
        <div class="smart-fill-dialog-content">
          <p>
            该服务基于
            <b>DeepseekR1</b>
            推理大模型。该服务会要求您先上传伦理委员会审批件、研究方案、知情同意书附件。
          </p>
          <p>
            大语言模型会根据您上传的附件进行内容提取和整理，提取完成后会将信息帮您填充进注册表单中。
          </p>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showSmartFillDialog = false">取消</el-button>
            <el-button type="primary" @click="handleSmartFillConfirm"
            >确定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <!-- AI翻译提示弹窗 -->
      <el-dialog
        v-model="showTranslateDialog"
        width="480px"
        :show-close="false"
        align-center
      >
        <div class="ai-translate-dialog-content">
          <p>
            该服务基于DeepseekR1推理大模型，您可以在中文输入框中填写中文后点击右侧的"魔术棒"按钮来使用基于大模型的AI翻译服务，翻译完成后我们会将结果帮您填入对应的英文输入框中。
          </p>
          <p>
            您亦可以使用"全文翻译"功能在填写完中文表单后，一键翻译英文表单结果。
          </p>
          <el-alert
            type="error"
            :closable="false"
            show-icon
            style="margin: 12px 0 0 0"
            title="请注意，大模型生成内容仅供参考，请您在提交前务必进行校对。"
          />
        </div>
        <template #footer>
          <el-button type="primary" @click="handleTranslateAll"
          >使用全文翻译
          </el-button>
          <el-button @click="showTranslateDialog = false">关闭</el-button>
          <el-button @click="showTranslateDialog = false">我知道了</el-button>
        </template>
      </el-dialog>
      <el-dialog
        v-model="showTranslateTipDialog"
        width="420px"
        :show-close="false"
        align-center
      >
        <div class="translate-tip-dialog-content">
          <div class="tip-header">
            <el-icon class="tip-icon" color="#409eff" style="font-size: 32px">
              <InfoFilled />
            </el-icon>
            <span class="tip-title">温馨提示</span>
          </div>
          <div class="tip-body">
            <p>
              为了不干扰您的填写，我们会临时禁用掉英语相关输入框；<br />
              在您填写完所有中文信息后可以点击页面下方的
              <b>"全文翻译"</b> 按钮进行翻译操作。
            </p>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="handleTranslateTipConfirm"
            >我知道了</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog
        v-model="contentTranslateDialogVisible"
        width="420px"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        align-center
      >
        <div class="extract-progress-content">
          <el-icon
            class="extract-progress-icon"
            style="font-size: 48px; margin-bottom: 18px"
          >
            <Loading />
          </el-icon>
          <div class="extract-progress-title">
            大语言模型正在进行全文翻译，请您耐心等待
          </div>
          <div
            class="extract-progress-time"
            v-if="contentTranslateDialogStatus != 'timeout'"
          >
            预计时间{{
              Math.floor(CONTENT_TRANSLATE_TOTAL_SECONDS / 60)
            }}分钟，当前剩余等待时间：{{ contentTranslateCountdownText }}
          </div>
          <el-progress
            v-if="contentTranslateDialogStatus != 'timeout'"
            :percentage="contentTranslateProgressPercent"
            :stroke-width="16"
            :show-text="false"
            style="margin: 0 auto 8px auto; width: 90%; border-radius: 8px"
            color="#409eff"
            :status="contentTranslateDialogStatus === 'done' ? 'success' : ''"
          />
          <div
            class="extract-progress-percent"
            v-if="contentTranslateDialogStatus != 'timeout'"
          >
            {{ contentTranslateProgressPercent }}%
          </div>
          <div
            v-if="contentTranslateDialogStatus === 'timeout'"
            class="extract-progress-hint"
          >
            等待太久还没有结果？请尝试点击
            <el-button
              type="primary"
              size="small"
              :disabled="!contentTranslateDialogCanQuery"
              @click="handleContentTranslateQuery"
            >去查询
            </el-button>
            获得最新进展。
            <span
              v-if="contentTranslateDialogQueryCooldown > 0"
              style="margin-left: 8px; color: #999"
            >
              {{ contentTranslateDialogQueryCooldown }}s后可再次查询
            </span>
          </div>
          <div v-if="contentTranslateDialogMsg" class="extract-progress-msg">
            {{ contentTranslateDialogMsg }}
          </div>
        </div>
      </el-dialog>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
      <el-button @click="handleSave" :loading="loading">保存/Save</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading"
      >提交/Submit
      </el-button>
      <el-button
        v-if="showBottomTranslateBtn && formSchema?.language === 'both'"
        type="primary"
        style="background: #1976d2; border-color: #1976d2"
        @click="executeContentTranslate"
        :loading="loading"
      >
        <svg
          style="vertical-align: middle; margin-right: 4px"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"
            fill="currentColor"
          />
        </svg>
        全文翻译
      </el-button>
    </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  computed,
  provide,
  nextTick,
  onUnmounted,
  watch,
} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElMessage, ElLoading, ElMessageBox} from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";
import {InfoFilled} from "@element-plus/icons-vue";
// import LeaderLine from "leader-line";
import "leader-line";
import {useAppStore} from "@/stores/app";

import {
  getProjectDefinition,
  getProject,
  createProject,
  submitProject,
  saveProject,
} from "@/api/itmctr";
import {FormDefinitionDto, FieldDto} from "@/dtos/dynamic-form.dto";
import {
  contentTranslateProgress,
  applyContentTranslate,
  getContentTranslateResult,
  realtimeTranslate,
} from "@/api/form-recognition";

const router = useRouter();
const route = useRoute();

const openTour = ref<boolean>(false);

const rejectReason = ref<string>("");

const isReject = ref<boolean>(false);

const unicomEnabled = import.meta.env.VITE_ENABLE_UNICOM === "true";

// 表单数据
const formSchema = ref<FormDefinitionDto>();


watch(
    () => formSchema.value,
    (newVal) => {
      if (newVal) {

        isReject.value = newVal.formData?.ProcessStatus == "RejectToApply";

        if (newVal.formData && newVal.formData.ApprovalHistory) {

          let approvalHistory = JSON.parse(newVal.formData.ApprovalHistory);
          //将approvalHistory按OperateTime降序排序后取第一个Action="Reject"的数据
          approvalHistory = approvalHistory
              .filter((item: any) => item.Action === "Reject")
              .sort((a: any, b: any) => new Date(b.OperateTime) - new Date(a.OperateTime));
          if (approvalHistory.length > 0) {
            rejectReason.value = approvalHistory[0].Description;
          } else {
            rejectReason.value = "";
          }
        }
      }
    },
    {immediate: true}
);


// 字段更新处理
const onUpdateField = (field: any) => {
  console.log("字段更新:", field);
  // 实现字段更新逻辑
};

const loading = ref<boolean>(false);

const businessId = computed(() => {
  return savedBusinessId.value ?? route.params.businessId;
});

const showSmartFillDialog = ref(false);
const showTranslateDialog = ref(false);
const showBottomTranslateBtn = ref(false);
const showTranslateTipDialog = ref(false);
const contentTranslateDialogVisible = ref(false);
const contentTranslateDialogStatus = ref<"waiting" | "timeout" | "done">(
    "waiting"
);
const contentTranslateDialogMsg = ref("");

const CONTENT_TRANSLATE_TOTAL_SECONDS = 8 * 60; // 8分钟
const CONTENT_TRANSLATE_POLL_INTERVAL = 45; // 45秒

const contentTranslateProgressPercent = ref(0);
const contentTranslateCountdown = ref(CONTENT_TRANSLATE_TOTAL_SECONDS); // 8分钟
const contentTranslateDialogCanQuery = ref(true);
const contentTranslateDialogQueryCooldown = ref(0);
let contentTranslateTimer: any = null;
let contentTranslateQueryTimer: any = null;
let contentTranslateRequestId: string | null = null;

// 1. 批注收集逻辑（提前声明）
const annotationEditMap = ref<Record<string, string>>({});
const annotationList = computed(() => {
  const list: {
    key: string;
    label: string;
    content: string;
    fieldRef: any;
    rowIndex?: number;
  }[] = [];
  if (!formSchema.value || !formSchema.value.groups) return list;
  for (const group of formSchema.value.groups) {
    if (!group.fields) continue;
    for (const field of group.fields) {
      collectFieldAnnotations(null, null, field, group, list);
    }
  }
  // 初始化输入内容
  list.forEach((item) => {
    if (annotationEditMap.value[item.key] === undefined) {
      annotationEditMap.value[item.key] = item.content;
    }
  });
  return list;
});

// 1. 维护控件DOM注册表和连线实例
const fieldDomMap = ref(new Map<string, HTMLElement>());
// const lines = ref<any[]>([]);

// 2. 提供注册方法，供 BaseField 注入调用
function registerFieldDom(key: string, el: HTMLElement) {
  fieldDomMap.value.set(key, el);
  nextTick(drawLinesOnCanvas);
}

provide("registerFieldDom", registerFieldDom);

// 3. 用 canvas 画线
function drawLinesOnCanvas() {
  const canvas = document.querySelector(
      ".annotation-canvas"
  ) as HTMLCanvasElement;
  if (!canvas) return;
  const ctx = canvas.getContext("2d");
  if (!ctx) return;
  // 适配高分屏
  const dpr = window.devicePixelRatio || 1;
  canvas.width = window.innerWidth * dpr;
  canvas.height = window.innerHeight * dpr;
  canvas.style.width = window.innerWidth + "px";
  canvas.style.height = window.innerHeight + "px";
  ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  annotationList.value.forEach((item) => {
    const fieldEl = fieldDomMap.value.get(item.key);
    const annotationEl = document.getElementById("annotation-" + item.key);
    if (fieldEl && annotationEl) {
      const fieldRect = fieldEl.getBoundingClientRect();
      const annotationRect = annotationEl.getBoundingClientRect();
      const x1 = fieldRect.right + 30;
      const y1 = fieldRect.top + fieldRect.height / 2;
      const x2 = annotationRect.left;
      const y2 = annotationRect.top + annotationRect.height / 2;
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.lineTo(x2, y2);
      ctx.strokeStyle = "#409eff";
      ctx.lineWidth = 1;
      ctx.stroke();
    }
  });
}

// 4. annotationList 变化时重绘
watch(annotationList, () => {
  nextTick(() => {
    updateAnnotationBoxTops();
    nextTick(() => {
      drawLinesOnCanvas();
    });
  });
});

// 新增：批注框top定位
const annotationBoxTops = ref<Record<string, number>>({});

function updateAnnotationBoxTops() {
  const tops: Record<string, number> = {};
  const minGap = 120; // 批注框最小间距
  const panel = document.querySelector(".annotation-panel");
  if (!panel) return;
  // 1. 获取所有控件的中心点（相对于 annotation-panel 顶部）
  const positions = annotationList.value.map((item) => {
    const fieldEl = fieldDomMap.value.get(item.key);
    if (!fieldEl) return {key: item.key, center: 0};
    const fieldRect = fieldEl.getBoundingClientRect();
    const panelRect = panel.getBoundingClientRect();
    const center = fieldRect.top + fieldRect.height / 2 - panelRect.top;
    return {key: item.key, center};
  });
  // 2. 按 center 排序
  positions.sort((a, b) => a.center - b.center);
  // 3. 避障：保证每个批注框之间有 minGap 间距
  let lastBottom = 0;
  positions.forEach((pos, idx) => {
    let top = Math.max(pos.center - 55, lastBottom); // 30为批注框高度一半
    tops[pos.key] = top;
    lastBottom = top + minGap;
  });
  annotationBoxTops.value = tops;
}

// 滚动/resize 事件防抖处理
let scrollRafId: number | null = null;

function onScrollOrResize() {
  if (scrollRafId !== null) cancelAnimationFrame(scrollRafId);
  scrollRafId = requestAnimationFrame(() => {
    updateAnnotationBoxTops();
    drawLinesOnCanvas();
    scrollRafId = null;
  });
}

onMounted(() => {
  nextTick(() => {
    updateAnnotationBoxTops();
    nextTick(() => {
      drawLinesOnCanvas();
    });
  });
});
window.addEventListener("resize", onScrollOrResize);
window.addEventListener("scroll", onScrollOrResize, true);
onUnmounted(() => {
  window.removeEventListener("resize", onScrollOrResize);
  window.removeEventListener("scroll", onScrollOrResize, true);
});

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = businessId.value
        ? await getProject(String(businessId.value))
        : await getProjectDefinition();
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;

      if (unicomEnabled) {

        // 为多语言文本/多行文本字段添加魔术棒按钮配置
        traverseAllFields(formSchema.value, (parent, field) => {
          if (
              field.type === "text_multilang" ||
              field.type === "textarea_multilang"
          ) {
            field.readonly = {};
            field.zhButtonIcon = "🪄"; // 配置按钮图标
            field.zhButtonClick = handleZhMagicClick; // 配置点击事件处理函数
          }
        });
      }

      loading.value = false;

      const appStore = useAppStore();
      openTour.value = appStore.getFirstTour();

    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    // ElMessage.error(`获取表单定义失败: ${error.message || error}`);
  } finally {
    if (loadingInstance) loadingInstance.close();
  }
};

// 组件挂载时加载表单
onMounted(loadProject);

function clearFirstTour() {
  const appStore = useAppStore();
  appStore.setFirstTour();
  openTour.value = false;
}

// 保存表单
const handleSave = async () => {
  let loadingInstance = ElLoading.service({
    lock: true,
    text: "正在保存.../Saving...",
    background: "rgba(0, 0, 0, 0.3)",
  });
  await save();
  if (businessId.value) {
    ElMessage.success("保存成功/Saved successfully");
    router.push({
      path: `/project/user/project-add/${businessId.value}`,
    });
  }

  if (loadingInstance) loadingInstance.close();
};

const savedBusinessId = ref<string>();

async function save() {
  try {
    loading.value = true;

    const schemaData = JSON.parse(JSON.stringify(formSchema.value));

    schemaData.formData = {};

    const response = businessId.value
        ? await saveProject(String(businessId.value), schemaData)
        : await createProject(schemaData);

    loading.value = false;
    savedBusinessId.value = response.data;
    // return response.data;
  } catch (error: any) {
    // ElMessage.error(`保存失败: ${error.message || error}`);
    loading.value = false;
    // return null;
  }
}

// 提交表单
const handleSubmit = async () => {
  const result = formCanvasRef.value?.validateAll?.();
  if (!result?.valid) {
    const messages = result.messages.filter((q: any) => q.message);

    // 1. 普通字段错误
    const normalErrors: string[] = [];
    // 2. 子表格错误，分组：parent.code + rowIndex
    const subformErrorMap: Record<string, {
      parentLabel: string,
      rowIndex: number,
      errors: string[]
    }> = {};

    for (const e of messages) {
      if (e.rowIndex == null && e.parent == null) {
        // 普通字段
        normalErrors.push(`${(formSchema.value.language == 'both' || formSchema.value.language == 'zh') ? e.labelZh : e.labelEn}：${e.message}`);
      } else if (e.rowIndex != null && e.parent) {
        // 子表格字段
        const key = `${e.parent.code}_${e.rowIndex}`;
        if (!subformErrorMap[key]) {
          subformErrorMap[key] = {
            parentLabel: (formSchema.value.language == 'both' || formSchema.value.language == 'zh') ? e.parent.labelZh : e.parent.labelEn,
            rowIndex: e.rowIndex,
            errors: []
          };
        }
        subformErrorMap[key].errors.push(`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${(formSchema.value.language == 'both' || formSchema.value.language == 'zh') ? e.labelZh : e.labelEn}：${e.message}`);
      }
    }
    // 拼接子表格错误
    const subformErrors: string[] = [];
    for (const key in subformErrorMap) {
      const group = subformErrorMap[key];
      // 行号一般用户习惯从1开始
      const rowNum = (group.rowIndex ?? 0) + 1;
      const rowErrorMsg = group.errors.join('<br/>');
      subformErrors.push(`${group.parentLabel} #${rowNum}：<br/>${rowErrorMsg}`);
    }

    // 合并所有错误
    const allErrors = [...normalErrors, ...subformErrors];

    // console.log(allErrors);

    const errorHtml = `
      <div style="max-height:520px;overflow-y:auto;padding-right:8px;">
        ${allErrors.join('<br/>')}
      </div>
    `;

    await ElMessageBox.alert(errorHtml, '表单校验未通过/The form validation failed', {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定',
      customClass: 'form-validate-error-box'
    });
    return;
  }
  let loadingInstance = ElLoading.service({
    lock: true,
    text: "正在提交.../Submitting...",
    background: "rgba(0, 0, 0, 0.3)",
  });

  await save();

  if (businessId.value) {
    loading.value = true;
    try {
      await submitProject(String(businessId.value));
      // ElMessage.success("提交成功");
      await router.push({
        path: `/project/user/project-view/${businessId.value}`,
      });
      // window.location.href = `/project/project-view/${businessId.value}`;
      // router.replace({
      //   path: route.path,
      // });
      // 再提交

      loading.value = false;
    } catch (error: any) {
      // ElMessage.error(`提交失败: ${error.message || error}`);
      loading.value = false;

      //如果有businessId 但url中没有 此时说明为新增并且保存成功但未提交成功，此时强制跳转页面 保证刷新不会丢失数据
      if (businessId.value && !route.params.businessId) {
        await router.push({
          path: `/project/user/project-add/${businessId.value}`,
        });
      }
    } finally {
      if (loadingInstance) loadingInstance.close();
    }
  }
};

function handleSmartFillConfirm() {
  showSmartFillDialog.value = false;
  router.push({path: "/project/user/project-extract"});
}

function handleTranslateAll() {
  showTranslateDialog.value = false;
  showTranslateTipDialog.value = true;
}

function handleTranslateTipConfirm() {
  showTranslateTipDialog.value = false;
  showBottomTranslateBtn.value = true;
  traverseAllFields(formSchema.value!, (parent, field) => {
    if (field.type == "textarea_multilang" || field.type == "text_multilang") {
      field.placeholder ??= {};
      field.readonly ??= {};

      field.placeholder.en = "等待全文翻译";
      field.readonly.en = true;
    }
  });
}

async function executeContentTranslate() {
  try {
    await save();
    if (businessId.value) {
      loading.value = true;
      const res = await applyContentTranslate(String(businessId.value));
      contentTranslateRequestId = res.data ?? res;
      showContentTranslateDialog();
      loading.value = false;
    }
  } catch (e: any) {
    // ElMessage.error("发起全文翻译失败：" + (e?.message || e));
  }
}

function showContentTranslateDialog() {
  contentTranslateDialogVisible.value = true;
  contentTranslateDialogStatus.value = "waiting";
  contentTranslateDialogMsg.value = "";
  contentTranslateCountdown.value = CONTENT_TRANSLATE_TOTAL_SECONDS;
  contentTranslateProgressPercent.value = 0;
  contentTranslateDialogCanQuery.value = true;
  contentTranslateDialogQueryCooldown.value = 0;
  clearInterval(contentTranslateTimer);
  clearInterval(contentTranslateQueryTimer);
  contentTranslateTimer = setInterval(async () => {
    contentTranslateCountdown.value--;
    contentTranslateProgressPercent.value = Math.round(
        ((CONTENT_TRANSLATE_TOTAL_SECONDS - contentTranslateCountdown.value) /
            CONTENT_TRANSLATE_TOTAL_SECONDS) *
        100
    );
    // 每45秒轮询一次
    if (
        (CONTENT_TRANSLATE_TOTAL_SECONDS - contentTranslateCountdown.value) %
        CONTENT_TRANSLATE_POLL_INTERVAL ===
        0 ||
        contentTranslateCountdown.value === 0
    ) {
      // 轮询 contentTranslateProgress
      try {
        const res = await contentTranslateProgress(contentTranslateRequestId!);
        if (res?.data === true) {
          contentTranslateDialogStatus.value = "done";
          closeContentTranslateDialog();
          return;
        }
      } catch (e) {
      }
    }
    if (contentTranslateCountdown.value <= 0) {
      contentTranslateDialogStatus.value = "timeout";
      clearInterval(contentTranslateTimer);
    }
  }, 1000);
}

function closeContentTranslateDialog() {
  clearInterval(contentTranslateTimer);
  clearInterval(contentTranslateQueryTimer);
  contentTranslateDialogVisible.value = false;
  contentTranslateDialogStatus.value = "waiting";
  contentTranslateDialogMsg.value = "";
  contentTranslateDialogCanQuery.value = true;
  contentTranslateDialogQueryCooldown.value = 0;
  contentTranslateCountdown.value = 0;
  contentTranslateProgressPercent.value = 0;

  getContentTranslateResult(String(businessId.value)).then((res) => {
    var data = res.data ?? res;
    traverseAllFields(formSchema.value!, (parent, field) => {
      if (
          field.type == "textarea_multilang" ||
          field.type == "text_multilang"
      ) {
        field.readonly.en = null;
        field.placeholder.en = null;

        if (data[field.code]) {
          console.log(field.code + ":" + field.value);
          if (parent) {
            data[field.code].forEach((row: any, index: number) => {
              if (row && row.translate) {
                if (parent.value.length - 1 >= index) {
                  parent.value[index][field.code] ??= {};
                  if (
                      parent.value[index][field.code].en == null ||
                      parent.value[index][field.code].en.length == 0
                  ) {
                    parent.value[index][field.code].en = row.translate;
                  }
                }
              }
            });
          } else {
            if (field.value.en == null || field.value.en.length == 0) {
              field.value.en = data[field.code].translate;
            }
          }
          field.annotations = data[field.code];
          // if (isArray(data[field.code])) {
          //   data[field.code].forEach((row: any, index: number) => {
          //     field.value ??= [];
          //     field.value.splice(index, 0, {});
          //     if (row) {
          //       field.value[index].en = row.translate;
          //     }
          //   });
          // } else if (data[field.code]) {
          //   field.value.en = data[field.code].translate;
          // }
        }
      }
    });
  });
}

async function handleContentTranslateQuery() {
  if (!contentTranslateDialogCanQuery.value) return;
  contentTranslateDialogCanQuery.value = false;
  contentTranslateDialogMsg.value = "";
  const res = await contentTranslateProgress(contentTranslateRequestId!);
  if (!res?.data) {
    contentTranslateDialogMsg.value = "未查询成功，请稍后重试";
    contentTranslateDialogQueryCooldown.value = CONTENT_TRANSLATE_POLL_INTERVAL;
    contentTranslateQueryTimer = setInterval(() => {
      contentTranslateDialogQueryCooldown.value--;
      if (contentTranslateDialogQueryCooldown.value <= 0) {
        contentTranslateDialogCanQuery.value = true;
        clearInterval(contentTranslateQueryTimer);
      }
    }, 1000);
  } else {
    // 查询成功，直接关闭弹窗
    closeContentTranslateDialog();
  }
}

const contentTranslateCountdownText = computed(() => {
  const min = Math.floor(contentTranslateCountdown.value / 60)
      .toString()
      .padStart(2, "0");
  const sec = (contentTranslateCountdown.value % 60)
      .toString()
      .padStart(2, "0");
  if (min == "-1" || sec == "-1") {
    return "--";
  }
  return `${min}:${sec}`;
});

// 遍历formSchema中所有FieldDto并对每个调用callback
function traverseAllFields(
    formSchema: FormDefinitionDto,
    callback: (parent: FieldDto, field: FieldDto) => void
) {
  if (!formSchema || !formSchema.groups) return;
  for (const group of formSchema.groups) {
    if (!group.fields) continue;
    for (const field of group.fields) {
      traverseField(null, field, callback);
    }
  }
}

function traverseField(
    parent: FieldDto | any,
    field: FieldDto,
    callback: (parent: FieldDto, field: FieldDto) => void
) {
  callback(parent, field);
  // 递归遍历子表单/多行子表单等嵌套字段
  if (Array.isArray(field.fields)) {
    for (const subField of field.fields) {
      traverseField(field, subField, callback);
    }
  }
}

// 批注收集逻辑
function collectFieldAnnotations(
    parentLabel: string | null,
    parent: any | null,
    field: any,
    group: any,
    list: any[],
    rowIndex?: number
) {
  const label = parentLabel
      ? `${parentLabel} - ${field.labelZh || field.code}`
      : field.labelZh || field.code;
  // 子表单递归
  if (Array.isArray(field.value) && field.value.length > 0) {
    field.value.forEach((row: any, idx: number) => {
      if (Array.isArray(field.fields)) {
        field.fields.forEach((subField: any) => {
          collectFieldAnnotations(
              `${label} [第${idx + 1}行]`,
              field,
              subField,
              group,
              list,
              idx
          );
        });
      }
    });
  } else {
    let content = "";
    if (field.annotations) {
      if (Array.isArray(field.annotations) && typeof rowIndex === "number") {
        content = field.annotations[rowIndex]?.translate || "";
      } else if (
          typeof field.annotations === "object" &&
          field.annotations !== null &&
          "translate" in field.annotations
      ) {
        content = field.annotations.translate;
      }
    }
    // if (
    //   !content &&
    //   field.value &&
    //   typeof field.value === "object" &&
    //   "en" in field.value
    // ) {
    //   content = field.value.en;
    // }
    if (content) {
      list.push({
        key: field.code + (rowIndex !== undefined ? `_${rowIndex}` : ""),
        label,
        content,
        fieldRef: field,
        parentFieldRef: parent,
        rowIndex,
      });
    }
  }
}

// 填充按钮逻辑
function handleFillAnnotation(item: any) {
  const value = annotationEditMap.value[item.key];
  if (item.rowIndex !== undefined) {
    // 子表单
    const row = item.parentFieldRef.value[item.rowIndex][item.fieldRef.code];
    if (row && typeof row === "object") {
      row.en = value;

      item.fieldRef.annotations[item.rowIndex].translate = null;

      // ElMessage.success("已填充到子表单字段");
    }
  } else if (item.fieldRef && item.fieldRef.value) {
    // 普通多语言字段
    item.fieldRef.value.en = value;

    item.fieldRef.annotations.translate = null;
    // ElMessage.success("已填充到字段");
  } else {
    // ElMessage.warning("无法填充：字段结构异常");
  }
  // 可选：清空输入框内容
  // annotationEditMap.value[item.key] = '';
}

// 新增：处理中文输入框魔术棒按钮点击事件
async function handleZhMagicClick(field: any, value: any, rowIndex?: number) {
  // console.log("点击了中文魔术棒按钮", field, rowIndex, value);
  if (value && value.zh) {
    // TODO: 在这里实现魔术棒按钮的具体逻辑，例如触发 AI 翻译等

    // await new Promise((resolve) => setTimeout(resolve, 2000)); // 延迟2秒

    // value.en = value.zh + "-translated";

    field.placeholder ??= {};
    field.readonly ??= {};

    field.placeholder.en = "等待翻译结果";
    field.readonly.en = true;

    try {
      var rsp = await realtimeTranslate(value.zh);
      value.en = rsp.data;
    } catch (e: any) {
      // ElMessage.error("实时翻译失败" + (e?.message || e));
    } finally {
      field.placeholder.en = null;
      field.readonly.en = null;
    }
  }
}

const formCanvasRef = ref();
</script>

<style lang="scss" scoped>
html,
body,
#app,
.app-wrapper,
.main-content,
.app-container.flex-layout {
  height: 100%;
  min-height: 0;
}

.app-container.flex-layout {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  height: 100%;
  min-height: 0;
}

.form-area {
  flex: 1 1 0;
  min-width: 0;
  margin-right: 24px;
}

.annotation-panel {
  width: 300px;
  height: 100%;
  display: flex;
  flex-direction: column;
  // border: 1.5px solid #e3e8f0;
  border-radius: 14px;
  // box-shadow: 0 6px 24px rgba(64,158,255,0.06);
  // background: #fcfcfe;
  padding: 0 0 18px 0;
}

.annotation-title {
  font-weight: bold;
  font-size: 18px;
  color: #409eff;
  margin-bottom: 16px;
  padding-left: 10px;
  background: #f4f8ff;
  padding: 12px 0 10px 4px;
  border-radius: 10px 10px 0 0;
  border-bottom: 1px solid #e3e8f0;
}

.annotation-item {
  border: 1.2px solid #e3e8f0;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.04);
  padding: 14px 14px 10px 14px;
  margin-bottom: 18px;
  transition: box-shadow 0.2s, border-color 0.2s;
}

.annotation-item:hover {
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.1);
  border-color: #b3d8fd;
}

.annotation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.annotation-label {
  font-size: 15px;
  color: #666;
  font-weight: 500;
}

.annotation-input {
  width: 100%;
  font-size: 15px;
  min-height: 38px;
  background: #f7faff;
}

.fill-btn {
  margin-left: 8px;
  align-self: unset;
  margin-top: 0;
}



.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}

// 浮动操作按钮样式已移至布局插槽

.side-affix {
  position: fixed;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 200;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.affix-btn-group {
  //display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

#smartFill {
  text-align: center;
}

#aiTranslate {
  text-align: center;
  margin-top: 15px;
}

.affix-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  font-size: 22px;
  //display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 0;
}

.affix-btn-label {
  margin-top: 2px;
  margin-bottom: 4px;
  font-size: 12px;
  color: #fff;
  background: rgba(0, 0, 0, 0.18);
  border-radius: 10px;
  padding: 1px 8px;
  text-align: center;
  font-weight: 400;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  letter-spacing: 0;
  line-height: 1.2;
}

.smart-fill-btn {
  background: #13ce66;
  color: #fff;
  box-shadow: 0 0 12px 2px #13ce6688, 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s;
}

.smart-fill-btn:hover {
  box-shadow: 0 0 24px 6px #13ce66cc, 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ai-translate-btn {
  background: #409eff;
  color: #fff;
  box-shadow: 0 0 12px 2px #409eff88, 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s;
}

.ai-translate-btn:hover {
  box-shadow: 0 0 24px 6px #409effcc, 0 2px 8px rgba(0, 0, 0, 0.15);
}

.smart-fill-dialog-content {
  font-size: 16px;
  color: #333;
  line-height: 1.8;
  padding: 12px 18px 0 18px;
  text-align: left;
}

.smart-fill-dialog-content p {
  margin-bottom: 10px;
}

.ai-translate-dialog-content {
  font-size: 16px;
  color: #333;
  line-height: 1.8;
  padding: 12px 18px 0 18px;
  text-align: left;
}

.ai-translate-dialog-content p {
  margin-bottom: 10px;
}

.translate-tip-dialog-content {
  padding: 8px 18px 0 18px;
}

.tip-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.tip-icon {
  font-size: 32px;
  margin-right: 8px;
}

.tip-title {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.tip-body {
  font-size: 15px;
  color: #333;
  line-height: 1.9;
  text-align: left;
}

.tip-body p {
  margin: 0;
}

.extract-progress-content {
  padding: 24px 32px 18px 32px;
  text-align: center;
}

.extract-progress-icon {
  font-size: 48px;
  margin-bottom: 18px;
  color: #409eff;
}

.extract-progress-title {
  font-size: 20px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 12px;
  margin-top: 0;
  letter-spacing: 0.5px;
}

.extract-progress-time {
  font-size: 15px;
  color: #666;
  margin-bottom: 24px;
}

.el-progress {
  width: 90%;
  margin: 0 auto 8px auto;
  display: block;
  border-radius: 8px;
}

.extract-progress-percent {
  font-size: 15px;
  color: #409eff;
  margin-bottom: 8px;
}

.extract-progress-hint {
  font-size: 15px;
  color: #d77680;
  margin-top: 24px;
}

.extract-progress-msg {
  font-size: 15px;
  color: #333;
  margin-top: 8px;
}

.el-dialog {
  border-radius: 14px !important;
}

/* LeaderLine 全局样式修正，防止双滚动条 */
:global(.leader-line) {
  position: fixed !important;
  left: 0 !important;
  top: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  pointer-events: none;
  z-index: 9999;
  overflow: visible !important;
}

.annotation-canvas {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 10;
}

:deep(.form-validate-error-box) .el-message-box {
  width: 600px !important;
  max-width: 90vw;
  min-width: 600px;
}

// 页面容器样式已移至布局层面
</style>
