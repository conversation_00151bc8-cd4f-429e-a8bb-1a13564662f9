<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div>
    <div class="app-container">

      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="table-card">
            <template #header>
              <div class="card-header">
                <span>申请修改/Apply to edit</span>
              </div>
            </template>
            <template #default>
              <el-alert type="warning" title="请注意/Please note" show-icon :closable="false">
                <template #default>
                  请务必在提交修改申请后，与平台工作人员取得联系/Please contact the platform administrator after
                  submitting
                  the
                  modification application
                </template>
              </el-alert>
              <el-row>
                <el-col :span="labelWidth">
                  <div>注册号<br />Registration number</div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <el-text>{{ registrationNumber }}</el-text>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="labelWidth">
                  <div>注册题目<br />Public title</div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <el-text>{{ publicTitle }}</el-text>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="labelWidth">
                  <div>请上传支持试验修改的相应试验方案<br />Please upload the corresponding study protocol to support
                    the study modification
                  </div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <FileUploader
                    v-model:files="studyProtocol"
                    :multiple="false"
                    language="both"
                    type="file"
                    :disabled="false"
                    :typeCode="'study_protocol'" />
                  <FileDownloader v-if="studyProtocol.length > 0" :file="studyProtocol[0]" />
                  <div v-if="validators.studyProtocol.error.value" class="error-message">
                    {{ validators.studyProtocol.errorMessage.value }}
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="labelWidth">
                  <div>请上传支持试验修改的相应伦理批件<br />Please upload the corresponding ethical batch to support
                    the study modification
                  </div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <FileUploader
                    v-model:files="ethic"
                    :multiple="false"
                    language="both"
                    type="file"
                    :disabled="false"
                    :typeCode="'ethic_committee_approved_file'" />
                  <FileDownloader v-if="ethic.length > 0" :file="ethic[0]" />
                  <div v-if="validators.ethic.error.value" class="error-message">
                    {{ validators.ethic.errorMessage.value }}
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="labelWidth">
                  <div>修改原因与计划修改内容<br />Reason for modification and planned content of modification</div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <el-input v-model="description" type="textarea" :rows="5" style="width:500px;" />
                  <div v-if="validators.description.error.value" class="error-message">
                    {{ validators.description.errorMessage.value }}
                  </div>
                </el-col>
              </el-row>


            </template>
          </el-card>
        </el-col>
      </el-row>
      <div class="floating-actions">
        <el-button type="primary" :loading="loading" @click="handleSubmit">提交申请/Submit application</el-button>
        <el-button @click="router.go(-1)">取消/Cancel</el-button>
      </div>

    </div>

  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  computed,
} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElLoading} from "element-plus";

import {getProject, editApplyProject} from "@/api/itmctr";


const router = useRouter();
const route = useRoute();
const businessId = computed(() => {
  return route.params.businessId;
});
const labelWidth = 8;
const registrationNumber = ref<string>("");
const publicTitle = ref<string>("");

const studyProtocol = ref<any[]>([]);
const ethic = ref<any[]>([]);
const description = ref<string>("");

const loading = ref<boolean>(false);

// 定义验证规则和状态
const validators = {
  studyProtocol: {
    required: true,
    message: '请上传试验方案/Please upload the study protocol',
    validate: () => studyProtocol.value.length > 0,
    error: ref(false),
    errorMessage: ref('')
  },
  ethic: {
    required: true,
    message: '请上传伦理批件/Please upload the ethical batch',
    validate: () => ethic.value.length > 0,
    error: ref(false),
    errorMessage: ref('')
  },
  description: {
    required: true,
    message: '请填写修改原因与计划/Please fill in the modification reason and plan',
    validate: () => description.value.trim().length > 0,
    error: ref(false),
    errorMessage: ref('')
  }
};
// 验证单个字段
const validateField = (field: string) => {
  const validator = validators[field];
  const isValid = validator.validate();
  validator.error.value = !isValid;
  validator.errorMessage.value = isValid ? '' : validator.message;
  return isValid;
};

// 验证所有字段
const validateForm = () => {
  let isValid = true;
  for (const field in validators) {
    if (!validateField(field)) {
      isValid = false;
    }
  }
  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    // 验证失败，显示错误
    ElMessage.error('请完成必填项/Please complete all required fields');
    return;
  }

  await editApplyProject(String(businessId.value), {
    studyProtocol: studyProtocol.value[0],
    ethic: ethic.value[0],
    description: description.value,
  });
  ElMessage.success('提交成功/Submit successfully');
  router.go(-1);
}

const loadProject = async () => {
      let loadingInstance: any;
      try {
        loading.value = true;
        loadingInstance = ElLoading.service({
          lock: true,
          text: "正在加载...",
          background: "rgba(0, 0, 0, 0.3)",
        });
        // 获取表单定义，formCode固定为test
        const response = await getProject(String(businessId.value));

        registrationNumber.value = response.data.formData["RegistrationNumber"];
        publicTitle.value = response.data.formData["PublicTitle"];
        loading.value = false;

      } catch (error: any) {
      } finally {
        if (loadingInstance) loadingInstance.close();
      }
    }
;

onMounted(loadProject);
</script>

<style lang="scss" scoped>

.error-message {
  color: #F56C6C;
  font-size: 12px;
  margin-top: 5px;
}

// 为错误字段添加红色边框效果（可选）
.field-error {
  .el-input__wrapper,
  .el-textarea__wrapper {
    box-shadow: 0 0 0 1px #F56C6C inset !important;
  }
}

// 悬浮操作按钮样式
.floating-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 50px;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  padding: 12px 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

// 添加到你的样式部分
.table-card {
  .el-row {
    margin-bottom: 10px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    align-items: center;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .el-col:first-child {
      //font-weight: 200;
      //color: #606266;
      text-align: right;
      padding-right: 20px;
      font-size: 12px;
      color: #333;
    }
  }

  // 表单样式
  .el-text {
    font-size: 14px;
    line-height: 1.6;
    display: block;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
    min-height: 40px;
    width: 500px;
  }

  // 修改按钮行样式
  .el-row:last-child {
    margin-top: 30px;
    border-bottom: none;

  }
}

// 响应式调整
@media (max-width: 768px) {
  .table-card .el-row .el-col {
    margin-bottom: 10px;
  }
}
</style>
