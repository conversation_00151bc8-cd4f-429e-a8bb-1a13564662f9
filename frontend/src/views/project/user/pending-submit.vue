<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      :title="props.title"
      :data="formInstanceList"
      :columns="tableColumns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #toolbar>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
      </template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button
          type="primary"
          link
          @click="handleEdit(row)"
          v-if="
            row.status === FormInstanceStatus.Draft ||
            row.status === FormInstanceStatus.Rejected ||
            row.status === FormInstanceStatus.Confirmed
          "
        >
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-button type="primary" link @click="handleView(row)">
          <el-icon><View /></el-icon>
          查看
        </el-button>
        <el-button
          type="danger"
          link
          @click="handleDelete(row)"
          v-if="
            row.status === FormInstanceStatus.Draft ||
            row.status === FormInstanceStatus.Rejected
          "
        >
          <el-icon><Delete /></el-icon>
          作废
        </el-button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import SearchForm, { FormItem } from "@/components/common/SearchForm.vue";
import DataTable, { TableColumn } from "@/components/common/DataTable.vue";
import { FormInstanceStatus } from "@/enums";
import { Plus, Edit, Delete, View } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getUserFormInstancePage,
  cancelFormInstance,
} from "@/api/dynamic-form";
import {
  FormInstanceDto,
  FormInstanceJsonDto,
  FormDefinitionDto,
} from "@/dtos/dynamic-form.dto";
import {
  getDisplayByDefinition,
  getStatusTagType,
  getStatusText,
  getFormDataValue,
} from "@/utils/dynamic-form";
import router from "@/router";

// props
const props = defineProps<{
  formCode: string;
  title: string;
  columns: Array<{
    prop: string;
    label: string;
    width?: number;
    placeholder?: string;
    search?: boolean;
  }>;
}>();

const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "注册题目/Public title",
    prop: "public_title",
    placeholder: "",
  },
  {
    type: "input",
    label: "正式科学名/Scientific title",
    prop: "scientific_title",
    placeholder: "",
  },
  {
    type: "input",
    label: "研究课题代号(代码)/Subject ID",
    prop: "study_subject_id",
    placeholder: "",
  },
]);

const tableColumns = ref<TableColumn[]>([
  {
    prop: "registration_number",
    label: { zh: "注册号", en: "Registration number" },
    width: 120,
    formatter: (row: any) =>
      getDisplayByDefinition(formDefinition.value, row, "registration_number"),
  },
  {
    prop: "public_title",
    label: { zh: "注册题目", en: "Public title" },
    width: 120,
    formatter: (row: any) =>
      getDisplayByDefinition(formDefinition.value, row, "public_title"),
  },
  {
    prop: "status",
    label: { zh: "审核状态", en: "Review Status" },
    width: 100,
    slot: "status",
  },
  // {
  //   prop: "firstSubmitTime",
  //   label: { zh: "首次提交时间", en: "First Submission" },
  //   width: 100,
  //   formatter(row: any) {
  //     return getFormDataValue(row.formData, "FirstSubmitTime");
  //   },
  // },
  {
    prop: "firstApprovalUserName",
    label: { zh: "总审核员", en: "Send User" },
    width: 100,
    formatter(row: any) {
      return getFormDataValue(row.formData, "FirstApprovalUserName");
    },
  },
  {
    prop: "secondApprovalUserName",
    label: { zh: "高级审核员", en: "Execute User" },
    width: 100,
    formatter(row: any) {
      return getFormDataValue(row.formData, "SecondApprovalUserName");
    },
  },
]);

// 查询参数
const queryParams = reactive<any>({
  $pageIndex: 1,
  $pageSize: 10,
  formCode: "PROJECT",
  status: [FormInstanceStatus.Draft, FormInstanceStatus.Rejected],
  dynamicQueries: {},
  formDataDynamicQueries: [],
});

const loading = ref(false);
const total = ref(0);

const formInstanceList = ref<FormInstanceJsonDto[]>([]);
const formDefinition = ref<FormDefinitionDto>({
  language: "",
  groups: [],
});

onMounted(() => {
  getList();
});

const getList = async () => {
  try {
    loading.value = true;
    const response = await getUserFormInstancePage(queryParams);
    const { data } = response;
    formInstanceList.value = data.rows || [];
    formDefinition.value = data.formDefinition;
    total.value = data.totals;
  } catch (error) {
    // ElMessage.error("获取表单实例列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = (formData: Record<string, any>) => {
  const dynamicQueries: Record<string, any> = {};
  for (const key in formData) {
    if (formData[key] != "" && key != "status") {
      dynamicQueries[key] = formData[key];
    }
  }
  Object.assign(queryParams, {
    ...formData,
    // status: formData.status,
    dynamicQueries: dynamicQueries,
  });
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

const handleAdd = () => {
  router.push({ path: "/project/user/project-add" });
};

const handleEdit = (row: FormInstanceDto) => {
  router.push({ path: `/project/user/project-add/${row.businessId}` });
};

const handleView = (row: FormInstanceDto) => {
  router.push({ path: `/project/user/project-view/${row.businessId}` });
};

const handleDelete = async (row: FormInstanceDto) => {
  try {
    await ElMessageBox.confirm("确认要作废该填报吗？/Are you sure you want to void this submission?", "警告/Warning", {
      confirmButtonText: "确定/Confirm",
      cancelButtonText: "取消/Cancel",
      type: "warning",
    });
    await cancelFormInstance(row.businessId, row.version);

    // ElMessage.success("作废成功");
    await getList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("作废填报失败:", error);
      // ElMessage.error("作废填报失败");
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
</style>
