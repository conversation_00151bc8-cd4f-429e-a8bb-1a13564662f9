<template>
  <Navbar :show-user-name="false" :show-lang="false" />
  <div class="page-with-sticky-actions">
    <div class="app-container page-content">
      <el-card class="table-card">
        <template #header>
          <div class="card-header">
            <span>查看项目信息/View project</span>
          </div>
        </template>
        <div class="form-canvas">
          <el-form-item label="填写语言/Language" style="padding-left: 180px">
            <el-radio-group v-model="formSchema.language" disabled>
              <el-radio value="both">中文和英文/Chinese And English</el-radio>
              <el-radio value="en">仅英文/English Only</el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="group-container">
            <div class="group-block">
              <div class="group-title">项目信息/Project Info</div>
              <div class="el-row group-fields" style="margin-left: -8px; margin-right: -8px;">
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>


                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>注册号状态</div>
                          <div>Registration Status</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-select v-model="formSchema.registration_status" disabled>
                                <el-option
                                  v-for="option in selectOptions.registration_status" :key="option.value"
                                  :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                              </el-select>
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">

                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>注册题目</div>
                            <div>Public title</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.publictitle_zh" disabled />
                                <el-input v-model="formSchema.publictitle_en" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">

                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>注册题目简写</div>
                            <div>English Acronym</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.english_acronym_zh" disabled />
                                <el-input v-model="formSchema.english_acronym_en" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">

                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究课题的正式科学名称</div>
                            <div>Scientific title</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.scientific_title_zh" disabled />
                                <el-input v-model="formSchema.scientific_title_en" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">

                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究课题的正式科学名称简写</div>
                            <div>Scientific title acronym</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.scientific_title_acronym_zh" disabled />
                                <el-input v-model="formSchema.scientific_title_acronym_en" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">

                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究课题代号(代码)</div>
                            <div>Study subject ID</div>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.study_subject_id" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">

                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>在二级注册机构或其它机构的注册号</div>
                            <div>The registration number of the Partner Registry or other register</div>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.partner_registry_number" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">

                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>申请注册联系人</div>
                            <div>Applicant</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.applicant_zh" disabled />
                                <el-input v-model="formSchema.applicant_en" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究负责人</div>
                            <div>Study leader</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.study_leader_zh" disabled />
                                <el-input v-model="formSchema.study_leader_en" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>申请注册联系人电话</div>
                            <div>Applicant's telephone</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.applicant_telephone" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究负责人电话</div>
                            <div>Study leader's telephone</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.study_leader_telephone" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>申请注册联系人传真</div>
                            <div>Applicant's Fax</div>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.applicant_fax" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究负责人传真</div>
                            <div>Study leader's fax</div>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.study_leader_fax" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>申请注册联系人电子邮件</div>
                            <div>Applicant's E-mail</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.applicant_email" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究负责人电子邮件</div>
                            <div>Study leader's E-mail</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.study_leader_email" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>申请单位网址(自愿提供)</div>
                            <div>Applicant's website(voluntary supply)</div>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.applicant_website" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究负责人网址(自愿提供)</div>
                            <div>Study leader's website(voluntary supply)</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.study_leader_website" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>申请注册联系人通讯地址</div>
                            <div>Applicant's address</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.applicant_address_zh" disabled />
                                <el-input v-model="formSchema.applicant_address_en" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究负责人通讯地址</div>
                            <div>Study leader's address</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.study_leader_address_zh" disabled />
                                <el-input v-model="formSchema.study_leader_address_en" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>申请注册联系人邮政编码</div>
                            <div>Applicant's postcode</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.applicant_postcode" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究负责人邮政编码</div>
                            <div>Study leader's postcode</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.study_leader_postcode" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>申请人所在单位</div>
                            <div>Affiliation of the Registrant</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.applicant_affiliation_zh" disabled />
                                <el-input v-model="formSchema.applicant_affiliation_en" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究负责人所在单位</div>
                            <div>Affiliation of the Leader</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.study_leader_affiliation_zh" disabled />
                                <el-input v-model="formSchema.study_leader_affiliation_en" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>是否获伦理委员会批准</div>
                            <div>Approved by ethic committee</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-radio-group v-model="formSchema.ethic_committee_approved" disabled>
                              <el-radio
                                v-for="option in selectOptions.ethic_committee_approved" :key="option.value"
                                :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                            </el-radio-group>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>伦理委员会批件文号</div>
                            <div>Approved No. of ethic committee</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row>
                              <el-col>
                                <el-input v-model="formSchema.ethic_committee_approved_no" disabled />
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>伦理委员会批件附件</div>
                            <div>Approved file of Ethical Committee</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;margin-top: 20px;">
                            <el-text>查看附件</el-text>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>批准本研究的伦理委员会名称</div>
                            <div>Name of the ethic committee</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.ethic_committee_name_zh" disabled />
                            <el-input v-model="formSchema.ethic_committee_name_en" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>伦理委员会批准日期</div>
                            <div>Date of approved by ethic committee</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-date-picker
                              v-model="formSchema.ethic_committee_approved_date" type="date" disabled
                              format="YYYY-MM-DD" value-format="YYYY-MM-DD" :editable="false" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>伦理委员会联系人</div>
                            <div>Contact Name of the ethic committee</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.ethic_committee_contact_zh" disabled />
                            <el-input v-model="formSchema.ethic_committee_contact_en" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>伦理委员会联系地址</div>
                            <div>Contact Address of the ethic committee</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.ethic_committee_address_zh" disabled />
                            <el-input v-model="formSchema.ethic_committee_address_en" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>伦理委员会联系人电话</div>
                            <div>Contact phone of the ethic committee</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.ethic_committee_phone" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>伦理委员会联系人邮箱</div>
                            <div>Contact email of the ethic committee</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.ethic_committee_email" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>国家药监局批准文号</div>
                            <div>Approved No. of MPA</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.mpa_approved_no" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>国家药监局批准附件</div>
                            <div>Approved file of MPA</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;margin-top: 20px;">
                            <el-text>查看附件</el-text>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>国家药监局批准日期</div>
                            <div>Date of approved by MPA</div>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-date-picker
                              v-model="formSchema.mpa_approved_date" type="date" disabled
                              format="YYYY-MM-DD" value-format="YYYY-MM-DD" :editable="false" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究方案</div>
                            <div>Study protocol</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;margin-top: 20px;">
                            <el-text>查看附件</el-text>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>知情同意书</div>
                            <div>Informed consent file</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;margin-top: 20px;">
                            <el-text>查看附件</el-text>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究实施负责（组长）单位</div>
                            <div>Primary sponsor</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.primary_sponsor_zh" disabled />
                            <el-input v-model="formSchema.primary_sponsor_en" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究实施负责（组长）单位地址</div>
                            <div>Primary sponsor's address</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.primary_sponsor_address_zh" disabled />
                            <el-input v-model="formSchema.primary_sponsor_address_en" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <!-- 多行 -->
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>试验主办单位</div>
                            <div>Trial Sponsor</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <div class="el-row">
                              <div class="el-col el-col-24">
                                <div class="multi-subform-container">
                                  <div class="multi-subform-fields">
                                    <div
                                      class="multi-subform-instance" v-for="(item, index) in formSchema.sponsor"
                                      :key="index">

                                      <div class="multi-subform-instance-header">
                                        <h4>#{{ index + 1 }}</h4>
                                      </div>
                                      <div class="multi-subform-instance-content">
                                        <div class="el-row" style="margin-left: -8px; margin-right: -8px;">
                                          <div
                                            class="el-col el-col-8 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>国家</div>
                                                    <div>Country</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.sponsor_country_zh" disabled />
                                                          <el-input v-model="item.sponsor_country_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-8 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>省(直辖市)</div>
                                                    <div>Province</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.sponsor_province_zh" disabled />
                                                          <el-input v-model="item.sponsor_province_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-8 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>市(区县)</div>
                                                    <div>City</div>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.sponsor_city_zh" disabled />
                                                          <el-input v-model="item.sponsor_city_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>单位</div>
                                                    <div>Institution</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.sponsor_institution_zh" disabled />
                                                          <el-input v-model="item.sponsor_institution_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>具体地址</div>
                                                    <div>Address</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.sponsor_address_zh" disabled />
                                                          <el-input v-model="item.sponsor_address_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>经费或物资来源</div>
                            <div>Source(s) of funding</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.funding_source_zh" disabled />
                            <el-input v-model="formSchema.funding_source_en" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究疾病</div>
                            <div>Target disease</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.target_disease_zh" disabled />
                            <el-input v-model="formSchema.target_disease_en" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究疾病代码</div>
                            <div>Target disease code</div>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-input v-model="formSchema.target_disease_code" disabled />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>研究类型</div>
                          <div>Study type</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-select v-model="formSchema.study_type" disabled>
                                <el-option
                                  v-for="option in selectOptions.study_type" :key="option.value"
                                  :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                              </el-select>
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>研究设计</div>
                          <div>Study design</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-select v-model="formSchema.study_design" disabled>
                                <el-option
                                  v-for="option in selectOptions.study_design" :key="option.value"
                                  :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                              </el-select>
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>研究所处阶段</div>
                          <div>Study phase</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-select v-model="formSchema.study_phase" disabled>
                                <el-option
                                  v-for="option in selectOptions.study_phase" :key="option.value"
                                  :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                              </el-select>
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>研究目的</div>
                          <div>Objectives of Study</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.study_objectives_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.study_objectives_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>药物成份或治疗方案详述</div>
                          <div>Description for medicine or protocol of treatment in detail</div>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.treatment_description_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.treatment_description_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>纳入标准</div>
                          <div>Inclusion criteria</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.inclusion_criteria_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.inclusion_criteria_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>排除标准</div>
                          <div>Exclusion criteria</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.exclusion_criteria_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.exclusion_criteria_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究实施时间</div>
                            <div>Study execute time</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <div class="date-range-container">
                              <el-date-picker
                                v-model="studyTimeRange" type="daterange" format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD" disabled style="width: 100%" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>征募观察对象时间</div>
                            <div>Recruiting time</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <div class="date-range-container">
                              <el-date-picker
                                v-model="RecruitingTimeRange" type="daterange" format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD" disabled style="width: 100%" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>


                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>金标准或参考标准（即可准确诊断某疾病的单项方法或多项联合方法，在本研究中用于诊断是否有该病的临床参考标准）</div>
                          <div>Gold Standard or Reference Standard (The single method or multiple combined methods that
                            can accurately diagnose a disease, used as the clinical reference standard for diagnosing
                            the disease in this study)</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.gold_standard_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.gold_standard_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>指标试验（即本研究的待评估诊断试验，无论为方法、生物标志物或设备，均请列出名称）</div>
                          <div>Index test (The diagnostic test to be evaluated in this study, please list the names
                            whether they are methods, biomarkers or devices)</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.index_test_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.index_test_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>目标人群（可以是某种疾病患者或正常人群，详细描述其疾病特征，注意应纳入符合分布特点的全序列病例，具有良好的代表性）</div>
                          <div>Target condition (Can be patients with certain diseases or normal population, describe
                            disease characteristics in detail, note that all sequential cases with distribution
                            characteristics should be included with good representativeness)</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.target_condition_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.target_condition_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>目标人群例数</div>
                          <div>Sample size</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input v-model="formSchema.target_sample_size" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>





                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>容易混淆的疾病人群（即与目标疾病不易区分的一种或多种不同疾病，应避免采用正常人群对照的病例-对照设计）</div>
                          <div>Population with condition difficult to distinguish from the target condition (One or more
                            different diseases that are difficult to distinguish from the target disease, case-control
                            design with normal population control should be avoided)</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.confounding_condition_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.confounding_condition_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>容易混淆的疾病人群例数</div>
                          <div>Sample size</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input v-model="formSchema.confounding_sample_size" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 研究实施地点 -->
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究实施地点</div>
                            <div>Research Site</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <div class="el-row">
                              <div class="el-col el-col-24">
                                <div class="multi-subform-container">
                                  <div class="multi-subform-fields">
                                    <div
                                      class="multi-subform-instance" v-for="(item, index) in formSchema.researchSite"
                                      :key="index">

                                      <div class="multi-subform-instance-header">
                                        <h4>#{{ index + 1 }}</h4>
                                      </div>
                                      <div class="multi-subform-instance-content">
                                        <div class="el-row" style="margin-left: -8px; margin-right: -8px;">
                                          <div
                                            class="el-col el-col-8 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>国家</div>
                                                    <div>Country</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.site_country_zh" disabled />
                                                          <el-input v-model="item.site_country_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-8 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>省(直辖市)</div>
                                                    <div>Province</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.site_province_zh" disabled />
                                                          <el-input v-model="item.site_province_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-8 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>市(区县)</div>
                                                    <div>City</div>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.site_city_zh" disabled />
                                                          <el-input v-model="item.site_city_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>单位(医院)</div>
                                                    <div>Institution / hospital</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.site_institution_zh" disabled />
                                                          <el-input v-model="item.site_institution_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>单位级别</div>
                                                    <div>Level of the institution</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.site_level_zh" disabled />
                                                          <el-input v-model="item.site_level_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 干预措施 -->
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>干预措施</div>
                            <div>Intervention</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <div class="el-row">
                              <div class="el-col el-col-24">
                                <div class="multi-subform-container">
                                  <div class="multi-subform-fields">
                                    <div
                                      class="multi-subform-instance" v-for="(item, index) in formSchema.intervention"
                                      :key="index">

                                      <div class="multi-subform-instance-header">
                                        <h4>#{{ index + 1 }}</h4>
                                      </div>
                                      <div class="multi-subform-instance-content">
                                        <div class="el-row" style="margin-left: -8px; margin-right: -8px;">
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>组别</div>
                                                    <div>Group</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.intervention_group_zh" disabled />
                                                          <el-input v-model="item.intervention_group_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>样本量</div>
                                                    <div>Sample size</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.intervention_sample_size" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>干预措施</div>
                                                    <div>Intervention</div>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.intervention_name_zh" disabled />
                                                          <el-input v-model="item.intervention_name_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>干预措施代码</div>
                                                    <div>Intervention code</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.intervention_code" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>

                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>干预措施样本总量</div>
                          <div>Total sample size</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input v-model="formSchema.intervention_total_sample_size" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <!-- 测量指标 -->
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>测量指标</div>
                            <div>Measurement Index</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <div class="el-row">
                              <div class="el-col el-col-24">
                                <div class="multi-subform-container">
                                  <div class="multi-subform-fields">
                                    <div
                                      class="multi-subform-instance" v-for="(item, index) in formSchema.measurement"
                                      :key="index">

                                      <div class="multi-subform-instance-header">
                                        <h4>#{{ index + 1 }}</h4>
                                      </div>
                                      <div class="multi-subform-instance-content">
                                        <div class="el-row" style="margin-left: -8px; margin-right: -8px;">
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>指标中文名</div>
                                                    <div>Outcome Name</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.outcome_name_zh" disabled />
                                                          <el-input v-model="item.outcome_name_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>指标类型</div>
                                                    <div>Type</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-select v-model="item.outcome_type" disabled>
                                                            <el-option
                                                              key="4002001" label="主要指标/Primary indicator"
                                                              value="4002001" />
                                                            <el-option
                                                              key="4002002" label="次要指标/Secondary indicator"
                                                              value="4002002" />
                                                            <el-option
                                                              key="4002003" label="附加指标/Additional indicator"
                                                              value="4002003" />
                                                            <el-option
                                                              key="4002004" label="副作用指标/Adverse events"
                                                              value="4002004" />
                                                          </el-select>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>测量时间点</div>
                                                    <div>Measure time point of outcome</div>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.measure_time_point_zh" disabled />
                                                          <el-input v-model="item.measure_time_point_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>测量方法</div>
                                                    <div>Measure method</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.measure_method_zh" disabled />
                                                          <el-input v-model="item.measure_method_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>

                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 采集人体标本 -->
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>采集人体标本</div>
                            <div>Human Sample Collection</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <div class="el-row">
                              <div class="el-col el-col-24">
                                <div class="multi-subform-container">
                                  <div class="multi-subform-fields">
                                    <div
                                      class="multi-subform-instance" v-for="(item, index) in formSchema.humanSample"
                                      :key="index">
                                      <div class="multi-subform-instance-header">
                                        <h4>#{{ index + 1 }}</h4>
                                      </div>
                                      <div class="multi-subform-instance-content">
                                        <div class="el-row" style="margin-left: -8px; margin-right: -8px;">
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>标本中文名</div>
                                                    <div>Sample Name</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.sample_name_zh" disabled />
                                                          <el-input v-model="item.sample_name_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>组织</div>
                                                    <div>Tissue</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.tissue_zh" disabled />
                                                          <el-input v-model="item.tissue_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>人体标本去向</div>
                                                    <div>Fate of sample</div>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-select v-model="item.fate_of_sample" disabled>
                                                            <el-option
                                                              key="1012001" label="使用后销毁/Destruction after use"
                                                              value="1012001" />
                                                            <el-option
                                                              key="1012002"
                                                              label="使用后保存/Preservation after use" value="1012002" />
                                                            <el-option
                                                              key="1012003" label="其它/Others"
                                                              value="1012003" />
                                                          </el-select>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <div
                                            class="el-col el-col-12 is-guttered"
                                            style="padding-right: 8px; padding-left: 8px;">
                                            <div class="subform-field-row">
                                              <div>
                                                <div class="form-field-row">
                                                  <label class="form-label">
                                                    <div>说明</div>
                                                    <div>Note</div><span class="required">*</span>
                                                  </label>
                                                  <div class="field-content" style="display: inline-block;">
                                                    <div class="el-row">
                                                      <div class="el-col el-col-24">
                                                        <div class="multilang-inputs">
                                                          <el-input v-model="item.sample_note_zh" disabled />
                                                          <el-input v-model="item.sample_note_en" disabled />
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>

                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>征募研究对象状况</div>
                          <div>Recruiting status</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-select v-model="formSchema.recruiting_status" disabled>
                                <el-option
                                  v-for="option in selectOptions.recruiting_status" :key="option.value"
                                  :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                              </el-select>
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>年龄范围</div>
                            <div>Age range</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <div class="int-range-wrapper">
                              <div class="int-range-content">
                                <span class="range-icon">
                                  <el-icon>
                                    <Document />
                                  </el-icon>
                                </span>
                                <el-input v-model="formSchema.age_range_min" type="number" disabled />
                                <span class="range-sep">-</span>
                                <el-input v-model="formSchema.age_range_max" type="number" disabled />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row ">
                          <label class="form-label">
                            <div>性别</div>
                            <div>Gender</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row :span="6">
                              <el-col :span="24">
                                <el-select v-model="formSchema.gender" disabled>
                                  <el-option
                                    v-for="option in selectOptions.gender" :key="option.value"
                                    :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                                </el-select>
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>

                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>随机方法（请说明由何人用什么方法产生随机序列）</div>
                          <div>Randomization Procedure (please state who generates the random number sequence and by
                            what method)</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.randomization_procedure_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.randomization_procedure_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>研究对象是否签署知情同意书</div>
                            <div>Sign the informed consent</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-radio-group v-model="formSchema.sign_informed_consent" disabled>
                              <el-radio
                                v-for="option in selectOptions.sign_informed_consent" :key="option.value"
                                :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                            </el-radio-group>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row ">
                          <label class="form-label">
                            <div>随访时间</div>
                            <div>Length of follow-up (include time point of outcome measure)</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-row :span="6">
                              <el-col :span="24">
                                <div
                                  data-v-6f06c8b9="" class="el-input el-input-group el-input-group--append"
                                  style="flex: 1 1 0%;">
                                  <el-input v-model="formSchema.follow_up_length" disabled />
                                  <el-select v-model="formSchema.follow_up_unit" disabled>
                                    <el-option key="1007001" label="天/Day(s)" value="1007001" />
                                    <el-option key="1007002" label="周/Week(s)" value="1007002" />
                                    <el-option key="1007003" label="月/Month(s)" value="1007003" />
                                    <el-option key="1007004" label="年/Year(s)" value="1007004" />
                                    <el-option key="1007005" label="小时/Hour(s)" value="1007005" />
                                  </el-select>
                                </div>
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>隐蔽分组方法和过程</div>
                          <div>Process of allocation concealment</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.allocation_concealment_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.allocation_concealment_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>盲法</div>
                          <div>Blinding</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.blinding_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.blinding_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>揭盲或破盲原则和方法</div>
                          <div>Rules of uncover or ceasing blinding</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.unblinding_rules_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.unblinding_rules_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>统计方法名称</div>
                          <div>Statistical methods</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.statistical_methods_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.statistical_methods_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>试验完成后的统计结果</div>
                          <div>Calculated Results after the Study Completed</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.calculated_results_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.calculated_results_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-24 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>上传试验完成后的统计结果</div>
                          <div>Statistical results after completion of the test file upload</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block; margin-top: 20px;">
                          <el-row :span="6">
                            <el-col :span="24">
                              查看附件
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>是否公开试验完成后的统计结果</div>
                            <div>Calculated Results after the Study Completed public access</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-select v-model="formSchema.calculated_results_public" disabled>
                              <el-option
                                v-for="option in selectOptions.calculated_results_public" :key="option.value"
                                :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                            </el-select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>全球唯一识别码</div>
                          <div>UTN</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block; margin-top: 20px;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input v-model="formSchema.utn" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>是否共享原始数据</div>
                            <div>IPD sharing</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-select v-model="formSchema.ipd_sharing" disabled>
                              <el-option
                                v-for="option in selectOptions.ipd_sharing" :key="option.value"
                                :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                            </el-select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>共享原始数据的方式（说明：请填入公开原始数据日期和方式，如采用网络平台，需填该网络平台名称和网址）</div>
                          <div>The way of sharing IPD(include metadata and protocol, If use web-based public database, please provide the url)</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block; margin-top: 20px;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input v-model="formSchema.ipd_sharing_way_zh" disabled />
                              <el-input v-model="formSchema.ipd_sharing_way_en" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>数据采集和管理（说明：数据采集和管理由两部分组成，一为病例记录表(Case Record Form, CRF)，二为电子采集和管理系统</div>
                          <div>(Electronic Data Capture, EDC)，如ResMan即为一种基于互联网的EDC）Data collection and Management (A standard data collection and management system include a CRF and an electronic data capture)</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.data_collection_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.data_collection_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;">
                        <div class="form-field-row">
                          <label class="form-label">
                            <div>数据与安全监察委员会</div>
                            <div>Data and Safety Monitoring Committee</div>
                            <span class="required">*</span>
                          </label>
                          <div class="field-content" style="display: inline-block;">
                            <el-select v-model="formSchema.safety_committee" disabled>
                              <el-option
                                v-for="option in selectOptions.safety_committee" :key="option.value"
                                :label="option.labelZh + '/' + option.labelEn" :value="option.value" />
                            </el-select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-12 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
                <div class="el-col el-col-18 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                  <div class="field-item-wrapper">
                    <div class="el-card is-never-shadow field-item-card">
                      <div class="el-card__body" style="padding: 0px; background: none;"></div>
                      <div class="form-field-row ">
                        <label class="form-label">
                          <div>研究计划书或研究结果报告发表信息（杂志名称、期、卷、页，时间；或网址）</div>
                          <div>Publication information of the protocol/research results report(name of the journal, volume, issue, pages, time; or website)</div>
                          <span class="required">*</span>
                        </label>
                        <div class="field-content" style="display: inline-block;">
                          <el-row :span="6">
                            <el-col :span="24">
                              <el-input
                                v-model="formSchema.publication_info_zh" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                              <el-input
                                v-model="formSchema.publication_info_en" type="textarea" :rows="4"
                                class="textarea-input" disabled />
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="el-col el-col-6 is-guttered" style="padding-right: 8px; padding-left: 8px;">
                </div>
              </div>
            </div>
          </div>
        </div>


      </el-card>
    </div>
    <!-- 悬浮操作按钮 -->
    <div class="floating-actions">
      <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getProjectInfo, getSelectOptions } from "@/api/itmctr";
import type { ProjectViewDto } from "@/dtos/project.dto";
import { formatDateString } from "@/utils/date";
const router = useRouter();
const route = useRoute();
const loading = ref(false);
const formSchema = ref<ProjectViewDto>({
  language: "",
  business_id: "",
  version: "",
  form_code: "",
  registration_number: "",
  registration_status: "",
  publictitle_zh: "",
  publictitle_en: "",
  english_acronym_zh: "",
  english_acronym_en: "",
  scientific_title_zh: "",
  scientific_title_en: "",
  scientific_title_acronym_zh: "",
  scientific_title_acronym_en: "",
  study_subject_id: "",
  partner_registry_number: "",
  applicant_zh: "",
  applicant_en: "",
  study_leader_zh: "",
  study_leader_en: "",
  applicant_telephone: "",
  study_leader_telephone: "",
  applicant_fax: "",
  study_leader_fax: "",
  applicant_email: "",
  study_leader_email: "",
  applicant_website: "",
  study_leader_website: "",
  applicant_address_zh: "",
  applicant_address_en: "",
  study_leader_address_zh: "",
  study_leader_address_en: "",
  applicant_postcode: "",
  study_leader_postcode: "",
  applicant_affiliation_zh: "",
  applicant_affiliation_en: "",
  study_leader_affiliation_zh: "",
  study_leader_affiliation_en: "",
  ethic_committee_approved: "",
  ethic_committee_approved_no: "",
  ethic_committee_name_zh: "",
  ethic_committee_name_en: "",
  ethic_committee_approved_date: "",
  ethic_committee_contact_zh: "",
  ethic_committee_contact_en: "",
  ethic_committee_address_zh: "",
  ethic_committee_address_en: "",
  ethic_committee_phone: "",
  ethic_committee_email: "",
  mpa_approved_no: "",
  mpa_approved_date: "",
  primary_sponsor_zh: "",
  primary_sponsor_en: "",
  primary_sponsor_address_zh: "",
  primary_sponsor_address_en: "",
  funding_source_zh: "",
  funding_source_en: "",
  target_disease_zh: "",
  target_disease_en: "",
  target_disease_code: "",
  study_type: "",
  study_design: "",
  study_phase: "",
  study_objectives_zh: "",
  study_objectives_en: "",
  treatment_description_zh: "",
  treatment_description_en: "",
  inclusion_criteria_zh: "",
  inclusion_criteria_en: "",
  exclusion_criteria_zh: "",
  exclusion_criteria_en: "",
  study_time_start: "",
  study_time_end: "",
  recruiting_time_start: "",
  recruiting_time_end: "",
  gold_standard_zh: "",
  gold_standard_en: "",
  index_test_zh: "",
  index_test_en: "",
  target_condition_zh: "",
  target_condition_en: "",
  target_sample_size: "",
  confounding_condition_zh: "",
  confounding_condition_en: "",
  confounding_sample_size: "",
  intervention_total_sample_size: "",
  recruiting_status: "",
  age_range_min: 0,
  age_range_max: 0,
  gender: "",
  randomization_procedure_zh: "",
  randomization_procedure_en: "",
  sign_informed_consent: "",
  follow_up_length: "",
  follow_up_unit: "",
  allocation_concealment_zh: "",
  allocation_concealment_en: "",
  blinding_zh: "",
  blinding_en: "",
  unblinding_rules_zh: "",
  unblinding_rules_en: "",
  statistical_methods_zh: "",
  statistical_methods_en: "",
  calculated_results_zh: "",
  calculated_results_en: "",
  calculated_results_public: "",
  utn: "",
  ipd_sharing: "",
  ipd_sharing_way_zh: "",
  ipd_sharing_way_en: "",
  data_collection_zh: "",
  data_collection_en: "",
  safety_committee: "",
  publication_info_zh: "",
  publication_info_en: "",
  first_submit_time: "",
  humanSample: [],
  intervention: [],
  sponsor: [],
  researchSite: [],
  measurement: [],
});
const selectOptions = ref<Record<string, Array<{ labelZh: string; labelEn: string; value: string }>>>({});

const fetchProjectInfo = async () => {
  loading.value = true;
  try {
    let key = route.params.key;
    const res = await getProjectInfo(key, false);
    formSchema.value = res.data as ProjectViewDto;
  } finally {
    loading.value = false;
  }
};
const fetchGetSelectOptions = async () => {
  try {
    const response = await getSelectOptions();
    const { data } = response;
    // Ensure data is an object with string keys
    if (Array.isArray(data)) {
      // Convert array to object if needed, or set to empty object to avoid type error
      selectOptions.value = {};
    } else {
      selectOptions.value = data;
    }
    console.log("获取选项成功", selectOptions.value);
  } catch (error) {
    ElMessage.error(`获取选项失败`);
  }
}
const studyTimeRange = computed({
  get() {
    return [
      formSchema.value.study_time_start || "",
      formSchema.value.study_time_end || ""
    ];
  },
  set(val: [string, string]) {
    formSchema.value.study_time_start = val?.[0] || "";
    formSchema.value.study_time_end = val?.[1] || "";
  }
});
const RecruitingTimeRange = computed({
  get() {
    return [
      formSchema.value.recruiting_time_start || "",
      formSchema.value.recruiting_time_end || ""
    ];
  },
  set(val: [string, string]) {
    formSchema.value.recruiting_time_start = val?.[0] || "";
    formSchema.value.recruiting_time_end = val?.[1] || "";
  }
});


onMounted(() => {
  fetchGetSelectOptions();
  fetchProjectInfo();
});

const handleBack = () => {
  router.back();
};
</script>


<style lang="scss" scoped>


.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}

// 浮动操作按钮样式已移至全局样式

.form-canvas {
  min-height: 200px;
  position: relative;
  width: 100%;
}

.empty-root-tip {
  color: #909399;
  background: #f8f8fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  padding: 32px;
  text-align: center;
  margin: 32px 24px;
  font-size: 16px;
}

.group-insert-placeholder {
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  background: #f8f8fa;
  border: 2px dashed transparent;
  border-radius: 6px;
  margin: 8px 0;
  font-size: 14px;
  transition: border-color 0.2s, background 0.2s;
}

.group-insert-placeholder:hover {
  border-color: #c0c4cc;
  background: #f5f7fa;
}

.group-container {
  width: 100%;
}

.group-insert-placeholder {
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #aaa;
  background: #f8f8fa;
  border: 2px dashed transparent;
  border-radius: 6px;
  margin: 8px 0;
  font-size: 15px;
  transition: border-color 0.2s, background 0.2s;
}

.group-insert-placeholder.drag-over-insert {
  border-color: #409eff;
  background: #e6f7ff;
  color: #409eff;
}

.group-block {
  margin-bottom: 32px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fff;
  padding: 18px 20px 18px 20px;
  transition: box-shadow 0.2s, border-color 0.2s;
  width: 100%;
}

.group-block.drag-over {
  border-color: #409eff;
  box-shadow: 0 0 0 2px #409eff33;
}

.group-title {
  font-weight: bold;
  margin-bottom: 16px;
  font-size: 17px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

/* 子表单相关样式已移除 */
.empty-tip {
  color: #aaa;
  background: #f8f8fa;
  border: 1px dashed #ddd;
  border-radius: 4px;
  padding: 24px;
  text-align: center;
  margin-bottom: 12px;
}

.group-title-input {
  font-size: 16px;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #ccc;
  width: 120px;
  margin-right: 8px;
}

.field-item-wrapper {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 4px;
  flex: 1;
  position: relative;

  /* border: 1px solid #dcdfe6; */
  padding: 10px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.field-action-btns {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  margin-bottom: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: 10px;
}

.dialog-body-scroll {
  max-height: 60vh;
  overflow-y: auto;
  padding: 10px 0;
}

.group-edit-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

.form-field-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4px;
  position: relative;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s;
}

.form-field-row.design-mode {
  border: 1px dashed transparent;
  padding: 8px;
  margin-bottom: 8px;
}

.form-field-row.design-mode:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

.form-field-row.approval-mode {
  border: 1px dashed transparent;
  padding: 8px;
  margin-bottom: 8px;
}

.form-field-row.approval-mode:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

.form-field-row.has-warning {
  border-color: var(--el-color-danger);
  background-color: rgba(64, 158, 255, 0.05);
}

.form-label {
  /* min-width: 180px;
  max-width: 250px; */
  width: 150px;
  margin-right: 8px;
  color: #333;
  padding-top: 8px;
  text-align: right;
  font-size: 12px;
}

.multi-subform-container {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9fafc;
}

.multi-subform-container .form-label {
  width: 80px;
  margin-right: 8px;
  color: #333;
  padding-top: 8px;
  text-align: right;
  font-size: 12px;
}

.multi-subform-fields {
  width: 100%;
}

.multi-subform-instance {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 16px;
  background-color: #fff;
}

.required {
  color: #f56c6c;
  margin-left: 2px;
}

.field-content {
  flex: 1;
  display: flex;
}

.field-content.readonly :deep(.el-input__wrapper),
.field-content.readonly :deep(.el-textarea__wrapper),
.field-content.readonly :deep(.el-select),
.field-content.readonly :deep(.el-checkbox),
.field-content.readonly :deep(.el-radio),
.field-content.readonly :deep(.el-date-editor) {
  pointer-events: none;
  opacity: 0.8;
}

.field-actions {
  position: absolute;
  right: 4px;
  top: 4px;
  z-index: 10;
  display: none;
}

.form-field-row.design-mode:hover>.field-actions {
  display: block;
}

.form-field-row.approval-mode:hover>.field-actions {
  display: block;
}

/* 导出这些样式，以便子组件可以使用 */
:export {
  fieldRowClass: form-field-row;
  fieldLabelClass: form-label;
  fieldContentClass: field-content;
  fieldActionsClass: field-actions;
}

.field-content .el-row .el-col:last-child {
  padding-left: 5px;
}

.field-error-msg {
  color: #f56c6c;
  font-size: 13px;
  margin-top: 4px;
}

.field-item-card {
  margin-bottom: 0;
  background: none;
  box-shadow: none;
  border: none;
}

.date-range-container {
  flex: 1;
}

.int-range-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 0;
  box-sizing: border-box;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  /* background-color: #fff; */
  transition: border-color 0.2s;
  white-space: nowrap;
  padding: 0 4px;
}


.int-range-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: 32px;
  padding: 0 4px;
}
</style>
