<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <Navbar :show-user-name="false" :show-lang="false" />
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      :show-more-button="true"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      title="数据检索/Trial search"
      :data="projectSearchResultList"
      :columns="tableColumns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #toolbar> </template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ getStatusText(row.status) }} 
        </el-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleView(row.key)">
          <el-icon><View /></el-icon>
          查看
        </el-button>

        <el-button type="primary" link @click="handleHistory(row.business_id)">
          <el-icon><Memo /></el-icon>
          历史版本
        </el-button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import SearchForm, { FormItem } from "@/components/common/SearchForm.vue";
import DataTable, { TableColumn } from "@/components/common/DataTable.vue";
import { View ,Memo} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { getTrialSearchPage,getSelectOptions} from "@/api/itmctr";
import {FormDataQueryOperator, FormInstanceStatus} from "@/enums";
import { ProjectSearchResultDto   } from "@/dtos/project.dto";
import {
  getDisplayByDefinition,
  getStatusTagType,
  getStatusText,
  getFormDataValue,
} from "@/utils/dynamic-form";
import router from "@/router";
import {formatDateString} from "@/utils/date";
import Navbar from '@/components/layout/Navbar.vue'
// props
const props = defineProps<{
  title: string;
  columns: Array<{
    prop: string;
    label: string;
    width?: number;
    placeholder?: string;
    search?: boolean;
  }>;
}>();

const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "注册题目/Public title",
    prop: "publictitle",
    placeholder: "",

  },
  {
    type: "input",
    label: "正式科学名/Scientific title",
    prop: "scientific_title",
    placeholder: "",
  },
  {
    type: "input",
    label: "研究课题代号(代码)/Subject ID",
    prop: "study_subject_id",
    placeholder: "",
  },
  {
    type: "select",
    label: "注册状态/Registration Status",
    prop: "registration_status",
    placeholder: ""
  },
  {
    type: "input",
    label: "注册号/Registration number",
    prop: "registration_number",
    placeholder: "",
  },
  {
    type: "input",
    label: "在其它机构的注册号/The registration number of the Partner Registry or other register",
    prop: "partner_registry_number",
    placeholder: "",
  },
  {
    type: "input",
    label: "申请注册联系人/Applicant",
    prop: "applicant",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "研究负责人/Study leader",
    prop: "study_leader",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "研究负责人所在单位/Primary sponsor",
    prop: "primary_sponsor",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "试验主办单位/Primary sponsor",
    prop: "sponsor_institution",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "经费或物资来源/Source(s) of funding",
    prop: "funding_source",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "研究疾病/Target disease",
    prop: "target_disease",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "研究疾病代码/Target disease code",
    prop: "target_disease_code",
    placeholder: "",
    visible: false,
  },
  {
    type: "select",
    label: "研究类型/Study type",
    prop: "study_type",
    placeholder: "",
    visible: false,
  },
  {
    type: "select",
    label: "研究所处阶段/Study phase",
    prop: "study_phase",
    placeholder: "",
    visible: false,
  },
  {
    type: "select",
    label: "研究设计/Study design",
    prop: "study_design",
    placeholder: "",
    visible: false,
  },
  {
    type: "date",
    label: "研究实施开始时间/Start time of study implementation.",
    prop: "study_time_start",
    placeholder: "",
    visible: false,
  },
  {
    type: "date",
    label: "研究实施结束时间/End time of study implementation.",
    prop: "study_time_end",
    placeholder: "",
    visible: false,
  },
  {
    type: "select",
    label: "征募研究对象状况/Recruiting status",
    prop: "recruiting_status",
    placeholder: "",
    visible: false,
  },
  {
    type: "select",
    label: "性别/Gender",
    prop: "gender",
    placeholder: "",
    visible: false,
  },
  {
    type: "select",
    label: "研究对象是否签署知情同意书/Sign the informed consent",
    prop: "sign_informed_consent",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "实施地点 国家/Research Site Country",
    prop: "site_country",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "实施地点 省/Research Site Province",
    prop: "site_province",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "实施地点 市/Research Site City",
    prop: "site_city",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "实施地点 单位/Research Site Institution",
    prop: "site_institution",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "实施地点 单位级别/Research Site Level of the institution",
    prop: "site_level",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "干预措施/Intervention",
    prop: "intervention_name",
    placeholder: "",
    visible: false,
  },
  {
    type: "input",
    label: "干预措施代码/Intervention code",
    prop: "intervention_code",
    placeholder: "",
    visible: false,
  },
  {
    type: "select",
    label: "是否获伦理委员会批准/Approved by ethic committee",
    prop: "ethic_committee_approved",
    placeholder: "",
    visible: false,
  },
  {
    type: "select",
    label: "是否公开试验完成后的统计结果/Calculated Results after the Study Completed public access",
    prop: "calculated_results_public",
    placeholder: "",
    visible: false,
  },
  {
    type: "select",
    label: "上传试验完成后的统计结果/Statistical results after completion of the test file upload",
    prop: "statistical_results_file",
    placeholder: "",
    options: [
      { label: "是/Yes", value: "True" },
      { label: "否/No", value: "False" },
    ],
    visible: false,
  },
]);

const tableColumns = ref<TableColumn[]>([
  {
    prop: "registration_number",
    label: {zh: "注册号", en: "Registration number"},
    width: 120,
    formatter: (row: any) =>
        formatProjectResultValue(row, "registration_number"),
  },
  {
    prop: "public_title",
    label: {zh: "注册题目", en: "Public title"},
    width: 120,
    formatter: (row: any) =>
        `${formatProjectResultValue(row, "publictitle_zh")}/${formatProjectResultValue(row, "publictitle_en")}`,
  },
  {
    prop: "applicant_affiliation",
    label: {zh: "所在单位", en: "Affiliation of the Registrant"},
    width: 120,
    formatter: (row: any) =>
        `${formatProjectResultValue(row, "applicant_affiliation_zh")}/${formatProjectResultValue(row, "applicant_affiliation_en")}`,
  },
  {
    prop: "study_type",
    label: { zh: "研究类型", en: "Study type" },
    width: 120,
    formatter: (row: any) => {
      const value = formatProjectResultValue(row, "study_type");
      const option = selectOptions.value.study_type?.find(opt => opt.value === value);
      return option ? `${option.labelZh}/${option.labelEn}` : value;
    }
  },
  {
    prop: "first_submit_time",
    label: { zh: "首次提交时间", en: "First Submission" },
    width: 100,
    formatter(row: any) {
      return formatDateString(getFormDataValue(row, "first_submit_time"));
    },
  },
]);

// 查询参数
const queryParams = reactive<any>({
  $pageIndex: 1,
  $pageSize: 10,
  formCode: "PROJECT",
  dynamicQueries: {},
});

const loading = ref(false);
const total = ref(0);
const selectOptions = ref<Record<string, Array<{ labelZh: string; labelEn: string; value: string }>>>({});

const projectSearchResultList = ref<ProjectSearchResultDto[]>([]);


onMounted(() => {
  fetchGetSelectOptions();
  getList();
});

const getList = async () => {
  try {
    loading.value = true;
    const response = await getTrialSearchPage(queryParams);
    const { data } = response;
    projectSearchResultList.value = data.rows || [];
    total.value = data.totals;
  } catch (error) {
    // ElMessage.error("获取表单实例列表失败");
  } finally {
    loading.value = false;
  }
};

const fetchGetSelectOptions = async() =>{
  try {
    const response = await getSelectOptions();
    const { data } = response;
    // Ensure data is an object with string keys
    if (Array.isArray(data)) {
      // Convert array to object if needed, or set to empty object to avoid type error
      selectOptions.value = {};
    } else {
      selectOptions.value = data;
    }
    searchFormItems.value.forEach(item => {
      if (item.type === "select" && item.prop && selectOptions.value[item.prop]) {
        var options = selectOptions.value[item.prop];
        item.options = options.map(opt => ({
          label: `${opt.labelZh}/${opt.labelEn}`,
          value: opt.value,
        }));
      }
    });
  } catch (error) {
    ElMessage.error(`获取选项失败`);
  }
}
function formatProjectResultValue(row: ProjectSearchResultDto, prop: keyof ProjectSearchResultDto) {
  return row[prop] ?? '';
}

const handleSearch = (formData: Record<string, any>) => {
  const dynamicQueries: Record<string, any> = {};
  for (const key in formData) {
    if (formData[key] != "" && key != "status") {
      dynamicQueries[key] = formData[key];
    }
  }
  Object.assign(queryParams, {
    ...formData,
    // status: formData.status,
    dynamicQueries: dynamicQueries,
  });
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
debugger;
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

const handleView = (key: number) => {
  router.push({ name: 'publicProjectView', params: { key: key, isHistory: "false" } });
};

const handleHistory = (business_id: string) => {
  router.push({ name: 'publicHistory', params: { businessId: business_id } });
};


</script>

<style lang="scss" scoped>

</style>
