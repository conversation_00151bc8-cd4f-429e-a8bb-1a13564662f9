<template>
  <Navbar :show-user-name="false"/>
  <div class="register-page">
    <div class="register-container">
      <div class="header">
        <h2>{{ $t('register.title') }}</h2>
      </div>
      <el-form ref="RegisterFormRef" :model="form" :rules="rules" label-width="180px">
        <el-form-item :label="$t('register.label.username')" prop="username">
          <el-input v-model="form.username" :placeholder="$t('register.placeholder.username')" tabindex="1"/>
        </el-form-item>
        <el-form-item :label="$t('register.label.password')" prop="password">
          <el-input v-model="form.password" type="password" :placeholder="$t('register.placeholder.password')"
                    tabindex="2"/>
        </el-form-item>
        <el-form-item :label="$t('register.label.confirmPassword')" prop="repassword">
          <el-input v-model="form.repassword" type="password" :placeholder="$t('register.placeholder.confirmPassword')"
                    tabindex="3"/>
        </el-form-item>
        <el-form-item :label="$t('register.label.email')" prop="email">
          <el-input v-model="form.email" :placeholder="$t('register.placeholder.email')" tabindex="4"/>
        </el-form-item>
        <el-form-item :label="$t('register.label.realname')" prop="realname">
          <el-input v-model="form.realname" :placeholder="$t('register.placeholder.realname')" tabindex="5"/>
        </el-form-item>
        <el-form-item :label="$t('register.label.gender')" prop="gender">
          <el-select v-model="form.gender" :placeholder="$t('register.placeholder.gender')" tabindex="6">
            <el-option value="男" label="男/Male"/>
            <el-option value="女" label="女/Female"/>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('register.label.country')" prop="country">
          <el-select v-model="form.country" :placeholder="$t('register.placeholder.country')" tabindex="7">
            <el-option value="中国" label="中国/China"/>
            <el-option value="其他" label="其他/Other"/>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('register.label.unit')" prop="unit">
          <el-input v-model="form.unit" :placeholder="$t('register.placeholder.unit')" tabindex="8"/>
        </el-form-item>
        <el-form-item :label="$t('register.label.contactaddress')" prop="contactaddress">
          <el-input v-model="form.contactaddress" :placeholder="$t('register.placeholder.contactaddress')"
                    tabindex="9"/>
        </el-form-item>
        <el-form-item :label="$t('register.label.mobile')" prop="mobile">
          <el-input v-model="form.mobile" :placeholder="$t('register.placeholder.mobile')" tabindex="10"/>
        </el-form-item>
        <el-form-item :label="$t('register.label.telephone')" prop="telephone">
          <el-input v-model="form.telephone" :placeholder="$t('register.placeholder.telephone')" tabindex="11"/>
        </el-form-item>
      </el-form>
      <div class="register-actions">
        <el-button type="primary" @click="submitForm">{{ $t('register.label.btnRegister') }}</el-button>
        <router-link to="/login" class="login-link">{{ $t('register.gotoLogin') }}</router-link>
      </div>
    </div>
  </div>
  <Footer/>
</template>

<script setup lang="ts">
import Navbar from '@/components/layout/Navbar.vue';
import Footer from '@/components/layout/Footer.vue';
// ...原有script代码...
import {ref, reactive, onMounted} from "vue";
import {ElMessage, FormInstance, FormRules} from "element-plus";
import {Register} from "@/api/rbac";
import {RegisterParams} from "@/dtos";
import {useI18n} from "vue-i18n";
import CryptoJS from "crypto-js";
import {useCryptoStore} from '@/stores/crypto';
import {useRouter} from 'vue-router'
import {watch} from 'vue';

const {t, locale} = useI18n();

watch(locale, () => {
  RegisterFormRef.value?.clearValidate();
});
const router = useRouter()
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");
const RegisterFormRef = ref<FormInstance>();
const form = reactive<RegisterParams>({
  username: "",
  password: "",
  repassword: "",
  email: "",
  realname: "",
  gender: "",
  country: "",
  unit: "",
  contactaddress: "",
  mobile: "",
  telephone: ""
});
const rules: FormRules = {
  username: [
    {required: true, message: () => t('register.placeholder.username'), trigger: "blur"},
    {min: 6, max: 20, message: () => t('register.validate.usernameLength'), trigger: "blur"},
  ],
  password: [
    {required: true, message: () => t('register.placeholder.password'), trigger: "blur"},
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+\-=`{}\[\]|\\:;"'<>,.?/]).{6,20}$/,
      message: () => t('register.validate.passwordComplex'),
      trigger: "blur"
    },
    {min: 6, max: 20, message: () => t('register.validate.passwordLength'), trigger: "blur"}
  ],
  repassword: [
    {required: true, message: () => t('register.placeholder.confirmPassword'), trigger: "blur"},
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+\-=`{}\[\]|\\:;"'<>,.?/]).{6,20}$/,
      message: () => t('register.validate.confirmPasswordComplex'),
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        if (value !== form.password) {
          callback(new Error(t('register.validate.confirmPasswordNotMatch')));
        } else {
          callback();
        }
      }, trigger: ["blur", "change"]
    }
  ],
  email: [
    {required: true, message: () => t('register.placeholder.email'), trigger: "blur"},
    {type: "email", message: () => t('register.validate.emailFormat'), trigger: ["blur", "change"]}
  ],
  realname: [
    {required: true, message: () => t('register.placeholder.realname'), trigger: "blur"}
  ],
  gender: [
    {required: true, message: () => t('register.placeholder.gender'), trigger: "change"}
  ],
  country: [
    {required: true, message: () => t('register.placeholder.country'), trigger: "change"}
  ],
  unit: [
    {required: true, message: () => t('register.placeholder.unit'), trigger: "blur"}
  ],
  contactaddress: [
    {required: true, message: () => t('register.placeholder.contactaddress'), trigger: "blur"}
  ],
  mobile: [
    {required: true, message: () => t('register.placeholder.mobile'), trigger: "blur"}
  ],
  telephone: [
    {required: true, message: () => t('register.placeholder.telephone'), trigger: "blur"}
  ],
};
onMounted(() => {
});
const cryptoStore = useCryptoStore();

function encryptPassword(password: string) {
  const iv = CryptoJS.enc.Utf8.parse('\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0');
  const keyUtf8 = CryptoJS.enc.Utf8.parse(cryptoStore.aesKey);
  const encrypted = CryptoJS.AES.encrypt(password, keyUtf8, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  return encrypted.toString();
}

const submitForm = async () => {
  if (!RegisterFormRef.value) return;
  try {
    await RegisterFormRef.value.validate();
    const encrypt_password = encryptPassword(form.password).toString();
    const encrypt_repassword = encryptPassword(form.repassword).toString();
    if (dialogType.value === "add") {
      form.password = encrypt_password;
      form.repassword = encrypt_repassword;
      await Register(form);
      ElMessage.success("新增/注册成功 / Registration successful");
      router.push({name: 'login'})
    }
    dialogVisible.value = false;
  } catch (error) {
    console.error("提交表单失败:", error);
  }
}
</script>

<style lang="scss" scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 10px;
  padding-bottom: 40px;
}

.register-container {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 32px rgba(64, 158, 255, 0.08);
  padding: 48px 40px 32px 40px;
  width: 800px;
  max-width: 90vw;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.header {
  text-align: center;
  margin-bottom: 32px;
  position: relative;

  .logo {
    height: 40px;
    margin-bottom: 12px;
  }

  h2 {
    font-size: 28px;
    color: #1976d2;
    font-weight: bold;
    margin: 0;
    letter-spacing: 2px;
  }

  .lang-select {
    position: absolute;
    top: 0;
    right: 0;
  }
}

.el-form {
  margin-bottom: 24px;
}

.register-actions {
  text-align: right;
  margin-top: 12px;

  .login-link {
    margin-left: 16px;
    color: #1976d2;
    font-size: 14px;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

@media (max-width: 600px) {
  .register-container {
    padding: 24px 8px;
    width: 100%;
  }
}

.el-form-item {
  margin-bottom: 36px;
}
</style>
  