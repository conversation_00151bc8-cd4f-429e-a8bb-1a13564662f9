<template>

  <div class="app-container">
    <div class="header">
      <h2>{{ $t('register.title') }}</h2>
    </div>
    <el-form ref="RegisterFormRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item :label="$t('register.label.username')" prop="username">
        <el-input v-model="form.username" :placeholder="$t('register.placeholder.username')" tabindex="1" />
      </el-form-item>
      <el-form-item :label="$t('register.label.password')" prop="password">
        <el-input v-model="form.password" type="password" :placeholder="$t('register.placeholder.password')" tabindex="2" />
      </el-form-item>
      <el-form-item :label="$t('register.label.confirmPassword')" prop="repassword">
        <el-input v-model="form.repassword" type="password" :placeholder="$t('register.placeholder.confirmPassword')" tabindex="3" />
      </el-form-item>
      <el-form-item :label="$t('register.label.email')" prop="email">
        <el-input v-model="form.email" :placeholder="$t('register.placeholder.email')" tabindex="4" />
      </el-form-item>
      <el-form-item :label="$t('register.label.realname')" prop="realname">
        <el-input v-model="form.realname" :placeholder="$t('register.placeholder.realname')" tabindex="5" />
      </el-form-item>
      <el-form-item :label="$t('register.label.gender')" prop="gender">
        <el-select v-model="form.gender" :placeholder="$t('register.placeholder.gender')" tabindex="6">
          <el-option value="男" label="男" />
          <el-option value="女" label="女" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('register.label.country')" prop="country">
        <el-select v-model="form.country" :placeholder="$t('register.placeholder.country')" tabindex="7">
          <el-option value="中国" label="中国" />
          <el-option value="其他" label="其他" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('register.label.unit')" prop="unit">
        <el-input v-model="form.unit" :placeholder="$t('register.placeholder.unit')" tabindex="8" />
      </el-form-item>
      <el-form-item :label="$t('register.label.contactaddress')" prop="contactaddress">
        <el-input v-model="form.contactaddress" :placeholder="$t('register.placeholder.contactaddress')" tabindex="9" />
      </el-form-item>
      <el-form-item :label="$t('register.label.mobile')" prop="mobile">
        <el-input v-model="form.mobile" :placeholder="$t('register.placeholder.mobile')" tabindex="10" />
      </el-form-item>
      <el-form-item :label="$t('register.label.telephone')" prop="telephone">
        <el-input v-model="form.telephone" :placeholder="$t('register.placeholder.telephone')" tabindex="11" />
      </el-form-item>
    </el-form>
    <div style="text-align:right;">
      <el-button type="primary" @click="submitForm">{{ $t('register.label.btnRegister') }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import {
    Register,
} from "@/api/rbac";
import {
    RegisterParams,
} from "@/dtos";
import type { ElTree } from "element-plus";
import { RoleType, CommonStatus } from "@/enums";
import { useI18n } from "vue-i18n";
import CryptoJS from "crypto-js";
import { useCryptoStore } from '@/stores/crypto';
import { useRouter } from 'vue-router'

const router = useRouter()
const { t } = useI18n();
// 搜索表单项配置

const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");
const RegisterFormRef = ref<FormInstance>();

const form = reactive<RegisterParams>({
    username: "",
    password: "",
    repassword: "",
    email: "",
    realname: "",
    gender: "",
    country: "",
    unit: "",
    contactaddress: "",
    mobile: "",
    telephone: ""
});

const rules: FormRules = {
    username: [
        { required: true, message: t('register.placeholder.username'), trigger: "blur" },
        {
            min: 6,
            max: 20,
            message: "用户名称长度在 6 到 20 个字符",
            trigger: "blur",
        },
    ],

    password: [
        { required: true, message: t('register.placeholder.password'), trigger: "blur" },
        {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+\-=`{}[\]|\\:;"'<>,.?/]).{6,20}$/,
            message: "密码需包含大小写字母、数字、特殊字符，长度6-20位",
            trigger: "blur",
        },
        {
            min: 6,
            max: 20,
            message: "用户密码长度在 6 到 20 个字符",
            trigger: "blur",
        }
    ],
    repassword: [
        { required: true, message: t('register.placeholder.confirmPassword'), trigger: "blur" },
        {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+\-=`{}[\]|\\:;"'<>,.?/]).{6,20}$/,
            message: "确认密码需包含大小写字母、数字、特殊字符，长度6-20位",
            trigger: "blur",
        },
         {
            validator: (rule, value, callback) => {
                if (value !== form.password) {
                    callback(new Error("两次输入的密码不一致"));
                } else {
                    callback();
                }
            },
            trigger: ["blur", "change"]
        }
    ],
    email: [
        { required: true, message: t('register.placeholder.email'), trigger: "blur" },
        {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"]
        }
    ],
    realname: [
        { required: true, message:  t('register.placeholder.realname'), trigger: "blur" }
    ],
    gender: [
        { required: true, message:  t('register.placeholder.gender'), trigger: "change" }
    ],
    country: [
        { required: true, message:  t('register.placeholder.country'), trigger: "change" }
    ],
    unit: [
        { required: true, message:  t('register.placeholder.unit'), trigger: "blur" }
    ],
    contactaddress: [
        { required: true, message:  t('register.placeholder.contactaddress'), trigger: "blur" }
    ],
    mobile: [
        { required: true, message:  t('register.placeholder.mobile'), trigger: "blur" }
    ],
    telephone: [
        { required: true, message:  t('register.placeholder.telephone'), trigger: "blur" }
    ],
};

onMounted(() => {

});
const cryptoStore = useCryptoStore();

function encryptPassword(password: string) {
    // 明确指定IV为全0
    const iv = CryptoJS.enc.Utf8.parse('\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0');
    const keyUtf8 = CryptoJS.enc.Utf8.parse(cryptoStore.aesKey);
    const encrypted = CryptoJS.AES.encrypt(password, keyUtf8, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString(); // Base64
}
const submitForm = async () => {
    if (!RegisterFormRef.value) return;

    try {
        await RegisterFormRef.value.validate();
        const encrypt_password = encryptPassword(form.password).toString();
        const encrypt_repassword = encryptPassword(form.repassword).toString();
        if (dialogType.value === "add") {
            form.password = encrypt_password;
            form.repassword = encrypt_repassword;
            await Register(form);
            ElMessage.success("新增/注册成功 / Registration successful");
            router.push({ name: 'login' })
        }
        dialogVisible.value = false;

    } catch (error) {
        console.error("提交表单失败:", error);
        // ElMessage.error("提交表单失败");
    }
};
</script>

<style lang="scss" scoped>
.app-container {
    padding: 20px;
    width: 1150px;
    margin: 0 auto;
    padding-top: 200px;
    border: 1px #dcdcdc solid;
}

.footer {
    text-align: right;
    padding-top: 20px;
}

.el-tree {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 10px;
}

.el-form-item__content {
    line-height: 1.5;
}

.permission-dialog {
    .el-dialog__body {
        padding-top: 10px;
    }

    .el-tree {
        width: 100%;
    }
}
</style>
