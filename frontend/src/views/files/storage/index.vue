<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      title="存储列表"
      :data="storageList"
      :columns="tableColumns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #toolbar>
        <el-button
          type="primary"
          @click="handleAdd"
          v-hasPermission="['files:storage:add']"
        >
          <el-icon><Plus /></el-icon>
          新增存储
        </el-button>
      </template>
      
      <!-- 存储协议列 -->
      <template #protocol="{ row }">
        <el-tag v-if="row.protocol === StorageProtocol.Local" type="primary">本地存储</el-tag>
        <el-tag v-else-if="row.protocol === StorageProtocol.Nas" type="success">NAS存储</el-tag>
        <el-tag v-else-if="row.protocol === StorageProtocol.S3" type="warning">S3存储</el-tag>
        <el-tag v-else-if="row.protocol === StorageProtocol.Ftp" type="info">FTP存储</el-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button
          type="primary"
          link
          @click="handleEdit(row)"
          v-hasPermission="['files:storage:edit']"
        >
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-button
          type="danger"
          link
          @click="handleDelete(row)"
          v-hasPermission="['files:storage:delete']"
        >
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </template>
    </DataTable>

    <!-- 存储表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="storageFormRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="存储编码" prop="storageCode">
          <el-input
            v-model="form.storageCode"
            placeholder="请输入存储编码"
            :readonly="dialogType === 'edit'"
            :disabled="dialogType === 'edit'"
          />
        </el-form-item>
        <el-form-item label="存储名称" prop="storageName">
          <el-input
            v-model="form.storageName"
            placeholder="请输入存储名称"
          />
        </el-form-item>
        <el-form-item label="存储协议" prop="protocol">
          <el-select v-model="form.protocol" placeholder="请选择存储协议" style="width: 100%">
            <el-option :value="StorageProtocol.Local" label="本地存储" />
            <el-option :value="StorageProtocol.Nas" label="NAS存储" />
            <el-option :value="StorageProtocol.S3" label="S3存储" />
            <el-option :value="StorageProtocol.Ftp" label="FTP存储" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务地址" prop="endpoint">
          <el-input
            v-model="form.endpoint"
            placeholder="请输入存储服务地址"
          />
        </el-form-item>
        <el-form-item label="访问Key" prop="accessKey">
          <el-input
            v-model="form.accessKey"
            placeholder="请输入访问Key"
          />
        </el-form-item>
        <el-form-item label="访问Secret" prop="secretKey">
          <el-input
            v-model="form.secretKey"
            placeholder="请输入访问Secret"
            type="password"
            show-password
          />
        </el-form-item>
        <el-form-item label="桶/容器名" prop="bucket">
          <el-input
            v-model="form.bucket"
            placeholder="请输入桶/容器名"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { Plus, Edit, Delete } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import SearchForm, { FormItem } from "@/components/common/SearchForm.vue";
import DataTable, { TableColumn } from "@/components/common/DataTable.vue";
import {
  getStoragePage,
  createStorage,
  updateStorage,
  deleteStorage,
} from "@/api/files-mgt";
import {
  StorageDto,
  StorageOperationDto,
  StorageQueryParams,
} from "@/dtos";
import { StorageProtocol } from "@/enums";

// 搜索表单项配置
const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "存储编码",
    prop: "Code",
    placeholder: "请输入存储编码",
  },
  {
    type: "input",
    label: "存储名称",
    prop: "Name",
    placeholder: "请输入存储名称",
  },
]);

// 表格列配置
const tableColumns = ref<TableColumn[]>([
  { prop: "storageCode", label: "存储编码", width: 150 },
  { prop: "storageName", label: "存储名称", width: 150 },
  { prop: "protocol", label: "存储协议", slot: "protocol", width: 120 },
  { prop: "endpoint", label: "服务地址" },
  { prop: "bucket", label: "桶/容器名", width: 150 },
  { prop: "remark", label: "备注" },
]);

const loading = ref(false);
const total = ref(0);
const storageList = ref<StorageDto[]>([]);
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");
const storageFormRef = ref<FormInstance>();

const queryParams = reactive<StorageQueryParams>({
  $pageIndex: 1,
  $pageSize: 10,
  Code: "",
  Name: "",
});

const form = reactive<StorageOperationDto>({
  storageCode: "",
  storageName: "",
  endpoint: "",
  protocol: StorageProtocol.Local,
  accessKey: "",
  secretKey: "",
  bucket: "",
  remark: "",
});

// 当前编辑的存储ID
const currentEditId = ref<string>("");

const rules: FormRules = {
  storageCode: [
    { required: true, message: "请输入存储编码", trigger: "blur" },
    {
      pattern: /^[A-Za-z0-9_-]+$/,
      message: "存储编码只能包含字母、数字、下划线和连字符",
      trigger: "blur",
    },
  ],
  storageName: [
    { required: true, message: "请输入存储名称", trigger: "blur" },
  ],
  protocol: [
    { required: true, message: "请选择存储协议", trigger: "change" },
  ],
  endpoint: [
    { required: true, message: "请输入存储服务地址", trigger: "blur" },
  ],
};

const dialogTitle = computed(() => {
  return dialogType.value === "add" ? "新增存储" : "编辑存储";
});

onMounted(() => {
  getList();
});

const getList = async () => {
  try {
    loading.value = true;
    const response = await getStoragePage(queryParams);
    console.log("存储列表数据:", response);

    const { data } = response;
    storageList.value = data.rows || [];

    // 确保 totals 是数字类型
    console.log("原始 totals 值:", data.totals, "类型:", typeof data.totals);

    // 尝试多种方式转换
    let totalNumber = 0;

    if (data.totals !== undefined && data.totals !== null) {
      if (typeof data.totals === "number") {
        totalNumber = data.totals;
      } else if (typeof data.totals === "string") {
        // 尝试将字符串转换为数字
        totalNumber = parseInt(data.totals, 10) || 0;
      }
    }

    console.log("转换后的 total 值:", totalNumber, "类型:", typeof totalNumber);

    total.value = totalNumber;
  } catch (error) {
    console.error("获取存储列表失败", error);
    ElMessage.error("获取存储列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

const resetForm = () => {
  form.storageCode = "";
  form.storageName = "";
  form.endpoint = "";
  form.protocol = StorageProtocol.Local;
  form.accessKey = "";
  form.secretKey = "";
  form.bucket = "";
  form.remark = "";
  currentEditId.value = "";
};

const handleAdd = () => {
  resetForm();
  dialogType.value = "add";
  dialogVisible.value = true;
};

const handleEdit = (row: StorageDto) => {
  resetForm();
  dialogType.value = "edit";
  dialogVisible.value = true;

  // 将行数据复制到表单
  form.storageCode = row.storageCode || "";
  form.storageName = row.storageName || "";
  form.endpoint = row.endpoint || "";
  form.protocol = row.protocol;
  form.accessKey = row.accessKey || "";
  form.secretKey = row.secretKey || "";
  form.bucket = row.bucket || "";
  form.remark = row.remark || "";

  // 保存当前编辑的存储ID
  currentEditId.value = row.key;
};

const handleDelete = async (row: StorageDto) => {
  try {
    await ElMessageBox.confirm("确认要删除该存储吗？", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    await deleteStorage(row.key);
    ElMessage.success("删除成功");
    getList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除存储失败:", error);
      ElMessage.error("删除存储失败");
    }
  }
};

const submitForm = async () => {
  if (!storageFormRef.value) return;

  try {
    await storageFormRef.value.validate();
    if (dialogType.value === "add") {
      await createStorage(form);
      ElMessage.success("新增成功");
    } else {
      await updateStorage(currentEditId.value, form);
      ElMessage.success("修改成功");
    }
    dialogVisible.value = false;
    getList();
  } catch (error) {
    console.error("提交表单失败:", error);
    ElMessage.error("提交表单失败");
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}
</style>
