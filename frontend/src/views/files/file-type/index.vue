<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      title="文件类型列表"
      :data="fileTypeList"
      :columns="tableColumns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #toolbar>
        <el-button
          type="primary"
          @click="handleAdd"
          v-hasPermission="['files:file-type:add']"
        >
          <el-icon><Plus /></el-icon>
          新增文件类型
        </el-button>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button
          type="primary"
          link
          @click="handleEdit(row)"
          v-hasPermission="['files:file-type:edit']"
        >
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-button
          type="primary"
          link
          @click="handleStorageRel(row)"
          v-hasPermission="['files:file-type:storage']"
        >
          <el-icon><Setting /></el-icon>
          存储配置
        </el-button>
        <el-button
          type="danger"
          link
          @click="handleDelete(row)"
          v-hasPermission="['files:file-type:delete']"
        >
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </template>
    </DataTable>

    <!-- 文件类型表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="700px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="fileTypeFormRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="类型编码" prop="typeCode">
          <el-input
            v-model="form.typeCode"
            placeholder="请输入类型编码"
            :readonly="dialogType === 'edit'"
            :disabled="dialogType === 'edit'"
          />
        </el-form-item>
        <el-form-item label="类型名称" prop="typeName">
          <el-input v-model="form.typeName" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="允许的文件后缀" prop="extension">
          <el-input
            v-model="form.extension"
            placeholder="请输入允许的文件后缀，如.jpg,.png"
          />
        </el-form-item>
        <el-form-item label="大小限制(字节)" prop="sizeLimit">
          <el-input
            v-model="form.sizeLimit"
            placeholder="请输入大小限制，单位为字节"
            type="number"
          />
        </el-form-item>
        <el-form-item label="分块大小限制(字节)" prop="chunkLimit">
          <el-input
            v-model="form.chunkLimit"
            placeholder="请输入分块大小限制，单位为字节"
            type="number"
          />
        </el-form-item>
        <el-form-item label="所需权限类型" prop="permissionRequired">
          <el-select
            v-model="form.permissionRequired"
            placeholder="请选择所需权限类型"
            style="width: 100%"
          >
            <el-option value="none" label="无" />
            <el-option value="user" label="用户" />
            <el-option value="role" label="角色" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 存储关系对话框 -->
    <el-dialog
      title="存储配置"
      v-model="storageRelDialogVisible"
      width="600px"
      append-to-body
      destroy-on-close
    >
      <div v-if="currentFileType">
        <p>
          文件类型: {{ currentFileType.typeName }} ({{
            currentFileType.typeCode
          }})
        </p>

        <el-table
          :data="storageRelList"
          border
          style="width: 100%; margin-top: 15px"
          v-loading="storageRelLoading"
        >
          <el-table-column prop="storageCode" label="存储编码" width="250" />
          <el-table-column prop="priority" label="优先级" width="100" />
          <el-table-column label="操作">
            <template #default="{ row }">
              <el-button
                type="danger"
                link
                @click="handleDeleteStorageRel(row)"
                v-hasPermission="['files:file-type:storage:delete']"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-divider>添加存储关系</el-divider>

        <el-form
          ref="storageRelFormRef"
          :model="storageRelForm"
          :rules="storageRelRules"
          label-width="100px"
        >
          <el-form-item label="存储" prop="storageCode">
            <el-select
              v-model="storageRelForm.storageCode"
              placeholder="请选择存储"
              style="width: 100%"
            >
              <el-option
                v-for="item in storageOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="优先级" prop="priority">
            <el-input-number
              v-model="storageRelForm.priority"
              :min="1"
              :max="100"
              placeholder="请输入优先级，数字越小优先级越高"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="submitStorageRel"
              v-hasPermission="['files:file-type:storage:add']"
            >添加</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="storageRelDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { Plus, Edit, Delete, Setting } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import SearchForm, { FormItem } from "@/components/common/SearchForm.vue";
import DataTable, { TableColumn } from "@/components/common/DataTable.vue";
import {
  getFileTypePage,
  createFileType,
  updateFileType,
  deleteFileType,
  getStorageList,
  getFileTypeStorageRelList,
  createFileTypeStorageRel,
  deleteFileTypeStorageRel,
} from "@/api/files-mgt";
import {
  FileTypeDto,
  FileTypeOperationDto,
  FileTypeQueryParams,
  FileTypeStorageRelDto,
  FileTypeStorageRelOperationDto,
} from "@/dtos";

// 搜索表单项配置
const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "类型名称",
    prop: "Name",
    placeholder: "请输入类型名称",
  },
]);

// 表格列配置
const tableColumns = ref<TableColumn[]>([
  { prop: "typeCode", label: "类型编码", width: 150 },
  { prop: "typeName", label: "类型名称", width: 150 },
  { prop: "extension", label: "允许的文件后缀", width: 200 },
  { prop: "sizeLimit", label: "大小限制(字节)", width: 150 },
  { prop: "chunkLimit", label: "分块大小限制(字节)", width: 150 },
  { prop: "permissionRequired", label: "所需权限类型", width: 120 },
  { prop: "remark", label: "备注" },
]);

const loading = ref(false);
const total = ref(0);
const fileTypeList = ref<FileTypeDto[]>([]);
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");
const fileTypeFormRef = ref<FormInstance>();

// 存储关系相关
const storageRelDialogVisible = ref(false);
const storageRelLoading = ref(false);
const storageRelList = ref<FileTypeStorageRelDto[]>([]);
const currentFileType = ref<FileTypeDto | null>(null);
const storageOptions = ref<{ label: string; value: string }[]>([]);
const storageRelFormRef = ref<FormInstance>();

const queryParams = reactive<FileTypeQueryParams>({
  $pageIndex: 1,
  $pageSize: 10,
  Name: "",
});

const form = reactive<FileTypeOperationDto>({
  typeCode: "",
  typeName: "",
  extension: "",
  sizeLimit: "0",
  chunkLimit: "0",
  remark: "",
  permissionRequired: "none",
});

const storageRelForm = reactive<FileTypeStorageRelOperationDto>({
  fileTypeCode: "",
  storageCode: "",
  priority: 1,
});

// 当前编辑的文件类型ID
const currentEditId = ref<string>("");

const rules: FormRules = {
  typeCode: [
    { required: true, message: "请输入类型编码", trigger: "blur" },
    {
      pattern: /^[A-Za-z0-9_-]+$/,
      message: "类型编码只能包含字母、数字、下划线和连字符",
      trigger: "blur",
    },
  ],
  typeName: [{ required: true, message: "请输入类型名称", trigger: "blur" }],
  extension: [
    { required: true, message: "请输入允许的文件后缀", trigger: "blur" },
  ],
  sizeLimit: [{ required: true, message: "请输入大小限制", trigger: "blur" }],
  chunkLimit: [
    { required: true, message: "请输分块大小限制", trigger: "blur" },
  ],
};

const storageRelRules: FormRules = {
  storageCode: [{ required: true, message: "请选择存储", trigger: "change" }],
  priority: [{ required: true, message: "请输入优先级", trigger: "blur" }],
};

const dialogTitle = computed(() => {
  return dialogType.value === "add" ? "新增文件类型" : "编辑文件类型";
});

onMounted(() => {
  getList();
  loadStorageOptions();
});

const getList = async () => {
  try {
    loading.value = true;
    const response = await getFileTypePage(queryParams);
    console.log("文件类型列表数据:", response);

    const { data } = response;
    fileTypeList.value = data.rows || [];

    // 确保 totals 是数字类型
    let totalNumber = 0;

    if (data.totals !== undefined && data.totals !== null) {
      if (typeof data.totals === "number") {
        totalNumber = data.totals;
      } else if (typeof data.totals === "string") {
        // 尝试将字符串转换为数字
        totalNumber = parseInt(data.totals, 10) || 0;
      }
    }

    total.value = totalNumber;
  } catch (error) {
    console.error("获取文件类型列表失败", error);
    // ElMessage.error("获取文件类型列表失败");
  } finally {
    loading.value = false;
  }
};

const loadStorageOptions = async () => {
  try {
    const response = await getStorageList();
    const storageList = response.data || [];

    storageOptions.value = storageList.map((storage) => ({
      label: `${storage.storageName} (${storage.storageCode})`,
      value: storage.storageCode || "",
    }));
  } catch (error) {
    console.error("获取存储列表失败", error);
    // ElMessage.error("获取存储列表失败");
  }
};

const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

const resetForm = () => {
  form.typeCode = "";
  form.typeName = "";
  form.extension = "";
  form.sizeLimit = "0";
  form.remark = "";
  form.permissionRequired = "none";
  currentEditId.value = "";
};

const resetStorageRelForm = () => {
  storageRelForm.fileTypeCode = "";
  storageRelForm.storageCode = "";
  storageRelForm.priority = 1;
};

const handleAdd = () => {
  resetForm();
  dialogType.value = "add";
  dialogVisible.value = true;
};

const handleEdit = (row: FileTypeDto) => {
  resetForm();
  dialogType.value = "edit";
  dialogVisible.value = true;

  // 将行数据复制到表单
  form.typeCode = row.typeCode || "";
  form.typeName = row.typeName || "";
  form.extension = row.extension || "";
  form.sizeLimit = row.sizeLimit || "0";
  form.chunkLimit = row.chunkLimit || "0";
  form.remark = row.remark || "";
  form.permissionRequired = row.permissionRequired || "none";

  // 保存当前编辑的文件类型ID
  currentEditId.value = row.key;
};

const handleDelete = async (row: FileTypeDto) => {
  try {
    await ElMessageBox.confirm("确认要删除该文件类型吗？", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    await deleteFileType(row.key);
    ElMessage.success("删除成功");
    getList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除文件类型失败:", error);
      ElMessage.error("删除文件类型失败");
    }
  }
};

const submitForm = async () => {
  if (!fileTypeFormRef.value) return;

  try {
    await fileTypeFormRef.value.validate();
    if (dialogType.value === "add") {
      await createFileType(form);
      ElMessage.success("新增成功");
    } else {
      await updateFileType(currentEditId.value, form);
      ElMessage.success("修改成功");
    }
    dialogVisible.value = false;
    getList();
  } catch (error) {
    console.error("提交表单失败:", error);
    ElMessage.error("提交表单失败");
  }
};

// 存储关系相关方法
const handleStorageRel = async (row: FileTypeDto) => {
  currentFileType.value = row;
  storageRelDialogVisible.value = true;
  resetStorageRelForm();
  storageRelForm.fileTypeCode = row.typeCode || "";

  await loadStorageRelList(row.typeCode || "");
};

const loadStorageRelList = async (fileTypeCode: string) => {
  try {
    storageRelLoading.value = true;
    const response = await getFileTypeStorageRelList({
      StorageCode: fileTypeCode,
    });
    storageRelList.value = response.data || [];
  } catch (error) {
    console.error("获取存储关系列表失败", error);
    ElMessage.error("获取存储关系列表失败");
  } finally {
    storageRelLoading.value = false;
  }
};

const handleDeleteStorageRel = async (row: FileTypeStorageRelDto) => {
  try {
    await ElMessageBox.confirm("确认要删除该存储关系吗？", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    await deleteFileTypeStorageRel(row.key);
    ElMessage.success("删除成功");

    // 重新加载存储关系列表
    if (currentFileType.value) {
      await loadStorageRelList(currentFileType.value.typeCode || "");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除存储关系失败:", error);
      ElMessage.error("删除存储关系失败");
    }
  }
};

const submitStorageRel = async () => {
  if (!storageRelFormRef.value) return;

  try {
    await storageRelFormRef.value.validate();
    await createFileTypeStorageRel(storageRelForm);
    ElMessage.success("添加成功");
    resetStorageRelForm();

    // 重新加载存储关系列表
    if (currentFileType.value) {
      storageRelForm.fileTypeCode = currentFileType.value.typeCode || "";
      await loadStorageRelList(currentFileType.value.typeCode || "");
    }
  } catch (error) {
    console.error("提交存储关系失败:", error);
    ElMessage.error("提交存储关系失败");
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.el-table th,
.el-table td {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
