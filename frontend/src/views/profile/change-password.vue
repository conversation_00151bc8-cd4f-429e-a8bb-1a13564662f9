<template>
  <div class="app-container">
    <el-card class="password-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('profile.changepassword.title') }}</span>
        </div>
      </template>
      <div class="password-content">
        <el-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          label-width="180px"
        >
          <el-form-item :label="$t('profile.changepassword.oldPassword')" prop="oldPassword">
            <el-input
              v-model="passwordForm.oldPassword"
              type="password"
              :placeholder="$t('profile.changepassword.oldPasswordrequired')"
              show-password
            />
          </el-form-item>
          <el-form-item :label="$t('profile.changepassword.newPassword')" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              :placeholder="$t('profile.changepassword.newPasswordrequired')"
              show-password
            />
          </el-form-item>
          <el-form-item :label="$t('profile.changepassword.confirmNewPassword')" prop="confirmNewPassword">
            <el-input
              v-model="passwordForm.confirmNewPassword"
              type="password"
              :placeholder="$t('profile.changepassword.confirmNewPasswordrequired')"
              show-password
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="submitChangePassword"
              :loading="passwordSubmitting"
            >
              {{ t('btnSave') }}
            </el-button>
            <el-button @click="resetForm">{{ t('btnReset') }}</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import {ElMessage, FormInstance} from "element-plus";
import {changePassword} from "@/api/rbac";
import {useUserStore} from "@/stores/user";
import {ChangePasswordParams} from "@/dtos";
import {useI18n} from "vue-i18n";
import { useRouter } from 'vue-router'

const router = useRouter()

const {t} = useI18n();
// 密码表单
const passwordForm = reactive<ChangePasswordParams>({
  oldPassword: "",
  newPassword: "",
  confirmNewPassword: "",
});

// 密码表单验证规则
const passwordRules = {
  oldPassword: [{required: true, message: t('profile.changepassword.oldPasswordrequired'), trigger: "blur"}],
  newPassword: [
    {required: true, message: t('profile.changepassword.newPasswordrequired'), trigger: "blur"},
    {min: 6, message: t('profile.changepassword.passwordlength'), trigger: "blur"},
  ],
  confirmNewPassword: [
    {required: true, message: t('profile.changepassword.confirmNewPasswordrequired'), trigger: "blur"},
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error(t('profile.changepassword.passwordMismatch')));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

const passwordFormRef = ref<FormInstance>();
const passwordSubmitting = ref(false);

// 提交修改密码
const submitChangePassword = async () => {
  if (!passwordFormRef.value) return;

  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      passwordSubmitting.value = true;
      try {
        await changePassword(passwordForm);
        ElMessage.success("密码修改成功，请重新登录/Password changed successfully, please log in again.");

        // 修改密码成功后，登出并跳转到登录页
        const userStore = useUserStore();
        await userStore.logout();
        await router.push({path: "/login"});

      } catch (error: any) {
        ElMessage.error(error.message || "密码修改失败/Password change failed.");
      } finally {
        passwordSubmitting.value = false;
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields();
  }
};
</script>

<style lang="scss" scoped>
.password-card {
  max-width: 600px;
  margin: 0 auto;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .password-content {
    padding: 20px 0;
  }
}
</style>
