<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <search-form
      :form-items="searchFormItems"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <data-table
      title="权限列表"
      :showPagination="false"
      :data="permissionTreeData"
      :columns="tableColumns"
      :loading="loading"
      row-key="key"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :default-expand-all="true"
    >
      <!-- 工具栏插槽 -->
      <template #toolbar>
        <el-button
          type="primary"
          @click="handleAdd"
          v-hasPermission="['system:permission:add']"
        >
          <el-icon><Plus /></el-icon>
          新增权限
        </el-button>
        <el-button
          type="success"
          @click="showInitApiDialog"
          v-hasPermission="['system:permission:add']"
        >
          <el-icon><Download /></el-icon>
          初始化API权限
        </el-button>
      </template>

      <!-- 权限类型列 -->
      <template #type="{ row }">
        <el-tag v-if="row.type === PermissionType.Directory" type="info"
        >目录</el-tag
        >
        <el-tag v-else-if="row.type === PermissionType.Menu" type="success"
        >菜单</el-tag
        >
        <el-tag v-else-if="row.type === PermissionType.Button" type="warning"
        >按钮</el-tag
        >
        <el-tag v-else-if="row.type === PermissionType.Api" type="primary"
        >API接口</el-tag
        >
        <el-tag v-else type="danger">未知({{ row.type }})</el-tag>
      </template>

      <template #name="{ row }">
        <el-icon v-if="row.icon">
          <component :is="getIconComponent(row.icon)" />
        </el-icon>
        {{ row.name }}
      </template>
      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag
          :type="row.status === CommonStatus.Enabled ? 'success' : 'danger'"
        >
          {{ row.status === CommonStatus.Enabled ? "启用" : "禁用" }}
        </el-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button
          type="primary"
          link
          @click="handleEdit(row)"
          v-hasPermission="['system:permission:edit']"
        >
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-button
          type="primary"
          link
          @click="handleAddChild(row)"
          v-hasPermission="['system:permission:add']"
        >
          <el-icon><Plus /></el-icon>
          添加子权限
        </el-button>
        <el-button
          type="danger"
          link
          @click="handleDelete(row)"
          v-hasPermission="['system:permission:delete']"
        >
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </template>
    </data-table>

    <!-- 初始化API权限对话框 -->
    <el-dialog
      title="初始化API权限"
      v-model="initApiDialogVisible"
      width="600px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="initApiFormRef"
        :model="initApiForm"
        :rules="initApiRules"
        label-width="120px"
      >
        <el-form-item label="应用选择" prop="apiSource">
          <el-select
            v-model="initApiForm.apiSource"
            placeholder="请选择应用"
            style="width: 100%"
          >
            <el-option
              v-for="app in appInfoList"
              :key="app.appCode"
              :label="`${app.appName} (${app.appCode})`"
              :value="app.appCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="应用代码" class="detected-info">
          <el-tag type="success">{{ getSelectedApp()?.appCode || "" }}</el-tag>
        </el-form-item>
        <el-form-item label="应用名称" class="detected-info">
          <el-tag type="success">{{ getSelectedApp()?.appName || "" }}</el-tag>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="initApiDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="fetchApiPermissions"
            :loading="fetchingApi"
          >获取API列表</el-button
          >
          <el-button
            type="success"
            @click="submitInitApi"
            :loading="submittingApi"
            :disabled="!apiPermissions.length"
          >
            初始化权限
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="上级权限" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="permissionTreeData"
            :props="{ label: 'name', value: 'key', children: 'children' }"
            placeholder="请选择上级权限"
            check-strictly
            clearable
          />
        </el-form-item>
        <el-form-item label="应用编码" prop="appCode">
          <el-input v-model="form.appCode" placeholder="请输入应用编码" />
        </el-form-item>
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="权限编码" prop="code">
          <el-input
            v-model="form.code"
            placeholder="请输入权限编码"
            :readonly="isEdit"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="权限类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="请选择权限类型"
            @change="handleTypeChange"
          >
            <el-option :value="PermissionType.Directory" label="目录" />
            <el-option :value="PermissionType.Menu" label="菜单" />
            <el-option :value="PermissionType.Button" label="按钮" />
            <el-option :value="PermissionType.Api" label="API接口" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.type === PermissionType.Api"
          label="路由路径"
          prop="path"
        >
          <el-input
            v-model="form.path"
            placeholder="请输入API路径"
          />
        </el-form-item>
        <el-form-item
          v-if="false"
          label="组件路径"
          prop="component"
        >
          <el-input v-model="form.component" placeholder="请输入组件路径" />
        </el-form-item>
        <el-form-item
          v-if="
            form.type === PermissionType.Menu ||
              form.type === PermissionType.Directory
          "
          label="重定向"
          prop="redirect"
        >
          <el-input v-model="form.redirect" placeholder="请输入重定向地址" />
        </el-form-item>
        <el-form-item
          v-if="
            form.type === PermissionType.Menu ||
              form.type === PermissionType.Directory
          "
          label="图标"
          prop="icon"
        >
          <el-input v-model="form.icon" placeholder="请输入图标" />
        </el-form-item>
        <el-form-item
          v-if="form.type === PermissionType.Api"
          label="HTTP方法"
          prop="method"
        >
          <el-select v-model="form.method" placeholder="请选择HTTP方法">
            <el-option :value="HttpMethod.GET" label="GET" />
            <el-option :value="HttpMethod.POST" label="POST" />
            <el-option :value="HttpMethod.PUT" label="PUT" />
            <el-option :value="HttpMethod.DELETE" label="DELETE" />
            <el-option :value="HttpMethod.PATCH" label="PATCH" />
            <el-option :value="HttpMethod.HEAD" label="HEAD" />
            <el-option :value="HttpMethod.OPTIONS" label="OPTIONS" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" :max="9999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option :value="CommonStatus.Enabled" label="启用" />
            <el-option :value="CommonStatus.Disabled" label="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import { Plus, Edit, Delete, Download } from "@element-plus/icons-vue";
import SearchForm, { FormItem } from "@/components/common/SearchForm.vue";
import DataTable, { TableColumn } from "@/components/common/DataTable.vue";
import { PermissionDto, PermissionTreeDto, ApiEndpointInfo } from "@/dtos";
import getIconComponent from "@/utils/ui";
import { appInfoList, getAppInfo } from "@/config/app-info";
import { getApiBaseUrl } from "@/config/env";

import {
  getPermissionList,
  createPermission,
  updatePermission,
  deletePermission,
  batchUpdateApiPermissions,
} from "@/api/rbac-mgt";
import { getAppApiPermissions } from "@/api/common";
import { PermissionOperationDto, PermissionQueryParams } from "@/dtos";
import { HttpMethod, CommonStatus, PermissionType } from "@/enums";

// 权限类型和HTTP方法选项已直接在模板中使用

// 状态选项已直接在模板中使用

// 搜索表单项配置
const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "应用编码",
    prop: "appCode",
    placeholder: "请输入应用编码",
  },
  {
    type: "input",
    label: "权限名称",
    prop: "name",
    placeholder: "请输入权限名称",
  },
  {
    type: "input",
    label: "权限编码",
    prop: "code",
    placeholder: "请输入权限编码",
  },
  {
    type: "select",
    label: "权限类型",
    prop: "type",
    placeholder: "请选择权限类型",
    options: [
      { label: "目录", value: PermissionType.Directory },
      { label: "菜单", value: PermissionType.Menu },
      { label: "按钮", value: PermissionType.Button },
      { label: "API接口", value: PermissionType.Api },
    ],
  },
  {
    type: "select",
    label: "状态",
    prop: "status",
    placeholder: "请选择状态",
    options: [
      { label: "启用", value: CommonStatus.Enabled },
      { label: "禁用", value: CommonStatus.Disabled },
    ],
  },
]);

// 表格列定义
const tableColumns = ref<TableColumn[]>([
  { prop: "name", label: "权限名称", slot: "name", width: 220 },
  { prop: "appCode", label: "应用编码", width: 120 },
  { prop: "code", label: "权限编码", width: 150 },
  { prop: "type", label: "权限类型", slot: "type", width: 120 },
  { prop: "path", label: "路由/API路径", width: 220 },
  { prop: "sortOrder", label: "排序", width: 80 },
  { prop: "status", label: "状态", slot: "status", width: 100 },
  { prop: "description", label: "描述" },
]);

// 查询参数
const queryParams = reactive<PermissionQueryParams>({
  $pageIndex: 1,
  $pageSize: 10,
  appCode: "",
  name: "",
  code: "",
  type: undefined,
  status: undefined,
});

// 分页信息已在状态变量中定义

// 表单数据
const form = reactive<PermissionOperationDto>({
  parentId: undefined,
  appCode: "",
  code: "",
  name: "",
  type: PermissionType.Menu, // 菜单
  path: "",
  component: "",
  redirect: "",
  icon: "",
  method: HttpMethod.GET,
  sortOrder: 0,
  status: CommonStatus.Enabled,
  description: "",
});

// 表单验证规则
const rules = reactive<FormRules>({
  // appCode: [{ required: true, message: "请输入应用编码", trigger: "blur" }],
  name: [{ required: true, message: "请输入权限名称", trigger: "blur" }],
  code: [{ required: true, message: "请输入权限编码", trigger: "blur" }],
  type: [{ required: true, message: "请选择权限类型", trigger: "change" }],
  path: [
    {
      required: true,
      message: "请输入API路径",
      trigger: "blur",
      validator: (_rule, value, callback) => {
        if (form.type === PermissionType.Api) {
          if (!value) {
            callback(new Error("请输入API路径"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
    },
  ],
  component: [
    {
      required: false,
      message: "",
      trigger: "blur",
      validator: (_rule, value, callback) => {
        callback();
      },
    },
  ],
  method: [
    {
      required: true,
      message: "请选择HTTP方法",
      trigger: "change",
      validator: (_rule, value, callback) => {
        if (form.type === PermissionType.Api) {
          if (value === undefined) {
            callback(new Error("请选择HTTP方法"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
    },
  ],
  sortOrder: [{ required: true, message: "请输入排序号", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
});

// 状态变量
const loading = ref(false);
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");
const formRef = ref<FormInstance>();
// 不再使用平铺列表，直接使用树形结构
const permissionTreeData = ref<PermissionDto[]>([]);
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
const currentEditId = ref<string>();

// 初始化API权限相关状态
const initApiDialogVisible = ref(false);
const initApiFormRef = ref<FormInstance>();
const fetchingApi = ref(false);
const submittingApi = ref(false);
const apiPermissions = ref<ApiEndpointInfo[]>([]);
const apiInfo = reactive({
  appCode: "",
  appName: "",
});

// 初始化API权限表单
const initApiForm = reactive({
  apiSource: "rbac-mgt" as string,
});

// 初始化API权限表单验证规则
const initApiRules = reactive<FormRules>({
  apiSource: [{ required: true, message: "请选择API来源", trigger: "change" }],
});

// 计算属性
const dialogTitle = computed(() => {
  return dialogType.value === "add" ? "新增权限" : "编辑权限";
});

// 是否为编辑模式
const isEdit = computed(() => dialogType.value === "edit");

// 权限类型标签已直接在模板中使用

// 加载权限列表数据
const loadData = async () => {
  loading.value = true;
  try {
    // 获取所有权限列表，不使用分页
    const response = await getPermissionList(queryParams);
    console.log("权限列表数据:", response);

    const { data } = response;

    // 构建树形结构
    const buildTree = (
      items: PermissionDto[],
      parentId?: string | null
    ): PermissionDto[] => {
      return items
        .filter((item) => item.parentId === parentId)
        .map((item) => ({
          ...item,
          children: buildTree(items, item.key),
        }));
    };

    // 使用返回的数据构建树
    permissionTreeData.value = buildTree(
      data as unknown as PermissionDto[],
      null
    );

    // 设置总数
    pagination.total = (data as unknown as PermissionDto[]).length;
  } catch (error) {
    console.error("加载权限列表失败", error);
    ElMessage.error("加载权限列表失败");
  } finally {
    loading.value = false;
  }
};

// 不再需要单独的loadTreeData函数，因为loadData已经处理了树形数据的加载

// 处理搜索
const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  // 树形结构不需要分页，但保留搜索功能
  loadData();
};

// 处理重置
const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  loadData();
};


// 处理添加
const handleAdd = () => {
  resetForm();
  dialogType.value = "add";
  dialogVisible.value = true;
};

// 处理添加子权限
const handleAddChild = (row: PermissionDto) => {
  resetForm();
  dialogType.value = "add";
  form.parentId = row.key;
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row: PermissionDto) => {
  resetForm();
  dialogType.value = "edit";
  currentEditId.value = row.key;

  try {
    // 直接使用行数据，避免额外的API调用
    // 将key转换为parentId等属性
    const { key: _key, children: _children, ...rest } = row;
    Object.assign(form, rest);

    // 如果需要更详细的数据，可以取消注释下面的代码
    // const res = await getPermissionById(row.key);
    // const { data } = res;
    // Object.assign(form, data);
  } catch (error) {
    console.error("获取权限详情失败", error);
    ElMessage.error("获取权限详情失败");
  }

  dialogVisible.value = true;
};

// 处理删除
const handleDelete = (row: PermissionDto) => {
  // 检查是否有子权限
  if (row.children && row.children.length > 0) {
    ElMessageBox.confirm(
      `该权限下有${row.children.length}个子权限，确认删除权限 ${row.name} 及其所有子权限吗？`,
      "警告",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    )
      .then(async () => {
        try {
          await deletePermission(row.key);
          ElMessage.success("删除成功");
          // 重新加载树形数据
          permissionTreeData.value = [];
          loadData();
        } catch (error) {
          console.error("删除权限失败", error);
          ElMessage.error("删除权限失败");
        }
      })
      .catch(() => {
        // 取消删除
      });
  } else {
    ElMessageBox.confirm(`确认删除权限 ${row.name} 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        try {
          await deletePermission(row.key);
          ElMessage.success("删除成功");
          // 重新加载树形数据
          permissionTreeData.value = [];
          loadData();
        } catch (error) {
          console.error("删除权限失败", error);
          ElMessage.error("删除权限失败");
        }
      })
      .catch(() => {
        // 取消删除
      });
  }
};

// 处理权限类型变化
const handleTypeChange = (value: PermissionType) => {
  form.type = value;
  // 根据类型重置相关字段
  if (value === PermissionType.Directory || value === PermissionType.Menu) {
    // 目录或菜单
    form.method = undefined;
  } else if (value === PermissionType.Api) {
    // API接口
    // 保留path字段，因为API接口需要路径
    form.component = "";
    form.redirect = "";
    form.icon = "";
    form.method = HttpMethod.GET;
  } else if (value === PermissionType.Button) {
    // 按钮
    form.path = "";
    form.component = "";
    form.redirect = "";
    form.icon = "";
    form.method = undefined;
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(form, {
    parentId: undefined,
    appCode: "",
    code: "",
    name: "",
    type: PermissionType.Menu, // 菜单
    path: "",
    component: "",
    redirect: "",
    icon: "",
    method: HttpMethod.GET,
    sortOrder: 0,
    status: CommonStatus.Enabled,
    description: "",
  });
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (!valid) return;

    try {
      if (isEdit.value && currentEditId.value !== undefined) {
        // 编辑
        await updatePermission(currentEditId.value, form);
        ElMessage.success("更新成功");
      } else {
        // 新增
        await createPermission(form);
        ElMessage.success("创建成功");
      }
      dialogVisible.value = false;

      // 清空树形数据缓存，强制重新加载
      permissionTreeData.value = [];
      loadData();
    } catch (error) {
      console.error("保存权限失败", error);
      ElMessage.error("保存权限失败");
    }
  });
};

// 获取选中的应用信息
const getSelectedApp = () => {
  return getAppInfo(initApiForm.apiSource);
};

// 显示初始化API权限对话框
const showInitApiDialog = () => {
  initApiDialogVisible.value = true;
  // 重置表单
  if (initApiFormRef.value) {
    initApiFormRef.value.resetFields();
  }
  // 重置API权限列表
  apiPermissions.value = [];
};

// 获取API权限列表
const fetchApiPermissions = async () => {
  if (!initApiFormRef.value) return;

  await initApiFormRef.value.validate(async (valid) => {
    if (!valid) return;

    const selectedApp = getSelectedApp();
    if (!selectedApp) {
      ElMessage.error("请选择有效的应用");
      return;
    }


    const apiUrl = getApiBaseUrl(selectedApp.appCode);

    fetchingApi.value = true;
    try {
      let response;

      // 根据应用信息获取API权限列表
      response = await getAppApiPermissions(apiUrl);

      if (response && response.data) {
        // 获取所有API权限
        const allApiPermissions = response.data;

        // 过滤掉公共权限
        apiPermissions.value = allApiPermissions.filter(
          (endpoint) => !endpoint.public
        );

        // 计算过滤掉的公共权限数量
        const publicCount =
          allApiPermissions.length - apiPermissions.value.length;

        // 使用选中的应用信息
        apiInfo.appCode = selectedApp.appCode;
        apiInfo.appName = selectedApp.appName;

        ElMessage.success(
          `成功获取 ${apiPermissions.value.length} 个API权限（已过滤 ${publicCount} 个公共权限）`
        );
      } else {
        ElMessage.warning("未获取到API权限");
        apiPermissions.value = [];
        apiInfo.appCode = "";
        apiInfo.appName = "";
      }
    } catch (error) {
      console.error("获取API权限失败", error);
      ElMessage.error(
        "获取API权限失败: " +
          (error instanceof Error ? error.message : String(error))
      );
      apiPermissions.value = [];
      apiInfo.appCode = "";
      apiInfo.appName = "";
    } finally {
      fetchingApi.value = false;
    }
  });
};

// 提交初始化API权限
const submitInitApi = async () => {
  if (!initApiFormRef.value) return;

  await initApiFormRef.value.validate(async (valid) => {
    if (!valid) return;

    if (apiPermissions.value.length === 0) {
      ElMessage.warning("请先获取API权限列表");
      return;
    }

    if (!apiInfo.appCode || !apiInfo.appName) {
      ElMessage.warning("未检测到应用信息，请重新获取API权限列表");
      return;
    }

    submittingApi.value = true;
    try {
      // 将API权限转换为权限树DTO

      // 创建根权限
      const rootPermission: PermissionTreeDto = {
        appCode: apiInfo.appCode,
        code: `${apiInfo.appCode}:api:root`,
        name: `${apiInfo.appName}API权限`,
        type: PermissionType.Directory,
        sortOrder: 0,
        status: CommonStatus.Enabled,
        description: `${apiInfo.appName}的API权限`,
        children: [],
      };

      // 按控制器分组API权限
      const controllerMap = new Map<string, ApiEndpointInfo[]>();

      apiPermissions.value.forEach((endpoint) => {
        if (!endpoint.controllerName) return;

        if (!controllerMap.has(endpoint.controllerName)) {
          controllerMap.set(endpoint.controllerName, []);
        }

        controllerMap.get(endpoint.controllerName)?.push(endpoint);
      });

      // 遍历控制器，构建权限树
      controllerMap.forEach((endpoints, controllerName) => {
        if (endpoints.length === 0) return;

        // 获取第一个端点，用于提取控制器信息
        const firstEndpoint = endpoints[0];

        // 创建控制器权限（目录）
        const controllerPermission: PermissionTreeDto = {
          appCode: apiInfo.appCode,
          code: `${apiInfo.appCode}:api:${controllerName.toLowerCase()}`,
          name: `${firstEndpoint.controllerDescription || controllerName}`,
          type: PermissionType.Directory,
          path: "",
          sortOrder: 0,
          status: CommonStatus.Enabled,
          description: `${apiInfo.appName} - ${
            firstEndpoint.controllerDescription || controllerName
          }`,
          children: [],
        };

        // 创建控制器下的操作权限（API）
        endpoints.forEach((endpoint) => {
          if (
            !endpoint.actionName ||
            !endpoint.httpMethods ||
            endpoint.httpMethods.length === 0
          )
            return;

          endpoint.httpMethods.forEach((httpMethodStr) => {
            // 获取HTTP方法
            let method = HttpMethod.GET;
            const httpMethod = httpMethodStr.toUpperCase();
            switch (httpMethod) {
              case "GET":
                method = HttpMethod.GET;
                break;
              case "POST":
                method = HttpMethod.POST;
                break;
              case "PUT":
                method = HttpMethod.PUT;
                break;
              case "DELETE":
                method = HttpMethod.DELETE;
                break;
              case "PATCH":
                method = HttpMethod.PATCH;
                break;
              case "HEAD":
                method = HttpMethod.HEAD;
                break;
              case "OPTIONS":
                method = HttpMethod.OPTIONS;
                break;
              default:
                method = HttpMethod.GET;
            }

            // 确定权限编码
            let permissionCode: string;
            if (endpoint.namedCode) {
              // 如果有命名编码，直接使用
              permissionCode = endpoint.namedCode;
            } else {
              // 否则使用拼接方式创建编码
              permissionCode = `${
                apiInfo.appCode
              }:api:${controllerName.toLowerCase()}:${
                endpoint.actionName?.toLowerCase() || "unknown"
              }:${httpMethod.toLowerCase()}`;
            }

            // 创建API权限
            const apiPermission: PermissionTreeDto = {
              appCode: apiInfo.appCode,
              code: permissionCode,
              name: `${
                endpoint.actionDescription || endpoint.actionName || "Unknown"
              } [${httpMethod}]`,
              type: PermissionType.Api,
              path: endpoint.routeTemplate || "",
              method,
              sortOrder: 0,
              status: CommonStatus.Enabled,
              description: `${apiInfo.appName} - ${
                firstEndpoint.controllerDescription || controllerName
              } - ${
                endpoint.actionDescription || endpoint.actionName || "Unknown"
              } [${httpMethod}]`,
            };

            // 将API权限添加到控制器权限的子权限列表中
            controllerPermission.children?.push(apiPermission);
          });
        });

        // 将控制器权限添加到根权限的子权限列表中
        rootPermission.children?.push(controllerPermission);
      });

      // 批量更新API权限
      if (apiPermissions.value.length > 0) {
        // 使用新的API接口，传递权限树数据
        await batchUpdateApiPermissions(apiInfo.appCode, [rootPermission]);
        ElMessage.success(`成功初始化API权限`);
        initApiDialogVisible.value = false;

        // 重新加载权限列表
        loadData();
      } else {
        ElMessage.warning("没有可初始化的权限");
      }
    } catch (error) {
      console.error("初始化API权限失败", error);
      ElMessage.error(
        "初始化API权限失败: " +
          (error instanceof Error ? error.message : String(error))
      );
    } finally {
      submittingApi.value = false;
    }
  });
};

// 初始化
onMounted(() => {
  // 只需要调用一次loadData，它会同时加载树形数据
  loadData();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.permission-dialog {
  .el-tree {
    width: 100%;
  }
}

.detected-info {
  .el-tag {
    font-size: 14px;
    padding: 6px 12px;
  }
}
</style>
