<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      :title="$t('system.role.title')"
      :data="roleList"
      :columns="tableColumns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #toolbar>
        <el-button
          type="primary"
          @click="handleAdd"
          v-hasPermission="['system:role:add']"
        >
          <el-icon><Plus /></el-icon>
          {{ $t("system.role.add") }}
        </el-button>
      </template>
      <!-- 角色类型列 -->
      <template #type="{ row }">
        <el-tag v-if="row.type === RoleType.General" type="primary"
        >通用角色</el-tag
        >
        <el-tag v-else-if="row.type === RoleType.Position" type="success"
        >岗位角色</el-tag
        >
        <el-tag v-else-if="row.type === RoleType.System" type="warning"
        >系统角色</el-tag
        >
      </template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag
          :type="row.status === CommonStatus.Enabled ? 'success' : 'danger'"
        >
          {{
            row.status === CommonStatus.Enabled
              ? $t("system.role.enable")
              : $t("system.role.disable")
          }}
        </el-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button
          type="primary"
          link
          @click="handleEdit(row)"
          v-hasPermission="['system:role:edit']"
        >
          <el-icon><Edit /></el-icon>
          {{ $t("system.role.edit") }}
        </el-button>
        <el-button
          type="primary"
          link
          @click="handlePermission(row)"
          v-hasPermission="['system:role:permission']"
        >
          <el-icon><Setting /></el-icon>

          {{ $t("system.role.permission") }}
        </el-button>
        <el-button
          type="danger"
          link
          @click="handleDelete(row)"
          v-hasPermission="['system:role:delete']"
        >
          <el-icon><Delete /></el-icon>
          {{ $t("system.role.delete") }}
        </el-button>
      </template>
    </DataTable>

    <!-- 角色表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="roleFormRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item :label="$t('system.role.name')" prop="name">
          <el-input
            v-model="form.name"
            :placeholder="$t('system.role.form.name')"
          />
        </el-form-item>
        <el-form-item :label="$t('system.role.code')" prop="code">
          <el-input
            v-model="form.code"
            :placeholder="$t('system.role.form.code')"
            :readonly="dialogType === 'edit'"
            :disabled="dialogType === 'edit'"
          />
        </el-form-item>
        <el-form-item label="角色类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择角色类型">
            <el-option :value="RoleType.General" label="通用角色" />
            <el-option :value="RoleType.Position" label="岗位角色" />
            <el-option :value="RoleType.System" label="系统角色" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option :value="CommonStatus.Enabled" label="启用" />
            <el-option :value="CommonStatus.Disabled" label="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">{{
            $t("cancel")
          }}</el-button>
          <el-button type="primary" @click="submitForm">{{
            $t("confirm")
          }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限分配对话框 -->
    <el-dialog
      title="权限分配"
      v-model="permissionDialogVisible"
      width="600px"
      append-to-body
      destroy-on-close
      class="permission-dialog"
      @closed="handlePermissionDialogClosed"
    >
      <el-form label-width="100px">
        <el-form-item label="角色">
          <span>{{ currentRole?.name }} ({{ currentRole?.code }})</span>
        </el-form-item>
        <el-form-item label="权限">
          <el-tree
            ref="permissionTreeRef"
            :data="permissionTreeData"
            :props="{ label: 'name', children: 'children' }"
            show-checkbox
            node-key="key"
            default-expand-all
            :check-strictly="true"
            @check="handleNodeCheck"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">{{
            $t("cancel")
          }}</el-button>
          <el-button type="primary" @click="submitPermission">{{
            $t("confirm")
          }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import { Plus, Edit, Delete, Setting } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import SearchForm, { FormItem } from "@/components/common/SearchForm.vue";
import DataTable, { TableColumn } from "@/components/common/DataTable.vue";
import {
  getRolePage,
  createRole,
  updateRole,
  deleteRole,
  getPermissionList,
  getRolePermissionList,
  updateRolePermissions,
} from "@/api/rbac-mgt";
import {
  RoleDto,
  RoleOperationDto,
  RoleQueryParams,
  PermissionDto,
} from "@/dtos";
import type { ElTree } from "element-plus";
import { RoleType, CommonStatus } from "@/enums";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
// 搜索表单项配置
const searchFormItems = computed(() => [
  {
    type: "input",
    label: t("system.role.name"),
    prop: "name",
    placeholder: "请输入角色名称",
  },
  {
    type: "input",
    label: t("system.role.code"),
    prop: "code",
    placeholder: "请输入角色编码",
  },
  {
    type: "select",
    label: "角色类型",
    prop: "type",
    placeholder: "请选择角色类型",
    options: [
      { label: "通用角色", value: RoleType.General },
      { label: "岗位角色", value: RoleType.Position },
      { label: "系统角色", value: RoleType.System },
    ],
  },
  {
    type: "select",
    label: "状态",
    prop: "status",
    placeholder: "请选择状态",
    options: [
      { label: "启用", value: CommonStatus.Enabled },
      { label: "禁用", value: CommonStatus.Disabled },
    ],
  },
]);

// 表格列配置
const tableColumns = computed(() => [
  { prop: "name", label: t("system.role.name"), width: 150 },
  { prop: "code", label: t("system.role.code"), width: 150 },
  { prop: "type", label: "角色类型", slot: "type", width: 120 },
  { prop: "status", label: "状态", slot: "status", width: 100 },
  { prop: "description", label: "描述" },
]);

const loading = ref(false);
const total = ref(0);
const roleList = ref<RoleDto[]>([]);
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");
const roleFormRef = ref<FormInstance>();
const permissionDialogVisible = ref(false);
const permissionTreeRef = ref<InstanceType<typeof ElTree>>();
const permissionTreeData = ref<any[]>([]);
const currentRoleId = ref<string>();
const currentRole = ref<RoleDto>();

const queryParams = reactive<RoleQueryParams>({
  $pageIndex: 1,
  $pageSize: 10,
  name: "",
  code: "",
  type: undefined,
  status: undefined,
});

const form = reactive<RoleOperationDto>({
  name: "",
  code: "",
  type: RoleType.General,
  status: CommonStatus.Enabled,
  description: "",
});

// 当前编辑的角色ID
const currentEditId = ref<string>("");

const rules: FormRules = {
  name: [
    { required: true, message: "请输入角色名称", trigger: "blur" },
    {
      min: 2,
      max: 50,
      message: "角色名称长度在 2 到 50 个字符",
      trigger: "blur",
    },
  ],
  code: [
    { required: true, message: "请输入角色编码", trigger: "blur" },
    {
      pattern: /^[A-Za-z0-9_-]+$/,
      message: "角色编码只能包含字母、数字、下划线和连字符",
      trigger: "blur",
    },
  ],
  type: [{ required: true, message: "请选择角色类型", trigger: "change" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
};

const dialogTitle = computed(() => {
  return dialogType.value === "add" ? "新增角色" : "编辑角色";
});

onMounted(() => {
  getList();
});

const getList = async () => {
  try {
    loading.value = true;
    const response = await getRolePage(queryParams);
    console.log("角色列表数据:", response);

    const { data } = response;
    roleList.value = data.rows || [];

    total.value = data.totals;
  } catch (error) {
    console.error("获取角色列表失败", error);
    ElMessage.error("获取角色列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

const resetForm = () => {
  form.name = "";
  form.code = "";
  form.type = RoleType.General;
  form.status = CommonStatus.Enabled;
  form.description = "";
  currentEditId.value = "";
};

const handleAdd = () => {
  resetForm();
  dialogType.value = "add";
  dialogVisible.value = true;
};

const handleEdit = (row: RoleDto) => {
  resetForm();
  dialogType.value = "edit";
  dialogVisible.value = true;

  // 将行数据复制到表单
  form.name = row.name || "";
  form.code = row.code || "";
  form.type = row.type;
  form.status = row.status;
  form.description = row.description || "";

  // 保存当前编辑的角色ID和角色对象
  currentEditId.value = row.key;
  currentRole.value = row;
};

const handleDelete = async (row: RoleDto) => {
  try {
    await ElMessageBox.confirm("确认要删除该角色吗？", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    await deleteRole(row.key);
    ElMessage.success("删除成功");
    getList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除角色失败:", error);
      ElMessage.error("删除角色失败");
    }
  }
};

const submitForm = async () => {
  if (!roleFormRef.value) return;

  try {
    await roleFormRef.value.validate();
    if (dialogType.value === "add") {
      await createRole(form);
      ElMessage.success("新增成功");
    } else {
      await updateRole(currentEditId.value, form);
      ElMessage.success("修改成功");
    }
    dialogVisible.value = false;
    getList();
  } catch (error) {
    console.error("提交表单失败:", error);
    ElMessage.error("提交表单失败");
  }
};

// 获取角色权限的方法
const fetchRolePermissions = async (roleId: string) => {
  try {
    // 注意：Swagger 文档显示参数名为 RoleId（首字母大写）
    const response = await getRolePermissionList({ RoleId: roleId });
    // 从响应中提取权限ID列表
    return response.data.map((item) => item.permissionId);
  } catch (error) {
    console.error("获取角色权限失败:", error);
    return [];
  }
};

// 更新角色权限的方法
const submitRolePermissions = async (
  roleId: string,
  permissionIds: string[]
) => {
  try {
    console.log(
      "准备提交角色权限, roleId:",
      roleId,
      "permissionIds:",
      permissionIds
    );

    // 构建角色权限操作DTO数组
    const rolePermissions = permissionIds.map((permissionId) => ({
      roleId: roleId,
      permissionId: permissionId, // 转换为数字类型
    }));

    console.log("构建的角色权限DTO数组:", JSON.stringify(rolePermissions));

    // 调用API更新角色权限
    const result = await updateRolePermissions(roleId, rolePermissions);
    console.log("角色权限更新API返回结果:", result);

    return result;
  } catch (error) {
    console.error("更新角色权限失败:", error);
    throw error;
  }
};

/**
 * 将平面权限列表转换为树结构
 * @param permissions 权限列表
 */
const buildPermissionTree = (permissions: PermissionDto[]) => {
  // 创建一个映射表，用于快速查找节点
  const permissionMap = new Map<
    string,
    PermissionDto & { children: PermissionDto[] }
  >();

  // 首先为每个节点添加children属性
  permissions.forEach((permission) => {
    permissionMap.set(permission.key, { ...permission, children: [] });
  });

  // 构建树结构
  const tree: PermissionDto[] = [];
  permissions.forEach((permission) => {
    const node = permissionMap.get(permission.key);
    if (!node) return;

    if (permission.parentId && permissionMap.has(permission.parentId)) {
      // 如果有父节点，将当前节点添加到父节点的children中
      const parent = permissionMap.get(permission.parentId);
      if (parent) {
        parent.children.push(node);
      }
    } else {
      // 如果没有父节点或父节点不存在，则作为根节点
      tree.push(node);
    }
  });

  return tree;
};

const handlePermission = async (row: RoleDto) => {
  try {
    currentRole.value = row;
    currentRoleId.value = row.key;

    // 获取权限列表
    const permissionResponse = await getPermissionList();
    console.log("获取到的权限列表:", permissionResponse);

    // 构建权限树
    const permissionTree = buildPermissionTree(permissionResponse.data || []);
    console.log("构建的权限树:", permissionTree);
    permissionTreeData.value = permissionTree;

    // 打开对话框
    permissionDialogVisible.value = true;

    // 获取角色权限
    const rolePermIds = await fetchRolePermissions(row.key);
    console.log("角色权限ID列表:", rolePermIds);

    // 在树加载完成后设置选中的节点
    // 使用 nextTick 确保树已经渲染完成
    nextTick(() => {
      console.log("设置选中节点:", rolePermIds);
      if (permissionTreeRef.value) {
        // 清空当前选中状态
        permissionTreeRef.value.setCheckedKeys([]);

        // 获取所有可用的权限ID（使用字符串存储，避免大整数精度问题）
        const allPermissionIds = new Set();
        const collectIds = (nodes: any[]) => {
          if (!nodes) return;
          for (const node of nodes) {
            if (node.key) {
              // 将key转为字符串存储
              allPermissionIds.add(String(node.key));
            }
            if (node.children && node.children.length > 0) {
              collectIds(node.children);
            }
          }
        };

        collectIds(permissionTreeData.value);
        console.log("所有可用的权限ID:", Array.from(allPermissionIds));

        // 过滤掉不存在于权限树中的ID（使用字符串比较）
        const validIds = rolePermIds.filter((id) => {
          // 将id转为字符串进行比较
          const strId = String(id);
          return allPermissionIds.has(strId);
        });

        console.log("有效的权限ID:", validIds);
        console.log(
          "被过滤掉的权限ID:",
          rolePermIds.filter((id) => !validIds.includes(id))
        );

        // 先设置叶子节点的选中状态，然后再处理父节点
        // 这样可以避免重复设置父节点的选中状态
        const leafNodes: any[] = [];
        const parentNodes: any[] = [];

        // 分类节点
        validIds.forEach((id) => {
          if (permissionTreeRef.value) {
            const node = permissionTreeRef.value.getNode(id);
            if (node) {
              if (node.isLeaf) {
                leafNodes.push(node);
              } else {
                parentNodes.push(node);
              }
            }
          }
        });

        console.log(
          "叶子节点:",
          leafNodes.map((n) => n.data.key)
        );
        console.log(
          "父节点:",
          parentNodes.map((n) => n.data.key)
        );

        // 先设置叶子节点的选中状态
        leafNodes.forEach((node) => {
          try {
            if (permissionTreeRef.value) {
              permissionTreeRef.value.setChecked(node.data.key, true, false);

              // 手动选中所有父节点
              selectParentNodes(node);
            }
          } catch (e) {
            console.warn(`设置叶子节点 ${node.data.key} 选中状态失败:`, e);
          }
        });

        // 再设置父节点的选中状态（如果还没被选中）
        parentNodes.forEach((node) => {
          try {
            if (permissionTreeRef.value) {
              permissionTreeRef.value.setChecked(node.data.key, true, false);
            }
          } catch (e) {
            console.warn(`设置父节点 ${node.data.key} 选中状态失败:`, e);
          }
        });

        // 打印当前选中的节点，用于调试
        setTimeout(() => {
          const checkedKeys = permissionTreeRef.value?.getCheckedKeys() || [];
          const halfCheckedKeys =
            permissionTreeRef.value?.getHalfCheckedKeys() || [];
          console.log("当前选中的节点:", checkedKeys);
          console.log("当前半选中的节点:", halfCheckedKeys);

          // 再次确保所有选中节点的父节点也被选中
          ensureParentNodesSelected();
        }, 100);
      }
    });
  } catch (error) {
    console.error("获取权限失败:", error);
    ElMessage.error(
      "获取权限失败: " +
        (error instanceof Error ? error.message : String(error))
    );
  }
};

// 确保所有选中节点的父节点也被选中
const ensureParentNodesSelected = () => {
  if (!permissionTreeRef.value) return;

  // 获取当前所有选中的节点
  const checkedKeys = permissionTreeRef.value.getCheckedKeys();
  console.log("确保父节点选中 - 当前选中节点:", checkedKeys);

  // 处理每个选中的节点
  checkedKeys.forEach((key) => {
    const node = permissionTreeRef.value?.getNode(key);
    if (node) {
      // 选中其所有父节点
      selectParentNodes(node);
    }
  });

  // 再次检查选中状态
  setTimeout(() => {
    const newCheckedKeys = permissionTreeRef.value?.getCheckedKeys() || [];
    console.log("确保父节点选中后 - 选中节点:", newCheckedKeys);
  }, 100);
};

const submitPermission = async () => {
  try {
    if (!currentRoleId.value) {
      ElMessage.error("角色ID不能为空");
      return;
    }

    // 获取选中的权限ID（完全选中的节点）
    // 在我们的自定义逻辑下，这包括用户手动选中的节点和通过关联自动选中的父节点
    const checkedKeys = permissionTreeRef.value?.getCheckedKeys() || [];

    // 在我们的自定义逻辑中，不会有半选中的节点，因为我们总是完全选中父节点
    // 但我们仍然保留这个调用，以便将来可能的需求变更
    const halfCheckedKeys = permissionTreeRef.value?.getHalfCheckedKeys() || [];

    console.log("选中的节点:", checkedKeys);
    console.log("半选中的节点:", halfCheckedKeys);

    // 在我们的自定义逻辑下，所有选中的节点都已经包含在checkedKeys中
    const allSelectedKeys = [...checkedKeys];

    // 将所有选中的节点ID转换为字符串
    const permissionIds = allSelectedKeys.map((id) => String(id));

    // 获取所有可用的权限ID（使用字符串存储，避免大整数精度问题）
    const allPermissionIds = new Set();
    const collectIds = (nodes: any[]) => {
      if (!nodes) return;
      for (const node of nodes) {
        if (node.key) {
          // 将key转为字符串存储
          allPermissionIds.add(String(node.key));
        }
        if (node.children && node.children.length > 0) {
          collectIds(node.children);
        }
      }
    };

    collectIds(permissionTreeData.value);
    console.log("所有可用的权限ID:", Array.from(allPermissionIds));

    // 过滤掉不存在于权限树中的ID（使用字符串比较）
    const validIds = permissionIds.filter((id) => allPermissionIds.has(id));
    console.log("有效的权限ID:", validIds);
    console.log(
      "被过滤掉的权限ID:",
      permissionIds.filter((id) => !validIds.includes(id))
    );

    // 去重，确保没有重复的permissionId
    const uniquePermissionIds = [...new Set(validIds)];

    console.log("提交的权限ID (数组):", uniquePermissionIds);
    console.log("提交的权限ID (JSON):", JSON.stringify(uniquePermissionIds));

    // 检查是否有权限被选中
    if (uniquePermissionIds.length === 0) {
      ElMessage.warning("请至少选择一个权限");
      return;
    }

    // 提交权限分配
    const result = await submitRolePermissions(
      currentRoleId.value,
      uniquePermissionIds
    );
    console.log("权限更新结果:", result);

    ElMessage.success("权限更新成功");
    permissionDialogVisible.value = false;
  } catch (error) {
    console.error("更新权限失败:", error);
    ElMessage.error(
      "更新权限失败: " +
        (error instanceof Error ? error.message : String(error))
    );
  }
};

// 处理节点选中事件
const handleNodeCheck = (data: any, checkedInfo: any) => {
  console.log("节点选中事件触发:", data, checkedInfo);

  // 检查节点是否被选中
  // 根据截图，checkedInfo对象中没有checked属性
  // 我们需要检查checkedKeys数组中是否包含当前节点的key
  if (checkedInfo.checkedKeys && checkedInfo.checkedKeys.includes(data.key)) {
    // 获取节点对象
    const node = permissionTreeRef.value?.getNode(data.key);
    if (node) {
      console.log("找到节点:", node);
      // 获取节点的所有父节点并选中它们
      selectParentNodes(node);
    } else {
      console.warn("未找到节点:", data.key);
    }
  }

  // 打印checkedInfo对象的结构，用于调试
  console.log("checkedInfo对象结构:", Object.keys(checkedInfo));
  if (checkedInfo.checkedKeys) {
    console.log("checkedKeys:", checkedInfo.checkedKeys);
  }
  if (checkedInfo.checkedNodes) {
    console.log("checkedNodes数量:", checkedInfo.checkedNodes.length);
  }
  if (checkedInfo.halfCheckedKeys) {
    console.log("halfCheckedKeys:", checkedInfo.halfCheckedKeys);
  }
  if (checkedInfo.halfCheckedNodes) {
    console.log("halfCheckedNodes数量:", checkedInfo.halfCheckedNodes.length);
  }
};

// 递归选中所有父节点
const selectParentNodes = (node: any) => {
  if (!node || !node.parent) {
    return;
  }

  // 确保父节点有key
  if (
    node.parent.key !== undefined &&
    node.parent.data &&
    node.parent.data.key !== undefined
  ) {
    console.log("选中父节点:", node.parent.data.key);

    // 选中父节点
    if (permissionTreeRef.value) {
      try {
        permissionTreeRef.value.setChecked(node.parent.data.key, true, false);
      } catch (e) {
        console.error("选中父节点失败:", e);
      }
    }

    // 递归处理更上层的父节点
    selectParentNodes(node.parent);
  } else {
    console.warn("父节点没有key:", node.parent);
  }
};

// 处理权限对话框关闭事件
const handlePermissionDialogClosed = () => {
  // 清空权限树数据和选中状态
  permissionTreeData.value = [];
  if (permissionTreeRef.value) {
    permissionTreeRef.value.setCheckedKeys([]);
  }
  // 清空当前角色信息
  currentRole.value = undefined;
  currentRoleId.value = undefined;
};
</script>

<style lang="scss" scoped>


.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.el-tree {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.el-form-item__content {
  line-height: 1.5;
}

.permission-dialog {
  .el-dialog__body {
    padding-top: 10px;
  }

  .el-tree {
    width: 100%;
  }
}
</style>
