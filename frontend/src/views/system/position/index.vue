<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      :title="$t('system.position.title')"
      :data="positionList"
      :columns="columns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
      @sort-change="handleSortChange"
    >
      <template #toolbar>
        <el-button
          type="primary"
          @click="handleAdd"
          v-hasPermission="['system:position:add']"
        >
          <el-icon><Plus /></el-icon>
          {{ $t("system.position.add") }}
        </el-button>
      </template>
      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag
          :type="row.status === PositionStatus.Enabled ? 'success' : 'danger'"
        >
          {{
            row.status === PositionStatus.Enabled
              ? $t("system.position.enable")
              : $t("system.position.disable")
          }}
        </el-tag>
      </template>

      <!-- 操作列插槽 -->
      <template #action="{ row }">
        <el-button
          type="primary"
          link
          @click="handleEdit(row)"
          v-hasPermission="['system:position:edit']"
        >
          <el-icon><Edit /></el-icon>
          {{ $t("system.position.edit") }}
        </el-button>

        <el-button
          type="danger"
          link
          @click="handleDelete(row)"
          v-hasPermission="['system:position:delete']"
        >
          <el-icon><Delete /></el-icon>
          {{ $t("system.position.delete") }}
        </el-button>
      </template>
    </DataTable>

    <!-- 岗位表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      append-to-body
      destroy-on-close
      @closed="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        style="max-width: 400px"
      >
        <el-form-item :label="$t('system.position.code')" prop="code">
          <el-input
            v-model="form.code"
            :placeholder="$t('system.position.form.code')"
            :disabled="isEdit"
          />
        </el-form-item>

        <el-form-item :label="$t('system.position.name')" prop="name">
          <el-input
            v-model="form.name"
            :placeholder="$t('system.position.form.name')"
          />
        </el-form-item>

        <el-form-item :label="$t('system.position.role')" prop="roleId">
          <el-select
            v-model="form.roleId"
            :placeholder="$t('system.position.form.role')"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="role in positionRoles"
              :key="role.key"
              :label="role.name"
              :value="role.key"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('system.position.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="PositionStatus.Enabled">{{
              $t("system.position.enable")
            }}</el-radio>
            <el-radio :label="PositionStatus.Disabled">{{
              $t("system.position.disable")
            }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          :label="$t('system.position.description')"
          prop="description"
        >
          <el-input
            v-model="form.description"
            type="textarea"
            :placeholder="$t('system.position.form.description')"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">{{
            $t("cancel")
          }}</el-button>
          <el-button
            type="primary"
            @click="submitForm"
            :loading="submitLoading"
          >{{ $t("confirm") }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { Plus, Edit, Delete } from "@element-plus/icons-vue";
import SearchForm from "@/components/common/SearchForm.vue";
import DataTable from "@/components/common/DataTable.vue";
import type { FormItem } from "@/components/common/SearchForm.vue";
import type { TableColumn } from "@/components/common/DataTable.vue";
import {
  getPositionPage,
  createPosition,
  updatePosition,
  deletePosition,
  getRoleList,
} from "@/api/rbac-mgt";
import { PositionDto, PositionOperationDto, RoleDto } from "@/dtos";
import { PositionStatus, RoleType } from "@/enums";

const { t } = useI18n();

// 查询参数
const queryParams = reactive({
  code: "",
  name: "",
  status: undefined as PositionStatus | undefined,
  $pageIndex: 1,
  $pageSize: 10,
  $sortBy: "Code",
  $orderBy: "asc" as "asc" | "desc",
});

// 搜索表单项配置
const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "岗位编码",
    prop: "code",
    placeholder: "请输入岗位编码",
  },
  {
    type: "input",
    label: "岗位名称",
    prop: "name",
    placeholder: "请输入岗位名称",
  },
  {
    type: "select",
    label: "状态",
    prop: "status",
    placeholder: "请选择状态",
    options: [
      { label: "启用", value: PositionStatus.Enabled },
      { label: "禁用", value: PositionStatus.Disabled },
    ],
  },
]);

// 表格列配置
const columns = ref<TableColumn[]>([
  { label: "岗位编码", prop: "code" },
  { label: "岗位名称", prop: "name" },
  { label: "状态", prop: "status", slot: "status" },
  { label: "描述", prop: "description" },
  // { label: "操作", prop: "operation", slot: "operation", width: "150px" },
]);

// 岗位列表数据
const positionList = ref<PositionDto[]>([]);
// 加载状态
const loading = ref(false);
// 分页信息
const total = ref(0);

// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = computed(() => (isEdit.value ? "编辑岗位" : "新增岗位"));
const isEdit = ref(false);
const submitLoading = ref(false);

// 表单相关
const formRef = ref<FormInstance>();
const form = reactive<PositionOperationDto>({
  code: "",
  name: "",
  roleId: undefined as unknown as string, // 初始值设为 undefined，这样下拉框会显示占位符
  status: PositionStatus.Enabled,
  description: "",
});

// 表单校验规则
const rules = {
  code: [
    { required: true, message: "请输入岗位编码", trigger: "blur" },
    { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" },
  ],
  name: [
    { required: true, message: "请输入岗位名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" },
  ],
  roleId: [{ required: true, message: "请选择关联角色", trigger: "change" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
};

// 岗位角色列表
const positionRoles = ref<RoleDto[]>([]);

// 获取岗位角色列表
const getPositionRoles = async () => {
  try {
    // 只获取岗位角色（type=2）
    const { data } = await getRoleList({ type: RoleType.Position });
    positionRoles.value = data;
  } catch (error) {
    console.error("获取岗位角色列表失败", error);
    ElMessage.error("获取岗位角色列表失败");
  }
};

// 获取岗位列表数据
const getPositionData = async () => {
  loading.value = true;
  try {
    console.log("发送请求参数:", JSON.stringify(queryParams));
    const response = await getPositionPage(queryParams);
    console.log("岗位列表数据:", response);

    const { data } = response;
    positionList.value = data.rows || [];

    total.value = data.totals;
  } catch (error) {
    console.error("获取岗位列表失败", error);
    ElMessage.error("获取岗位列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = (formData: any) => {
  // 将表单数据合并到查询参数中
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  console.log("搜索参数:", queryParams);
  getPositionData();
};

// 重置
const handleReset = (formData: any) => {
  // 将表单数据合并到查询参数中
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  console.log("重置后参数:", queryParams);
  getPositionData();
};

// 分页变化
const handlePageChange = (page: number) => {
  queryParams.$pageIndex = page;
  getPositionData();
};

// 每页条数变化
const handleSizeChange = (size: number) => {
  console.log("每页条数变化:", size);
  queryParams.$pageSize = size;
  queryParams.$pageIndex = 1; // 切换每页条数时，重置为第一页
  getPositionData();
};

// 排序变化
const handleSortChange = (sort: { prop: string; order: string }) => {
  queryParams.$sortBy = sort.prop;
  queryParams.$orderBy = sort.order === "ascending" ? "asc" : "desc";
  getPositionData();
};

// 新增岗位
const handleAdd = () => {
  isEdit.value = false;
  dialogVisible.value = true;
  // 重置表单
  resetForm();
};

// 编辑岗位
const handleEdit = (row: PositionDto) => {
  isEdit.value = true;
  dialogVisible.value = true;
  // 填充表单数据
  Object.assign(form, {
    code: row.code,
    name: row.name,
    roleId: row.roleId,
    status: row.status,
    description: row.description,
  });
};

// 删除岗位
const handleDelete = (row: PositionDto) => {
  ElMessageBox.confirm(
    t("system.position.confirmDelete"),
    t("system.position.title"),
    {
      confirmButtonText: t("confirm"),
      cancelButtonText: t("cancel"),
      type: "warning",
    }
  )
    .then(async () => {
      try {
        await deletePosition(row.key);
        ElMessage.success(t("system.position.delete") + t("success"));
        // 刷新列表
        getPositionData();
      } catch (error) {
        console.error("删除岗位失败", error);
        ElMessage.error(t("system.position.delete") + t("failed"));
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 设置默认值
  Object.assign(form, {
    code: "",
    name: "",
    roleId: undefined as unknown as number, // 重置为 undefined，这样下拉框会显示占位符
    status: PositionStatus.Enabled,
    description: "",
  });
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (!valid) return;

    // 额外检查 roleId 是否有值
    if (form.roleId === undefined || form.roleId === null) {
      ElMessage.error(t("system.position.form.role"));
      return;
    }

    submitLoading.value = true;
    try {
      if (isEdit.value) {
        // 编辑模式
        const currentPosition = positionList.value.find(
          (item) => item.code === form.code
        );
        if (!currentPosition) {
          ElMessage.error("未找到要编辑的岗位");
          return;
        }
        await updatePosition(currentPosition.key, form);
        ElMessage.success(t("system.position.edit") + t("success"));
      } else {
        // 新增模式
        await createPosition(form);
        ElMessage.success(t("system.position.add") + t("success"));
      }
      // 关闭对话框
      dialogVisible.value = false;
      // 刷新列表
      getPositionData();
    } catch (error) {
      console.error("保存岗位失败", error);
      ElMessage.error(t("system.position.title") + t("failed"));
    } finally {
      submitLoading.value = false;
    }
  });
};

// 页面加载时获取数据
onMounted(() => {
  getPositionData();
  getPositionRoles();
});
</script>

<style lang="scss" scoped>


.dialog-footer {
  text-align: right;
  padding-top: 20px;
}
</style>
