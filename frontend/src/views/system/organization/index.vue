<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      @search="handleSearch"
      @reset="handleReset"
    />

    <div class="org-container">
      <!-- 左侧组织树 -->
      <div class="org-tree-container">
        <div class="org-tree-header">
          <div class="org-tree-title">组织机构树</div>
          <el-button type="primary" size="small" @click="refreshTree">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>

        <el-tree
          ref="orgTreeRef"
          :data="organizationTree"
          node-key="key"
          :props="{
            label: 'name',
            children: 'children',
          }"
          highlight-current
          default-expand-all
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <span class="node-label">{{ node.label }}</span>
              <span class="node-actions">
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click.stop="handleAdd(data)"
                  v-hasPermission="['system:organization:add']"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click.stop="handleEdit(data)"
                  v-hasPermission="['system:organization:edit']"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click.stop="handleDelete(data)"
                  v-hasPermission="['system:organization:delete']"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </span>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 右侧内容区 -->
      <div class="org-content">
        <DataTable
          v-if="tableData.length > 0"
          title="组织列表"
          :data="tableData"
          :columns="tableColumns"
          :loading="loading"
          :total="total"
          :current-page-prop="queryParams.$pageIndex"
          :page-size-prop="queryParams.$pageSize"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
        >
          <template #toolbar>
            <el-button
              type="primary"
              @click="handleAddRoot"
              v-hasPermission="['system:organization:add']"
            >
              <el-icon><Plus /></el-icon>
              新增根组织
            </el-button>
          </template>
          <!-- 组织类型列 -->
          <template #orgType="{ row }">
            <el-tag
              v-if="row.orgType === OrganizationType.Internal"
              type="primary"
              class="org-type-tag"
              >内部组织</el-tag
            >
            <el-tag
              v-else-if="row.orgType === OrganizationType.External"
              type="success"
              class="org-type-tag"
              >外部组织</el-tag
            >
          </template>

          <!-- 状态列 -->
          <template #status="{ row }">
            <el-tag
              v-if="row.status === OrganizationStatus.Enabled"
              type="success"
              class="org-status-tag"
              >启用</el-tag
            >
            <el-tag
              v-else-if="row.status === OrganizationStatus.Disabled"
              type="danger"
              class="org-status-tag"
              >禁用</el-tag
            >
          </template>

          <!-- 操作列 -->
          <template #action="{ row }">
            <el-button
              type="primary"
              link
              @click="handleEdit(row)"
              v-hasPermission="['system:organization:edit']"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button
              type="primary"
              link
              @click="handleAdd(row)"
              v-hasPermission="['system:organization:add']"
            >
              <el-icon><Plus /></el-icon>
              添加子组织
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
              v-hasPermission="['system:organization:delete']"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </DataTable>

        <el-empty v-else description="暂无数据" />
      </div>
    </div>

    <!-- 组织表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="orgFormRef"
        :model="orgForm"
        :rules="orgFormRules"
        label-width="100px"
      >
        <el-form-item
          label="上级组织"
          prop="parentId"
          v-if="dialogType !== 'addRoot'"
        >
          <el-cascader
            v-model="orgForm.parentId"
            :options="organizationOptions"
            :props="{
              checkStrictly: true,
              label: 'name',
              value: 'key',
              emitPath: false,
            }"
            placeholder="请选择上级组织"
            :readonly="dialogType === 'edit'"
            :disabled="dialogType === 'edit'"
            clearable
          />
        </el-form-item>
        <el-form-item label="组织名称" prop="name">
          <el-input v-model="orgForm.name" placeholder="请输入组织名称" />
        </el-form-item>
        <el-form-item label="组织编码" prop="code">
          <el-input
            v-model="orgForm.code"
            placeholder="请输入组织编码"
            :readonly="dialogType === 'edit'"
            :disabled="dialogType === 'edit'"
          />
        </el-form-item>
        <el-form-item label="组织类型" prop="orgType">
          <el-select v-model="orgForm.orgType" placeholder="请选择组织类型">
            <el-option :value="OrganizationType.Internal" label="内部组织" />
            <el-option :value="OrganizationType.External" label="外部组织" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="orgForm.status" placeholder="请选择状态">
            <el-option :value="OrganizationStatus.Enabled" label="启用" />
            <el-option :value="OrganizationStatus.Disabled" label="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="orgForm.sortOrder" :min="0" :max="9999" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="orgForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import { Plus, Edit, Delete, Refresh } from "@element-plus/icons-vue";
import SearchForm, { FormItem } from "@/components/common/SearchForm.vue";
import DataTable, { TableColumn } from "@/components/common/DataTable.vue";
import {
  getOrganizationList,
  getOrganizationPage,
  createOrganization,
  updateOrganization,
  deleteOrganization,
} from "@/api/rbac-mgt";
import {
  OrganizationDto,
  OrganizationOperationDto,
  OrganizationQueryParams,
} from "@/dtos";
import { CommonStatus, OrganizationStatus, OrganizationType } from "@/enums";

// 搜索表单项配置
const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "组织名称",
    prop: "name",
    placeholder: "请输入组织名称",
  },
  {
    type: "input",
    label: "组织编码",
    prop: "code",
    placeholder: "请输入组织编码",
  },
  {
    type: "select",
    label: "状态",
    prop: "status",
    placeholder: "请选择状态",
    options: [
      { label: "启用", value: OrganizationStatus.Enabled },
      { label: "禁用", value: OrganizationStatus.Disabled },
    ],
  },
]);

// 表格列配置
const tableColumns = ref<TableColumn[]>([
  { prop: "name", label: "组织名称", width: 180 },
  { prop: "code", label: "组织编码", width: 150 },
  { prop: "orgType", label: "组织类型", slot: "orgType", width: 120 },
  { prop: "level", label: "层级", width: 80 },
  { prop: "sortOrder", label: "排序", width: 80 },
  { prop: "status", label: "状态", slot: "status", width: 100 },
  { prop: "description", label: "描述" },
]);

// 组织树数据
const organizationTree = ref<OrganizationDto[]>([]);
const organizationOptions = ref<OrganizationDto[]>([]);
const orgTreeRef = ref();
const currentOrg = ref<OrganizationDto>();

// 表格数据
const tableData = ref<OrganizationDto[]>([]);
const loading = ref(false);
const total = ref(0);

// 查询参数
const queryParams = reactive<OrganizationQueryParams>({
  $pageIndex: 1,
  $pageSize: 10,
  name: "",
  code: "",
  status: undefined,
  parentId: undefined,
});

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit" | "addRoot">("add");
const dialogTitle = computed(() => {
  if (dialogType.value === "add") return "新增组织";
  if (dialogType.value === "edit") return "编辑组织";
  return "新增根组织";
});
const orgFormRef = ref<FormInstance>();
const orgForm = reactive<OrganizationOperationDto>({
  parentId: undefined,
  name: "",
  code: "",
  level: 1,
  path: "",
  sortOrder: 0,
  status: CommonStatus.Enabled,
  description: "",
  orgType: OrganizationType.Internal
});

// 表单验证规则
const orgFormRules: FormRules = {
  name: [
    { required: true, message: "请输入组织名称", trigger: "blur" },
    {
      min: 2,
      max: 50,
      message: "组织名称长度在 2 到 50 个字符",
      trigger: "blur",
    },
  ],
  code: [
    { required: true, message: "请输入组织编码", trigger: "blur" },
    {
      pattern: /^[A-Za-z0-9_-]+$/,
      message: "组织编码只能包含字母、数字、下划线和连字符",
      trigger: "blur",
    },
  ],
  orgType: [{ required: true, message: "请选择组织类型", trigger: "change" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  sortOrder: [{ required: true, message: "请输入排序", trigger: "blur" }],
};

// 获取组织机构树
const getOrganizationTree = async () => {
  try {
    const { data } = await getOrganizationList();

    // 构建树形结构
    const buildTree = (items: OrganizationDto[]): OrganizationDto[] => {
      const result: OrganizationDto[] = [];
      const itemMap = new Map<string, OrganizationDto>();

      // 先将所有项放入 Map 中
      items.forEach((item) => {
        itemMap.set(item.key, { ...item, children: [] });
      });

      // 构建树形结构
      items.forEach((item) => {
        const currentItem = itemMap.get(item.key);
        if (currentItem) {
          if (item.parentId && itemMap.has(item.parentId)) {
            const parent = itemMap.get(item.parentId);
            if (parent && parent.children) {
              parent.children.push(currentItem);
            }
          } else {
            result.push(currentItem);
          }
        }
      });

      return result;
    };

    organizationTree.value = buildTree(data);
    organizationOptions.value = [...data];
  } catch (error) {
    console.error("获取组织机构树失败", error);
    ElMessage.error("获取组织机构树失败");
  }
};

// 获取组织机构列表
const getList = async () => {
  loading.value = true;
  try {
    const { data } = await getOrganizationPage(queryParams);
    tableData.value = data.rows || [];
    total.value = data.totals;
  } catch (error) {
    console.error("获取组织机构列表失败", error);
    ElMessage.error("获取组织机构列表失败");
  } finally {
    loading.value = false;
  }
};

// 刷新组织树
const refreshTree = () => {
  getOrganizationTree();
};

// 处理节点点击
const handleNodeClick = (data: OrganizationDto) => {
  currentOrg.value = data;

  // 更新查询参数，只查询当前节点的子节点
  queryParams.parentId = data.key;
  getList();
};

// 处理搜索
const handleSearch = (formData: any) => {
  queryParams.$pageIndex = 1;
  Object.assign(queryParams, formData);
  getList();
};

// 处理重置
const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

// 处理分页变化
const handlePageChange = (page: number) => {
  queryParams.$pageIndex = page;
  getList();
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  queryParams.$pageSize = size;
  queryParams.$pageIndex = 1;
  getList();
};

// 新增根组织
const handleAddRoot = () => {
  dialogType.value = "addRoot";
  orgForm.parentId = undefined;
  orgForm.name = "";
  orgForm.code = "";
  orgForm.level = 1;
  orgForm.path = "";
  orgForm.sortOrder = 0;
  orgForm.status = CommonStatus.Enabled;
  orgForm.description = "";
  // 根组织默认为内部组织，但用户可以修改
  orgForm.orgType = OrganizationType.Internal;
  dialogVisible.value = true;
};

// 新增组织
const handleAdd = (data: OrganizationDto) => {
  dialogType.value = "add";
  orgForm.parentId = data.key;
  orgForm.name = "";
  orgForm.code = "";
  orgForm.level = data.level + 1;
  orgForm.path = "";
  orgForm.sortOrder = 0;
  orgForm.status = 1; // 启用
  orgForm.description = "";
  // 默认使用父组织的类型
  orgForm.orgType = data.orgType || 1; // 默认内部组织
  dialogVisible.value = true;
};

// 编辑组织
const handleEdit = (data: OrganizationDto) => {
  dialogType.value = "edit";
  orgForm.parentId = data.parentId;
  orgForm.name = data.name || "";
  orgForm.code = data.code || "";
  orgForm.level = data.level;
  orgForm.path = data.path || "";
  orgForm.sortOrder = data.sortOrder;
  orgForm.status = data.status;
  orgForm.description = data.description || "";
  orgForm.orgType = data.orgType;
  currentOrg.value = data;
  dialogVisible.value = true;
};

// 删除组织
const handleDelete = async (data: OrganizationDto) => {
  try {
    // 检查是否有子组织
    if (data.children && data.children.length > 0) {
      ElMessage.warning("该组织下有子组织，无法删除");
      return;
    }

    await ElMessageBox.confirm("确认要删除该组织吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await deleteOrganization(data.key);
    ElMessage.success("删除组织成功");

    // 刷新组织树和列表
    await getOrganizationTree();
    await getList();

    // 如果删除的是当前选中的组织，则清空当前选中
    if (currentOrg.value && currentOrg.value.key === data.key) {
      currentOrg.value = undefined;
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除组织失败", error);
      ElMessage.error("删除组织失败");
    }
  }
};

// 提交表单
const submitForm = async () => {
  if (!orgFormRef.value) return;

  await orgFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === "edit" && currentOrg.value) {
          await updateOrganization(currentOrg.value.key, orgForm);
          ElMessage.success("更新组织成功");
        } else {
          await createOrganization(orgForm);
          ElMessage.success("新增组织成功");
        }
        dialogVisible.value = false;

        // 刷新组织树和列表
        await getOrganizationTree();
        await getList();
      } catch (error) {
        console.error("保存组织失败", error);
        ElMessage.error("保存组织失败");
      }
    }
  });
};

// 组件挂载时获取组织机构树和列表
onMounted(() => {
  getOrganizationTree();
  getList();
});
</script>
