<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <search-form
      :form-items="searchFormItems" :initial-values="queryParams" @search="handleSearch"
      @reset="handleReset" />

    <!-- 数据表格 -->
    <data-table
      title="用户列表" :data="userList" :actionWidth="350" :columns="tableColumns" :loading="loading"
      :total="total" :current-page-prop="queryParams.$pageIndex" :page-size-prop="queryParams.$pageSize" row-key="key"
      @page-change="handleCurrentChange" @size-change="handleSizeChange">
      <!-- 工具栏插槽 -->
      <template #toolbar>
        <el-button type="primary" @click="handleAdd" v-hasPermission="['system:user:add']">
          <el-icon>
            <Plus />
          </el-icon>
          新增
        </el-button>
      </template>

      <!-- 用户类型列插槽 -->
      <template #userType="{ row }">
        <el-tag v-if="row.userType === UserType.Internal" type="primary">内部用户</el-tag>
        <el-tag v-else-if="row.userType === UserType.External" type="success">外部用户</el-tag>
        <el-tag v-else-if="row.userType === UserType.System" type="warning">系统用户</el-tag>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag v-if="row.status === UserStatus.Enabled" type="success">启用</el-tag>
        <el-tag v-else-if="row.status === UserStatus.Disabled" type="danger">禁用</el-tag>
        <el-tag v-else-if="row.status === UserStatus.Locked" type="warning">锁定</el-tag>
      </template>

      <!-- 邮箱验证列插槽 -->
      <template #emailConfirmed="{ row }">
        <el-tag v-if="row.emailConfirmed" type="success">已验证</el-tag>
        <el-tag v-else type="info">未验证</el-tag>
      </template>

      <!-- 手机验证列插槽 -->
      <template #mobileConfirmed="{ row }">
        <el-tag v-if="row.mobileConfirmed" type="success">已验证</el-tag>
        <el-tag v-else type="info">未验证</el-tag>
      </template>

      <!-- 操作列插槽 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleEdit(row)" v-hasPermission="['system:user:edit']">
          <el-icon>
            <Edit />
          </el-icon>
          编辑
        </el-button>
        <el-button
          v-if="row.status === UserStatus.Disabled" type="success" link @click="handleEnable(row)"
          v-hasPermission="['system:user:enable']">
          <el-icon>
            <Check />
          </el-icon>
          启用
        </el-button>
        <el-button
          v-if="row.status === UserStatus.Enabled" type="danger" link @click="handleDisable(row)"
          v-hasPermission="['system:user:disable']">
          <el-icon>
            <Close />
          </el-icon>
          禁用
        </el-button>
        <el-button type="primary" link @click="handleRoles(row)" v-hasPermission="['system:user:role']">
          <el-icon>
            <UserFilled />
          </el-icon>
          角色
        </el-button>
        <el-button
          type="primary" link @click="handleOrganizationsAndPositions(row)"
          v-hasPermission="['system:user:org']">
          <el-icon>
            <OfficeBuilding />
          </el-icon>
          组织与岗位
        </el-button>
      </template>
    </data-table>

    <!-- 用户表单对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" append-to-body destroy-on-close>
      <el-form ref="userFormRef" :model="userForm" :rules="userFormRules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" :disabled="dialogType === 'edit'" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item v-if="dialogType=='add'" label="用户密码" prop="password">
          <el-input v-model="userForm.password" type="password" placeholder="请输入用户密码" />
        </el-form-item>
        <el-form-item v-if="dialogType=='add'" label="确认密码" prop="confirmPassword">
          <el-input v-model="userForm.confirmPassword" type="password" placeholder="请输入确认密码" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="userForm.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="用户类型" prop="userType">
          <el-select v-model="userForm.userType" placeholder="请选择用户类型">
            <el-option :value="UserType.Internal" label="内部用户" />
            <el-option :value="UserType.External" label="外部用户" />
            <el-option :value="UserType.System" label="系统用户" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="userForm.status" placeholder="请选择状态">
            <el-option :value="UserStatus.Enabled" label="启用" />
            <el-option :value="UserStatus.Disabled" label="禁用" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="头像" prop="avatar">
          <el-input v-model="userForm.avatar" placeholder="请输入头像URL" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户角色对话框 -->
    <el-dialog
      v-model="roleDialogVisible" title="分配角色" width="750px" append-to-body destroy-on-close
      class="role-assignment-dialog">
      <el-form label-width="100px">
        <el-form-item label="用户">
          <div class="user-info">
            <span class="username">{{ currentUser?.username }}</span>
            <span class="realname" v-if="currentUser?.realName">({{ currentUser?.realName }})</span>
          </div>
        </el-form-item>
        <el-form-item label="角色">
          <div class="role-description">
            请从左侧选择角色，添加到右侧进行分配
          </div>
          <el-transfer
            v-model="selectedRoles" :data="roleOptions" :titles="['可选角色', '已选角色']" :props="{
              key: 'key',
              label: 'name',
            }" filterable :filter-method="filterMethod" filter-placeholder="请输入角色名称">
            <template #default="{ option }">
              <div class="role-item">
                <div class="role-name">{{ option.name }}</div>
                <div class="role-code" v-if="option.code">
                  {{ option.code }}
                </div>
              </div>
            </template>
          </el-transfer>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="roleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRoles">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新的用户组织和岗位分配对话框 -->
    <el-dialog
      v-model="orgPositionDialogVisible" title="分配组织和岗位" width="900px" append-to-body destroy-on-close
      class="org-position-dialog">
      <div class="dialog-content-wrapper">
        <el-form label-width="100px">
          <el-form-item label="用户">
            <div class="user-info">
              <span class="username">{{ currentUser?.username }}</span>
              <span class="realname" v-if="currentUser?.realName">({{ currentUser?.realName }})</span>
            </div>
          </el-form-item>

          <!-- 组织选择树 -->
          <el-form-item label="组织机构">
            <div class="org-tree-container">
              <el-tree
                ref="orgTreeRef" :data="organizationOptions" show-checkbox :check-strictly="true" node-key="key"
                :props="{
                  label: 'name',
                  children: 'children',
                }" @check="handleOrgCheck" />
            </div>
          </el-form-item>

          <!-- 主要组织选择 -->
          <el-form-item label="主要组织">
            <el-select v-model="primaryOrgId" placeholder="请选择主要组织" clearable>
              <el-option v-for="org in selectedOrganizations" :key="org.key" :label="org.name" :value="org.key" />
            </el-select>
          </el-form-item>

          <!-- 岗位分配区域 -->
          <el-form-item label="岗位分配">
            <div class="position-assignment">
              <div v-for="org in selectedOrganizations" :key="org.key" class="org-positions">
                <div class="org-title">
                  <span>{{ org.name }}</span>
                  <el-button type="primary" link @click="addPositionToOrg(org.key)">
                    <el-icon>
                      <Plus />
                    </el-icon>
                    添加岗位
                  </el-button>
                </div>

                <div class="position-list">
                  <div v-for="(position, index) in getOrgPositions(org.key)" :key="index" class="position-item">
                    <el-row :gutter="10">
                      <el-col :span="6">
                        <el-select
                          v-model="position.positionId" placeholder="请选择岗位" clearable filterable
                          class="full-width">
                          <el-option v-for="pos in positionOptions" :key="pos.key" :label="pos.name" :value="pos.key" />
                        </el-select>
                      </el-col>
                      <el-col :span="5">
                        <el-date-picker
                          v-model="position.startTime" type="date" placeholder="开始日期" format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD" class="full-width" />
                      </el-col>
                      <el-col :span="5">
                        <el-date-picker
                          v-model="position.endTime" type="date" placeholder="结束日期" format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD" class="full-width" />
                      </el-col>
                      <el-col :span="3">
                        <el-select v-model="position.status" placeholder="状态" class="full-width">
                          <el-option :value="1" label="有效" />
                          <el-option :value="0" label="无效" />
                        </el-select>
                      </el-col>
                      <el-col :span="3">
                        <el-checkbox
                          v-model="position.isPrimary" @change="
                            (val) =>
                              handlePrimaryPositionChange(!!val, org.key, index)
                          " class="primary-checkbox">
                          主要
                        </el-checkbox>
                      </el-col>
                      <el-col :span="2" class="text-right">
                        <el-button type="danger" circle @click="removePosition(org.key, index)" class="delete-btn">
                          <el-icon>
                            <Delete />
                          </el-icon>
                        </el-button>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="orgPositionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitOrgAndPositions">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import {
  Plus,
  Edit,
  Check,
  Close,
  UserFilled,
  OfficeBuilding,
  Delete,
} from "@element-plus/icons-vue";
import SearchForm, { FormItem } from "@/components/common/SearchForm.vue";
import DataTable, { TableColumn } from "@/components/common/DataTable.vue";
import {
  getUserPage,
  createUser,
  updateUser,
  enableUser,
  disableUser,
  getRoleList,
  getUserRoleList,
  updateUserRoles,
  getOrganizationList,
  getUserOrganizationList,
  updateUserOrganizations,
  getPositionList,
  getUserPositionList,
  updateUserPositions,
} from "@/api/rbac-mgt";
import { CommonStatus, RoleType, UserStatus, UserType } from "@/enums";
import {
  UserDto,
  UserOperationDto,
  RoleDto,
  OrganizationDto,
  PositionDto,
  UserPositionDto,
  UserPositionOperationDto,
} from "@/dtos";
import CryptoJS from "crypto-js";
import { useCryptoStore } from '@/stores/crypto';
// 搜索表单项配置
const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "用户名",
    prop: "Username",
    placeholder: "请输入用户名",
  },
  {
    type: "input",
    label: "真实姓名",
    prop: "RealName",
    placeholder: "请输入真实姓名",
  },
  { type: "input", label: "邮箱", prop: "Email", placeholder: "请输入邮箱" },
  {
    type: "input",
    label: "手机号",
    prop: "Mobile",
    placeholder: "请输入手机号",
  },
  {
    type: "select",
    label: "用户类型",
    prop: "UserType",
    placeholder: "请选择用户类型",
    options: [
      { label: "内部用户", value: UserType.Internal },
      { label: "外部用户", value: UserType.External },
      { label: "系统用户", value: UserType.System },
    ],
  },
  {
    type: "select",
    label: "用户状态",
    prop: "Status",
    placeholder: "请选择用户状态",
    options: [
      { label: "启用", value: UserStatus.Enabled },
      { label: "禁用", value: UserStatus.Disabled },
      { label: "锁定", value: UserStatus.Locked },
    ],
  },
]);

// 表格列配置
const tableColumns = ref<TableColumn[]>([
  { prop: "username", label: "用户名" },
  { prop: "realName", label: "真实姓名" },
  { prop: "email", label: "邮箱", width: 180 },
  { prop: "mobile", label: "手机号" },
  { prop: "userType", label: "用户类型", slot: "userType" },
  { prop: "status", label: "状态", slot: "status" },
  { prop: "emailConfirmed", label: "邮箱验证", slot: "emailConfirmed" },
  { prop: "mobileConfirmed", label: "手机验证", slot: "mobileConfirmed" },
]);

// 查询参数
const queryParams = reactive({
  Username: "",
  RealName: "",
  Email: "",
  Mobile: "",
  UserType: undefined,
  Status: null,
  $pageIndex: 1,
  $pageSize: 10,
  $sortBy: "Username",
  $orderBy: "desc" as "asc" | "desc",
});

// 用户列表数据
const userList = ref<UserDto[]>([]);
const total = ref(0);
const loading = ref(false);

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");
const dialogTitle = computed(() =>
  dialogType.value === "add" ? "新增用户" : "编辑用户"
);
const userFormRef = ref<FormInstance>();
const userForm = reactive<UserOperationDto>({
  username: "",
  realName: "",
  email: "",
  mobile: "",
  avatar: "",
  userType: UserType.Internal,
  status: UserStatus.Enabled,
  password: "",
  confirmPassword: "",
});

// 表单验证规则
const userFormRules: FormRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 20,
      message: "用户名长度在 3 到 20 个字符",
      trigger: "blur",
    },
  ],
  realName: [{ required: true, message: "请输入真实姓名", trigger: "blur" }],
  email: [
    { required: true, message: "请输入邮箱", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
  ],
  mobile: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  userType: [{ required: true, message: "请选择用户类型", trigger: "change" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
};

// 角色相关
const roleDialogVisible = ref(false);
const currentUser = ref<UserDto>();
const roleOptions = ref<RoleDto[]>([]);
const selectedRoles = ref<string[]>([]);
const userRoles = ref<string[]>([]);

// 组织和岗位分配相关
const orgPositionDialogVisible = ref(false);
const orgTreeRef = ref();
const organizationOptions = ref<OrganizationDto[]>([]);
const positionOptions = ref<PositionDto[]>([]);
const selectedOrganizations = computed(() => {
  if (!orgTreeRef.value) return [];
  const checkedKeys = orgTreeRef.value.getCheckedKeys();
  return flatOrganizations.value.filter((org) => checkedKeys.includes(org.key));
});

const primaryOrgId = ref<string>();
const flatOrganizations = ref<OrganizationDto[]>([]);

interface OrgPositionMap {
  [orgId: string]: UserPositionOperationDto[];
}

const orgPositionsMap = ref<OrgPositionMap>({});

// 获取组织机构树
const getOrganizations = async () => {
  try {
    const { data } = await getOrganizationList();
    organizationOptions.value = buildOrgTree(data);
    flatOrganizations.value = data;
  } catch (error) {
    console.error("获取组织机构列表失败", error);
    ElMessage.error("获取组织机构列表失败");
  }
};

// 构建组织机构树
const buildOrgTree = (orgs: OrganizationDto[]) => {
  const result: OrganizationDto[] = [];
  const map = new Map();

  orgs.forEach((org) => {
    map.set(org.key, { ...org, children: [] });
  });

  orgs.forEach((org) => {
    const node = map.get(org.key);
    if (org.parentId && map.has(org.parentId)) {
      const parent = map.get(org.parentId);
      parent.children.push(node);
    } else {
      result.push(node);
    }
  });

  return result;
};

// 获取岗位列表
const getPositions = async () => {
  try {
    const { data } = await getPositionList();
    positionOptions.value = data;
  } catch (error) {
    console.error("获取岗位列表失败", error);
    ElMessage.error("获取岗位列表失败");
  }
};

// 处理组织选择变化
const handleOrgCheck = (data: any, checked: any) => {
  const { checkedKeys } = checked;

  // 不再删除未选中组织的岗位数据
  // 只处理主要组织的逻辑
  if (primaryOrgId.value && !checkedKeys.includes(primaryOrgId.value)) {
    primaryOrgId.value = undefined;
  }
};

// 获取指定组织的岗位列表
const getOrgPositions = (orgId: string) => {
  if (!orgPositionsMap.value[orgId]) {
    orgPositionsMap.value[orgId] = [];
  }
  return orgPositionsMap.value[orgId];
};

// 为组织添加岗位
const addPositionToOrg = (orgId: string) => {
  if (!currentUser.value) return;

  if (!orgPositionsMap.value[orgId]) {
    orgPositionsMap.value[orgId] = [];
  }

  orgPositionsMap.value[orgId].push({
    userId: currentUser.value.key,
    positionId: undefined as unknown as string,
    organizationId: orgId,
    isPrimary: false,
    status: 1,
  });
};

// 移除岗位
const removePosition = (orgId: string, index: number) => {
  orgPositionsMap.value[orgId].splice(index, 1);
};

// 处理主要岗位变更
const handlePrimaryPositionChange = (
  checked: boolean,
  orgId: string,
  index: number
) => {
  if (checked) {
    // 将其他所有岗位设置为非主要
    Object.values(orgPositionsMap.value).forEach(
      (positions: UserPositionOperationDto[]) => {
        positions.forEach(
          (pos: UserPositionOperationDto) => (pos.isPrimary = false)
        );
      }
    );
    // 设置当前岗位为主要
    orgPositionsMap.value[orgId][index].isPrimary = true;
  }
};

// 加载用户现有的组织和岗位数据
const loadUserOrgAndPositions = async (userId: string) => {
  try {
    // 加载用户组织
    const { data: userOrgs } = await getUserOrganizationList({
      UserId: userId,
    });
    if (orgTreeRef.value) {
      orgTreeRef.value.setCheckedKeys(
        userOrgs.map((org) => org.organizationId)
      );
      const primaryOrg = userOrgs.find((org) => org.isPrimary);
      if (primaryOrg) {
        primaryOrgId.value = primaryOrg.organizationId;
      }
    }

    // 加载用户岗位
    const { data: userPositions } = await getUserPositionList({ userId });

    // 重置岗位映射
    orgPositionsMap.value = {};

    // 按组织分组岗位
    if (Array.isArray(userPositions)) {
      (userPositions as Array<UserPositionDto>).forEach((position) => {
        const orgId = position.organizationId;
        if (!orgPositionsMap.value[orgId]) {
          orgPositionsMap.value[orgId] = [];
        }

        orgPositionsMap.value[orgId].push({
          userId,
          positionId: position.positionId,
          organizationId: orgId,
          isPrimary: position.isPrimary,
          startTime: position.startTime,
          endTime: position.endTime,
          status: position.status,
        });
      });
    }
  } catch (error) {
    console.error("加载用户组织和岗位数据失败", error);
    ElMessage.error("加载用户组织和岗位数据失败");
  }
};

// 打开组织和岗位分配对话框
const handleOrganizationsAndPositions = async (row: UserDto) => {
  currentUser.value = row;
  orgPositionDialogVisible.value = true;

  await Promise.all([getOrganizations(), getPositions()]);

  // 等待DOM更新后加载用户数据
  setTimeout(() => {
    loadUserOrgAndPositions(row.key);
  }, 100);
};

// 提交组织和岗位分配
const submitOrgAndPositions = async () => {
  if (!currentUser.value || !orgTreeRef.value) return;

  try {
    const checkedOrgs = orgTreeRef.value.getCheckedKeys();

    // 验证是否选择了组织
    if (checkedOrgs.length === 0) {
      ElMessage.warning("请至少选择一个组织");
      return;
    }

    // 验证是否选择了主要组织
    if (!primaryOrgId.value || !checkedOrgs.includes(primaryOrgId.value)) {
      ElMessage.warning("请选择一个主要组织");
      return;
    }

    // 准备组织数据
    const orgsToUpdate = checkedOrgs.map((orgId: string) => ({
      organizationId: orgId,
      isPrimary: orgId === primaryOrgId.value,
    }));

    // 准备岗位数据 - 只提交选中组织的岗位数据
    const positionsToUpdate: UserPositionOperationDto[] = [];
    checkedOrgs.forEach((orgId: string) => {
      if (orgPositionsMap.value[orgId]) {
        orgPositionsMap.value[orgId].forEach(
          (position: UserPositionOperationDto) => {
            if (position.positionId) {
              // 只添加已选择岗位的数据
              positionsToUpdate.push(position);
            }
          }
        );
      }
    });

    // 验证主要岗位
    const hasPrimaryPosition =
      positionsToUpdate.length === 0 ||
      positionsToUpdate.some((p) => p.isPrimary);
    if (!hasPrimaryPosition) {
      ElMessage.warning("请设置一个主要岗位");
      return;
    }

    // 批量更新组织和岗位
    await Promise.all([
      updateUserOrganizations(currentUser.value.key, orgsToUpdate),
      updateUserPositions(currentUser.value.key, positionsToUpdate),
    ]);

    ElMessage.success("分配组织和岗位成功");
    orgPositionDialogVisible.value = false;
  } catch (error) {
    console.error("分配组织和岗位失败", error);
    ElMessage.error("分配组织和岗位失败");
  }
};

// 获取用户列表
const getList = async () => {
  loading.value = true;
  try {
    const response = await getUserPage(queryParams);
    console.log("用户列表数据:", response);

    const { data } = response;
    userList.value = data.rows || [];

    total.value = data.totals;
  } catch (error) {
    console.error("获取用户列表失败", error);
    ElMessage.error("获取用户列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  getList();
};

// 重置搜索
const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  queryParams.$pageSize = size;
  getList();
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.$pageIndex = page;
  getList();
};

// 新增用户
const handleAdd = () => {
  dialogType.value = "add";
  userForm.username = "";
  userForm.realName = "";
  userForm.email = "";
  userForm.mobile = "";
  userForm.avatar = "";
  userForm.userType = UserType.Internal;
  userForm.status = UserStatus.Enabled;
  userForm.password = "";
  userForm.confirmPassword = "";
  dialogVisible.value = true;
  userFormRules.password = [
    { required: true, message: '请输入用户密码', trigger: "blur" },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+\-=`{}[\]|\\:;"'<>,.?/]).{6,20}$/,
      message: "密码需包含大小写字母、数字、特殊字符，长度6-20位",
      trigger: "blur",
    },
    {
      min: 6,
      max: 20,
      message: "用户密码长度在 6 到 20 个字符",
      trigger: "blur",
    }
  ];
  userFormRules.confirmPassword = [
    { required: true, message: '请输入确认密码', trigger: "blur" },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+\-=`{}[\]|\\:;"'<>,.?/]).{6,20}$/,
      message: "确认密码需包含大小写字母、数字、特殊字符，长度6-20位",
      trigger: "blur",
    },
    {
      validator: (rule, value, callback) => {
        if (value !== userForm.password) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: ["blur", "change"]
    }
  ];
};

// 编辑用户
const handleEdit = (row: UserDto) => {
  dialogType.value = "edit";
  userForm.username = row.username || "";
  userForm.realName = row.realName || "";
  userForm.email = row.email || "";
  userForm.mobile = row.mobile || "";
  // userForm.avatar = row.avatar || "";
  userForm.userType = row.userType;
  userForm.status = row.status;
  currentUser.value = row;
  dialogVisible.value = true;
  userFormRules.password = [];
  userFormRules.confirmPassword = [];
};

// 提交表单
const submitForm = async () => {
  if (!userFormRef.value) return;
// 编辑时移除密码校验
  if (dialogType.value !== "add") {
    userFormRules.password = [];
    userFormRules.confirmPassword = [];
  } else {
    userFormRules.password = [
      { required: true, message: '请输入用户密码', trigger: "blur" },
      {
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+\-=`{}[\]|\\:;"'<>,.?/]).{6,20}$/,
        message: "密码需包含大小写字母、数字、特殊字符，长度6-20位",
        trigger: "blur",
      },
      {
        min: 6,
        max: 20,
        message: "用户密码长度在 6 到 20 个字符",
        trigger: "blur",
      }
    ];
    userFormRules.confirmPassword = [
      { required: true, message: '请输入确认密码', trigger: "blur" },
      {
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+\-=`{}[\]|\\:;"'<>,.?/]).{6,20}$/,
        message: "确认密码需包含大小写字母、数字、特殊字符，长度6-20位",
        trigger: "blur",
      },
      {
        validator: (rule, value, callback) => {
          if (value !== userForm.password) {
            callback(new Error("两次输入的密码不一致"));
          } else {
            callback();
          }
        },
        trigger: ["blur", "change"]
      }
    ];
  }
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === "add") {
          const encrypt_password = encryptPassword(userForm.password).toString();
          const encrypt_repassword = encryptPassword(userForm.confirmPassword).toString();
          userForm.password = encrypt_password;
          userForm.confirmPassword = encrypt_repassword;
          await createUser(userForm);
          ElMessage.success("新增用户成功");
        } else {
          if (!currentUser.value) return;
          await updateUser(currentUser.value.key, userForm);
          ElMessage.success("更新用户成功");
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        userForm.password = "";
        userForm.confirmPassword = "";
        console.error("保存用户失败", error);
        ElMessage.error("保存用户失败");
      }
    }
  });
};
const cryptoStore = useCryptoStore();

function encryptPassword(password: string) {
    // 明确指定IV为全0
    const iv = CryptoJS.enc.Utf8.parse('\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0');
    const keyUtf8 = CryptoJS.enc.Utf8.parse(cryptoStore.aesKey);
    const encrypted = CryptoJS.AES.encrypt(password, keyUtf8, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString(); // Base64
}
// 启用用户
const handleEnable = async (row: UserDto) => {
  try {
    await enableUser(row.key);
    ElMessage.success("启用用户成功");
    getList();
  } catch (error) {
    console.error("启用用户失败", error);
    ElMessage.error("启用用户失败");
  }
};

// 禁用用户
const handleDisable = async (row: UserDto) => {
  try {
    await ElMessageBox.confirm("确认要禁用该用户吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await disableUser(row.key);
    ElMessage.success("禁用用户成功");
    getList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("禁用用户失败", error);
      ElMessage.error("禁用用户失败");
    }
  }
};

// 获取所有通用角色和系统角色
const getRoles = async () => {
  try {
    // 获取所有角色
    const { data } = await getRoleList();
    // 过滤，只保留通用角色和系统角色
    roleOptions.value = data.filter(
      (role) => role.type === RoleType.General || role.type === RoleType.System
    );
  } catch (error) {
    console.error("获取角色列表失败", error);
    ElMessage.error("获取角色列表失败");
  }
};

// 获取用户角色
const getUserRoles = async (userId: string) => {
  try {
    const { data } = await getUserRoleList({ userId: userId });
    userRoles.value = data.map((item) => item.roleId);
    selectedRoles.value = [...userRoles.value];
  } catch (error) {
    console.error("获取用户角色失败", error);
    ElMessage.error("获取用户角色失败");
  }
};

// 角色筛选方法
const filterMethod = (query: string, item: any) => {
  if (query === "") return true;
  return (
    item.name?.toLowerCase().includes(query.toLowerCase()) ||
    item.code?.toLowerCase().includes(query.toLowerCase()) ||
    false
  );
};

// 打开角色分配对话框
const handleRoles = async (row: UserDto) => {
  currentUser.value = row;
  await getRoles();
  await getUserRoles(row.key);
  roleDialogVisible.value = true;
};

// 提交角色分配
const submitRoles = async () => {
  if (!currentUser.value) return;

  try {
    // 将选中的角色ID转换为UserRoleOperationDto数组
    const userRoleOperationDtos = selectedRoles.value.map((roleId) => ({
      userId: currentUser.value!.key,
      roleId: roleId,
      status: CommonStatus.Enabled,
    }));

    console.log("提交用户角色:", currentUser.value.key, userRoleOperationDtos);

    // 使用新的API方法一次性更新用户角色
    await updateUserRoles(currentUser.value.key, userRoleOperationDtos);

    ElMessage.success("分配角色成功");
    roleDialogVisible.value = false;

    // 更新本地缓存的用户角色
    userRoles.value = [...selectedRoles.value];
  } catch (error) {
    console.error("分配角色失败", error);
    ElMessage.error("分配角色失败");
  }
};

// 初始化
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.org-tree-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.position-assignment {
  margin-top: 20px;
}

.org-positions {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;

  .org-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    span {
      font-weight: bold;
      font-size: 14px;
      color: #303133;
    }
  }
}

.position-item {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &:last-child {
    margin-bottom: 0;
  }

  .el-row {
    align-items: center;
  }
}

.full-width {
  width: 100%;
}

.primary-checkbox {
  margin-left: 8px;
}

.text-right {
  text-align: right;
}

.delete-btn {
  padding: 6px;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

.user-info {
  .username {
    font-weight: bold;
    margin-right: 5px;
  }

  .realname {
    color: #666;
  }
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-checkbox) {
  margin-right: 0;
}

.org-position-dialog {
  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 90vh;
    max-width: 90vw;
  }

  :deep(.el-dialog__header) {
    padding: 20px;
    margin: 0;
    border-bottom: 1px solid #dcdfe6;
  }

  :deep(.el-dialog__body) {
    flex: 1;
    overflow: hidden;
    padding: 0;
  }

  :deep(.el-dialog__footer) {
    padding: 20px;
    border-top: 1px solid #dcdfe6;
    background-color: #fff;
  }
}

.dialog-content-wrapper {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  max-height: calc(90vh - 120px); // 减去头部和底部的高度
}

.org-tree-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.position-assignment {
  margin-top: 20px;
}

.org-positions {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;

  &:last-child {
    margin-bottom: 0;
  }
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

/* 自定义滚动条样式 */
.dialog-content-wrapper {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
  }
}
.org-position-dialog {
  .dialog-content-wrapper {
    height: 550px;
  }
}
</style>
