<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <div class="form-header">
      <h2>修改填报</h2>
      <div class="form-actions">
        <el-select v-model="language" style="width: 160px">
          <el-option label="仅中文" value="zh" />
          <el-option label="仅英文" value="en" />
          <el-option label="中英文" value="both" />
        </el-select>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </div>

    <FormCanvas
      :formSchema="formSchema"
      :render-mode="renderMode as any"
      :language="language as any"
      @update:field="onUpdateField"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";
import {
  getLatestFormInstance,
  saveFormInstance,
  submitFormInstance,
} from "@/api/dynamic-form";

// 定义FormSchema接口
interface FormSchema {
  groups: any[];
}

const route = useRoute();
const router = useRouter();
const businessId = route.params.businessId as string;
// version参数不再直接用const，而是每次用route.params.version

// 表单渲染模式
const renderMode = ref("edit");
// 语言选择
const language = ref("zh");
// 表单数据
const formSchema = ref<FormSchema>({ groups: [] });

// 字段更新处理
const onUpdateField = (field: any) => {
  console.log("字段更新:", field);
  // 实现字段更新逻辑
};

// 加载表单实例
const loadFormInstance = async () => {
  try {
    const response = await getLatestFormInstance(businessId, route.params.version as string);
    if (response.data) {
      // 保证groups为数组，避免undefined
      formSchema.value = {
        ...response.data,
        groups: response.data.groups ?? [],
      };
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    ElMessage.error(`获取表单定义失败: ${error.message || error}`);
  }
};

// 保存表单
const handleSave = async () => {
  try {
    const response = await saveFormInstance(
      businessId,
      route.params.version as string,
      formSchema.value
    );
    ElMessage.success("保存成功");
    // 只在版本号变化时跳转
    if (String(response.data) !== String(route.params.version)) {
      router.push({
        path: `/instance/report-edit/${businessId}/${response.data}`,
      });
    }
  } catch (error: any) {
    ElMessage.error(`保存失败: ${error.message || error}`);
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    const response = await saveFormInstance(
      businessId,
      route.params.version as string,
      formSchema.value
    );
    await submitFormInstance(businessId, String(response.data));
    ElMessage.success("提交成功");
    router.push({ name: "form-management:my-reports" });
  } catch (error: any) {
    ElMessage.error(`提交失败: ${error.message || error}`);
  }
};

// 取消操作
const handleCancel = () => {
  router.go(-1);
};

// 组件挂载时加载表单
onMounted(loadFormInstance);

// 监听version参数变化，自动刷新表单数据
watch(
  () => route.params.version,
  async (newVersion, oldVersion) => {
    if (newVersion !== oldVersion) {
      await loadFormInstance();
    }
  }
);
</script>

<style lang="scss" scoped>

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}
</style>
