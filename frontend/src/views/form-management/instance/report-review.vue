<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <ReviewFormInstanceList
    formCode="demo"
    title="填报审核"
    :columns="columns"
    :onReview="handleReview"
  />
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import ReviewFormInstanceList from "./components/ReviewFormInstanceList.vue";
import { FormInstanceDto } from "@/dtos/dynamic-form.dto";

const router = useRouter();

const columns = [
  { prop: "name", label: "姓名", width: 120, search: true },
  { prop: "sex", label: "性别", width: 80 },
  { prop: "location", label: "所在地", width: 150 },
  { prop: "hobby", label: "爱好", width: 150 },
  { prop: "mobile", label: "手机号", width: 120, search: true },
  { prop: "trialTime", label: "试验参与时间", width: 180 },
  { prop: "period", label: "访试周期", width: 120 },
];

const handleReview = (row: FormInstanceDto) => {
  router.push({
    path: `/instance/report-approval/${row.businessId}/${row.version}`,
  });
};
</script>
