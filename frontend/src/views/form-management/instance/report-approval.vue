<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <div class="form-header">
      <h2>审批填报</h2>
      <div class="form-actions">
        <el-button @click="handleCancel">返回</el-button>
        <el-button type="success" @click="handleConfirm">确认</el-button>
        <el-button type="danger" @click="handleReject">驳回</el-button>
      </div>
    </div>
    <el-tabs v-model="activeTab">
      <el-tab-pane label="填报内容" name="content">
        <FormCanvas
          :formSchema="formSchema"
          :render-mode="renderMode"
          :language="language"
          @update:field="onUpdateField"
        />
      </el-tab-pane>
      <el-tab-pane label="变更历史" name="history">
        <el-table
          v-loading="historiesLoading"
          :data="histories"
          style="width: 100%; margin-top: 12px"
        >
          <el-table-column label="版本号" prop="version">
            <template #default="{ row }">
              <el-radio
                :label="row.version"
                v-model="selectedVersion"
                :disabled="row.version == version"
              >
                {{ row.version
                }}<el-button
                  v-if="row.version == selectedVersion"
                  type="primary"
                  size="small"
                  style="margin-left: 8px"
                  @click="handleCompare(row)"
                >
                  比较
                </el-button>
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column prop="previousVersion" label="上一版本" />
          <el-table-column
            prop="versionTime"
            label="版本时间"
            width="240"
            :formatter="(row) => formatToLocal(row.versionTime)"
          />
          <el-table-column
            prop="status"
            label="状态"
            width="180"
            :formatter="(row) => getStatusText(row)"
          />
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElRadio } from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";
import {
  getDiffWithPreviousVersion,
  getFormInstanceHistories,
} from "@/api/dynamic-form";
import {
  confirmFormInstance,
  rejectFormInstance,
} from "@/api/dynamic-form-mgt";
import { FormInstanceStatus } from "@/enums";
import { formatToLocal } from "@/utils/date";
import { FormInstanceDto } from "@/dtos/dynamic-form.dto";

// 定义FormSchema接口
interface FormSchema {
  groups: any[];
}

const route = useRoute();
const router = useRouter();
const businessId = route.params.businessId as string;
const version = route.params.version as string;

// 表单渲染模式
const renderMode = ref("approval");
// 语言选择
const language = ref("zh");
// 表单数据
const formSchema = ref<FormSchema>({ groups: [] });

const activeTab = ref("content");
const histories = ref<any[]>([]);
const historiesLoading = ref(false);
const selectedVersion = ref<string | null>(null);

const getStatusText = (row: FormInstanceDto) => {
  var annotation = row.isObsoleted ? "(已失效)" : "";
  if (row.version == version) {
    annotation = "(当前版本)" + annotation;
  }
  switch (row.status) {
    case FormInstanceStatus.Draft:
      return "草稿" + annotation;
    case FormInstanceStatus.Submitted:
      return "已提交" + annotation;
    case FormInstanceStatus.Confirmed:
      return "已确认" + annotation;
    case FormInstanceStatus.Cancelled:
      return "已作废" + annotation;
    case FormInstanceStatus.Rejected:
      return "已驳回" + annotation;
    default:
      return "未知";
  }
};
const loadHistories = async () => {
  try {
    historiesLoading.value = true;
    const res = await getFormInstanceHistories(businessId);
    histories.value = res.data || [];
  } catch (e: any) {
    ElMessage.error("获取变更历史失败: " + (e?.message || e));
  } finally {
    historiesLoading.value = false;
  }
};

watch(activeTab, (val) => {
  if (val === "history" && histories.value.length === 0) {
    loadHistories();
  }
});

// 字段更新处理
const onUpdateField = (field: any) => {
  console.log("字段更新:", field);
  // 实现字段更新逻辑
};

const handleCompare = (row: any) => {
  console.log("比较版本：", row.version, row);
  router.push({
    path: `/instance/report-compare/${businessId}/${version}/${row.version}`,
  });
  // 这里可以做你想做的事情，比如高亮、对比、加载详情等
};

// 取消操作
const handleCancel = () => {
  // router.push({ name: "form-management:report-review" });
  router.go(-1);
};

// 确认操作
const handleConfirm = async () => {
  try {
    await confirmFormInstance(businessId, version);
    ElMessage.success("确认成功");
    router.push({ name: "form-management:report-review" });
  } catch (error: any) {
    ElMessage.error(`确认失败: ${error.message || error}`);
  }
};

// 驳回操作
const handleReject = async () => {
  try {
    var annotations: Record<string, string> = {};
    formSchema.value.groups.forEach((group) => {
      group.fields.forEach((field) => {
        annotations[field.code] = field.warning;
        field.fields.forEach((child) => {
          annotations[child.code] = child.warning;
        });
      });
    });

    await rejectFormInstance(businessId, version, annotations);
    ElMessage.success("驳回成功");
    router.push({ name: "form-management:report-review" });
  } catch (error: any) {
    ElMessage.error(`驳回失败: ${error.message || error}`);
  }
};
// 组件挂载时加载表单
onMounted(async () => {
  try {
    // 创建表单实例，formCode固定为test
    const response = await getDiffWithPreviousVersion(businessId, version);
    console.log("获取表单实例成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;
    }
  } catch (error: any) {
    console.error("获取表单实例失败:", error);
    ElMessage.error(`获取表单实例失败: ${error.message || error}`);
  }
});
</script>

<style lang="scss" scoped>

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}
</style>
