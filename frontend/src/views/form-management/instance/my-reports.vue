<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <UserFormInstanceList
    formCode="demo"
    title="我的填报列表"
    :columns="columns"
    :onAdd="handleAdd"
    :onEdit="handleEdit"
    :onView="handleView"
  />
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import UserFormInstanceList from "./components/UserFormInstanceList.vue";
import { FormInstanceDto } from "@/dtos/dynamic-form.dto";

const router = useRouter();

const columns = [
  { prop: 'name', label: '姓名', width: 120, search: true },
  { prop: 'sex', label: '性别', width: 80 },
  { prop: 'location', label: '所在地', width: 150 },
  { prop: 'hobby', label: '爱好', width: 150 },
  { prop: 'mobile', label: '手机号', width: 120, search: true },
  { prop: 'trialTime', label: '试验参与时间', width: 180 },
  { prop: 'period', label: '访试周期', width: 120 },
];

const handleAdd = () => {
  router.push({ path: "/instance/report-add" });
};
const handleEdit = (row: FormInstanceDto) => {
  router.push({ path: `/instance/report-edit/${row.businessId}/${row.version}` });
};
const handleView = (row: FormInstanceDto) => {
  router.push({ path: `/instance/report-view/${row.businessId}/${row.version}` });
};
</script>
