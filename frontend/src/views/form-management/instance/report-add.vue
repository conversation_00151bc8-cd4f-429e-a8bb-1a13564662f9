<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <div class="form-header">
      <h2>新增填报</h2>
      <div class="form-actions">
        <el-select v-model="language" style="width: 160px">
          <el-option label="仅中文" value="zh" />
          <el-option label="仅英文" value="en" />
          <el-option label="中英文" value="both" />
        </el-select>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </div>

    <FormCanvas
      :formSchema="formSchema"
      :render-mode="renderMode"
      :language="language"
      @update:field="onUpdateField"
    />
  </div>
</template>

<script setup lang="ts">
import { ref,  onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";
import {
  getFormDefinition,
  saveFormInstanceWithoutVersion,
  submitFormInstance,
} from "@/api/dynamic-form";

// 定义FormSchema接口
interface FormSchema {
  groups: any[];
}

const router = useRouter();
const route = useRoute();
const formCode = route.params.formCode as string;

// 表单渲染模式
const renderMode = ref("edit");
// 语言选择
const language = ref("zh");
// 表单数据
const formSchema = ref<FormSchema>({ groups: [] });

// 字段更新处理
const onUpdateField = (field: any) => {
  console.log("字段更新:", field);
  // 实现字段更新逻辑
};

// 保存表单
const handleSave = async () => {
  try {
    const response = await saveFormInstanceWithoutVersion(
      formCode ?? "demo",
      formSchema.value
    );
    ElMessage.success("保存成功");
    router.push({
      path: `/instance/report-edit/${response.data.businessId}/${response.data.version}`,
    });
  } catch (error: any) {
    ElMessage.error(`保存失败: ${error.message || error}`);
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 先保存
    const response = await saveFormInstanceWithoutVersion(
      formCode ?? "demo",
      formSchema.value
    );
    // 再提交
    await submitFormInstance(response.data.businessId, response.data.version); // 假设版本号为1，实际应从后端返回
    ElMessage.success("提交成功");
    router.push({ name: "form-management:my-reports" });
  } catch (error: any) {
    ElMessage.error(`提交失败: ${error.message || error}`);
  }
};

// 取消操作
const handleCancel = () => {
  router.go(-1);
};

// 组件挂载时加载表单
onMounted(async () => {
  try {
    // 获取表单定义，formCode固定为test
    const response = await getFormDefinition(formCode ?? "demo");
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    ElMessage.error(`获取表单定义失败: ${error.message || error}`);
  }
});
</script>

<style lang="scss" scoped>


.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}
</style>
