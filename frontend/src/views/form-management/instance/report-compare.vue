<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <div class="form-header">
      <h2>版本对比</h2>
      <el-button @click="handleCancel">返回</el-button>
    </div>

    <FormCanvas
      :formSchema="formSchema"
      :render-mode="renderMode"
      :language="language"
      @update:field="onUpdateField"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";
import { getDiffWithSpecificVersion } from "@/api/dynamic-form";

// 定义FormSchema接口
interface FormSchema {
  groups: any[];
}

const route = useRoute();
const router = useRouter();
const businessId = route.params.businessId as string;
const version = route.params.version as string;
const namedVersion = route.params.namedVersion as string;

// 表单渲染模式
const renderMode = ref("view");
// 语言选择
const language = ref("zh");
// 表单数据
const formSchema = ref<FormSchema>({ groups: [] });

// 字段更新处理
const onUpdateField = (field: any) => {
  console.log("字段更新:", field);
  // 实现字段更新逻辑
};

// 取消操作
const handleCancel = () => {
  router.back();
};

// 组件挂载时加载表单
onMounted(async () => {
  try {
    // 创建表单实例，formCode固定为test
    const response = await getDiffWithSpecificVersion(
      businessId,
      version,
      namedVersion
    );
    console.log("获取表单实例成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;
    }
  } catch (error: any) {
    console.error("获取表单实例失败:", error);
    ElMessage.error(`获取表单实例失败: ${error.message || error}`);
  }
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}
</style>
