<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      :title="props.title"
      :data="formInstanceList"
      :columns="tableColumns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #toolbar> </template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button
          type="primary"
          link
          @click="handleReview(row)"
          v-if="row.status === FormInstanceStatus.Submitted"
        >
          <el-icon><View /></el-icon>
          审核
        </el-button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import SearchForm, { FormItem } from "@/components/common/SearchForm.vue";
import DataTable, { TableColumn } from "@/components/common/DataTable.vue";
import { FormInstanceStatus } from "@/enums";
import { View } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { getFormInstancePage } from "@/api/dynamic-form-mgt";
import { fieldTypeMap } from "@/components/dynamic-form/form-fields";
import {
  FormInstanceDto,
  FormInstanceJsonDto,
  FormDefinitionDto,
} from "@/dtos/dynamic-form.dto";

// props
const props = defineProps<{
  formCode: string;
  title: string;
  columns: Array<{
    prop: string;
    label: string;
    width?: number;
    placeholder?: string;
    search?: boolean;
  }>;
  onReview?: (row: FormInstanceDto) => void;
}>();

// 生成searchFormItems
const searchFormItems = computed<FormItem[]>(() => {
  const items: FormItem[] = props.columns
    .filter((col) => col.search)
    .map((col) => ({
      type: "input",
      label: col.label,
      prop: col.prop,
      placeholder: col.placeholder || `请输入${col.label}`,
    }));
  // 固定追加status
  items.push({
    type: "select",
    label: "状态",
    prop: "status",
    placeholder: "请选择状态",
    options: [
      // { label: "草稿", value: FormInstanceStatus.Draft },
      { label: "已提交", value: FormInstanceStatus.Submitted },
      { label: "已确认", value: FormInstanceStatus.Confirmed },
      // { label: "已作废", value: FormInstanceStatus.Cancelled },
      // { label: "已驳回", value: FormInstanceStatus.Rejected },
    ],
  });
  return items;
});

// 生成tableColumns
const tableColumns = computed<TableColumn[]>(() => {
  const cols: TableColumn[] = props.columns.map((col) => ({
    prop: col.prop,
    label: col.label,
    width: col.width,
    formatter: (row: any) => getDisplay(row, col.prop),
  }));
  // 固定追加status列
  cols.push({
    prop: "status",
    label: "状态",
    width: 100,
    slot: "status",
  });
  return cols;
});

// 查询参数
const queryParams = reactive<any>({
  $pageIndex: 1,
  $pageSize: 10,
  formCode: props.formCode,
  status: [FormInstanceStatus.Submitted, FormInstanceStatus.Confirmed],
  dynamicQueries: {},
});

// 其它逻辑与原my-reports.vue类似
const loading = ref(false);
const total = ref(0);

const formInstanceList = ref<FormInstanceJsonDto[]>([]);
const formDefinition = ref<FormDefinitionDto>();

onMounted(() => {
  getList();
});

const getList = async () => {
  try {
    loading.value = true;
    const response = await getFormInstancePage(queryParams);
    const { data } = response;
    formInstanceList.value = data.rows || [];
    formDefinition.value = data.formDefinition;
    total.value = data.totals;
  } catch (error) {
    ElMessage.error("获取表单实例列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = (formData: Record<string, any>) => {
  const dynamicQueries: Record<string, any> = {};
  for (const key in formData) {
    if (formData[key] != "" && key != "status") {
      dynamicQueries[key] = formData[key];
    }
  }
  Object.assign(queryParams, {
    ...formData,
    status: formData.status,
    dynamicQueries: dynamicQueries,
  });
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

// getDisplay方法
function getDisplay(row: any, fieldCode: string): string {
  if (!formDefinition.value || !formDefinition.value.groups) return "";
  const field = formDefinition.value.groups
    .map((q: any) => q.fields)
    .flat()
    .find((q: any) => q.code === fieldCode);
  if (!field) return "";
  // 这里假设有fieldTypeMap
  return fieldTypeMap[field.type].getDisplay(row.value[fieldCode], field);
}

// 获取状态标签类型
const getStatusTagType = (status: FormInstanceStatus) => {
  switch (status) {
    case FormInstanceStatus.Draft:
      return "info";
    case FormInstanceStatus.Submitted:
      return "warning";
    case FormInstanceStatus.Confirmed:
      return "success";
    case FormInstanceStatus.Cancelled:
      return "danger";
    case FormInstanceStatus.Rejected:
      return "danger";
    default:
      return "info";
  }
};

const handleReview = (row) => {
  if (props.onReview) {
    props.onReview(row);
  }
};

// 获取状态文本
const getStatusText = (status: FormInstanceStatus) => {
  switch (status) {
    case FormInstanceStatus.Draft:
      return "草稿";
    case FormInstanceStatus.Submitted:
      return "已提交";
    case FormInstanceStatus.Confirmed:
      return "已确认";
    case FormInstanceStatus.Cancelled:
      return "已作废";
    case FormInstanceStatus.Rejected:
      return "已驳回";
    default:
      return "未知";
  }
};
</script>

<style lang="scss" scoped>

</style>
