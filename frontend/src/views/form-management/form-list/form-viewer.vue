<template>
  <div class="form-viewer">
    <div class="form-viewer-header">
      <h2>{{ formTitle }}</h2>
      <div class="form-viewer-actions">
        <el-radio-group v-model="currentMode" size="small">
          <el-radio-button label="edit">编辑模式</el-radio-button>
          <el-radio-button label="readonly">只读模式</el-radio-button>
          <el-radio-button label="view">查看模式</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    
    <div class="form-viewer-content">
      <FormCanvas
        :formSchema="formSchema"
        :render-mode="currentMode"
        @update:field="onFieldUpdate"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElRadioGroup, ElRadioButton } from 'element-plus'
import FormCanvas from './components/FormCanvas.vue'
import { type RenderMode } from '@/components/dynamic-form/form-fields/index'

// 表单标题
const formTitle = ref('表单查看器')

// 当前渲染模式
const currentMode = ref<RenderMode>('edit')

// 表单数据
const formSchema = ref({
  groups: [
    {
      id: 'g1',
      name: '基本信息',
      type: 'group',
      code: 'basic_info',
      fields: [
        {
          id: 'f1',
          code: 'name',
          name: '姓名',
          type: 'input',
          labelZh: '姓名',
          labelEn: 'Name',
          required: true,
          unit: 1,
          value: '张三'
        },
        {
          id: 'f2',
          code: 'age',
          name: '年龄',
          type: 'number',
          labelZh: '年龄',
          labelEn: 'Age',
          required: false,
          unit: 1,
          value: 25,
          extends: {
            precision: 0,
            step: 1
          }
        },
        {
          id: 'f3',
          code: 'gender',
          name: '性别',
          type: 'radio',
          labelZh: '性别',
          labelEn: 'Gender',
          required: true,
          unit: 1,
          value: 'male',
          options: [
            { labelZh: '男', labelEn: 'Male', value: 'male' },
            { labelZh: '女', labelEn: 'Female', value: 'female' }
          ]
        }
      ]
    },
    {
      id: 'g2',
      name: '联系方式',
      type: 'group',
      code: 'contact_info',
      fields: [
        {
          id: 'f4',
          code: 'phone',
          name: '电话',
          type: 'input',
          labelZh: '电话',
          labelEn: 'Phone',
          required: true,
          unit: 1,
          value: '13800138000'
        },
        {
          id: 'f5',
          code: 'email',
          name: '邮箱',
          type: 'input',
          labelZh: '邮箱',
          labelEn: 'Email',
          required: false,
          unit: 1,
          value: '<EMAIL>'
        }
      ]
    },
    {
      id: 'g3',
      name: '子表单示例',
      type: 'group',
      code: 'subform_example',
      fields: [
        {
          id: 'sf1',
          code: 'education',
          name: '教育经历',
          type: 'subForm',
          labelZh: '教育经历',
          labelEn: 'Education',
          required: false,
          unit: 3,
          fields: [
            {
              id: 'sf1_f1',
              code: 'school',
              name: '学校',
              type: 'input',
              labelZh: '学校',
              labelEn: 'School',
              required: true,
              unit: 1,
              value: '北京大学'
            },
            {
              id: 'sf1_f2',
              code: 'degree',
              name: '学位',
              type: 'select',
              labelZh: '学位',
              labelEn: 'Degree',
              required: true,
              unit: 1,
              value: 'bachelor',
              options: [
                { labelZh: '学士', labelEn: 'Bachelor', value: 'bachelor' },
                { labelZh: '硕士', labelEn: 'Master', value: 'master' },
                { labelZh: '博士', labelEn: 'PhD', value: 'phd' }
              ]
            },
            {
              id: 'sf1_f3',
              code: 'graduation_date',
              name: '毕业日期',
              type: 'date',
              labelZh: '毕业日期',
              labelEn: 'Graduation Date',
              required: false,
              unit: 1,
              value: '2020-07-01'
            }
          ]
        },
        {
          id: 'msf1',
          code: 'work_experience',
          name: '工作经历',
          type: 'multiSubForm',
          labelZh: '工作经历',
          labelEn: 'Work Experience',
          required: false,
          unit: 3,
          fields: [
            {
              id: 'msf1_f1',
              code: 'company',
              name: '公司',
              type: 'input',
              labelZh: '公司',
              labelEn: 'Company',
              required: true,
              unit: 1
            },
            {
              id: 'msf1_f2',
              code: 'position',
              name: '职位',
              type: 'input',
              labelZh: '职位',
              labelEn: 'Position',
              required: true,
              unit: 1
            },
            {
              id: 'msf1_f3',
              code: 'period',
              name: '时间段',
              type: 'date_range',
              labelZh: '时间段',
              labelEn: 'Period',
              required: false,
              unit: 1,
              extends: {
                format: 'yyyy/MM/dd'
              }
            }
          ],
          value: {
            rows: [
              {
                company: '示例公司1',
                position: '软件工程师',
                period: { start: '2020-08-01', end: '2022-06-30' }
              },
              {
                company: '示例公司2',
                position: '高级工程师',
                period: { start: '2022-07-01', end: '' }
              }
            ]
          }
        }
      ]
    }
  ]
})

// 处理字段更新
function onFieldUpdate(field: any) {
  console.log('字段更新:', field)
}

// 组件挂载时的处理
onMounted(() => {
  // 可以在这里加载表单数据
  console.log('表单查看器已加载')
})
</script>

<style scoped>
.form-viewer {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.form-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.form-viewer-header h2 {
  margin: 0;
  font-size: 20px;
  color: #303133;
}

.form-viewer-actions {
  display: flex;
  gap: 10px;
}

.form-viewer-content {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
