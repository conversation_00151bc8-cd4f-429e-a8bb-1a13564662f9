<template>
  <div class="form-canvas" :class="{ 'design-mode': renderMode === 'design' }">
    <!-- 表单内容 -->
    <el-form-item
        label="填写语言/Language"
        style="padding-left: 180px"
        v-if="renderMode === 'edit' || renderMode === 'add'"
    >
      <el-radio-group v-model="formSchema.language">
        <el-radio value="both">中文和英文/Chinese And English</el-radio>
        <el-radio value="en">仅英文/English Only</el-radio>
      </el-radio-group>
    </el-form-item>
    <!-- 表单内容为空时的提示 -->
    <div v-if="formSchema.groups.length === 0" class="empty-root-tip">
      {{ "暂无表单内容" }}
    </div>
    <template v-for="(group, idx) in formSchema.groups" :key="group.id">
      <GroupContainer
          :group="group"
          :groups="formSchema.groups"
          :formData="formSchema.formData"
          :index="idx"
          :total-groups="formSchema.groups.length"
          :render-mode="renderMode"
          :language="formSchema.language as 'zh' | 'en' | 'both'"
          @update-value="onUpdateValue"
      />
    </template>

    <!-- 设计模式下的最后一个插入占位符 -->
    <!-- 已移除底部插入分组占位符 -->
  </div>
</template>

<script setup lang="ts">
import {defineProps, defineExpose} from "vue";
import GroupContainer from "./GroupContainer.vue";
import {type RenderMode} from "@/components/dynamic-form/form-fields/index";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";
import {fieldTypeMap} from '@/components/dynamic-form/form-fields/index';
import { dslFuncs } from '@/utils/dsl-funcs';

const props = defineProps({
  formSchema: {
    type: Object as () => FormDefinitionDto,
    required: true,
    default: () => ({groups: []}),
  },
  renderMode: {
    type: String as () => RenderMode,
    default: "design",
  },
});

function normalizeDate(val: any) {
  if (typeof val === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(val)) {
    return Date.parse(val);
  }
  if (val instanceof Date) return val.getTime();
  return val;
}

function normalizeByType(val: any, type: string): any {
  if (type === 'date') {
    if (typeof val === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(val)) {
      return Date.parse(val);
    }
    if (val instanceof Date) return val.getTime();
    if (typeof val === 'number') return val; // 已经是时间戳
  }
  if (type === 'int') return parseInt(val as string, 10);
  if (type === 'float') return parseFloat(val as string);
  if (type === 'boolean') return Boolean(val);
  // 可扩展更多类型
  return val;
}

function evalValidationExpr(
  expr: any,
  context: Record<string, any>,
  funcs: Record<string, (...args: any[]) => any>
): any {
  if (typeof expr === 'boolean' || typeof expr === 'number' || typeof expr === 'string') return expr;
  if (Array.isArray(expr)) throw new Error('不支持数组表达式');
  if ('if' in expr) {
    return evalValidationExpr(expr.if, context, funcs)
        ? evalValidationExpr(expr.then, context, funcs)
        : evalValidationExpr(expr.else, context, funcs);
  }
  if ('and' in expr) {
    return (expr.and as any[]).every((sub: any) => evalValidationExpr(sub, context, funcs));
  }
  if ('or' in expr) {
    return (expr.or as any[]).some((sub: any) => evalValidationExpr(sub, context, funcs));
  }
  if ('not' in expr) {
    return !evalValidationExpr(expr.not, context, funcs);
  }
  if ('func' in expr) {
    const fn = funcs[expr.func];
    if (!fn) throw new Error(`未注册函数: ${expr.func}`);
    const args = (expr.args || []).map((arg: any) => evalValidationExpr(arg, context, funcs));
    return fn(...args, context);
  }
  for (const op of ['==', '!=', '>', '>=', '<', '<=']) {
    if (op in expr) {
      const [left, right] = expr[op];
      const l = evalValidationExpr(left, context, funcs);
      const r = evalValidationExpr(right, context, funcs);
      switch (op) {
        case '==':
          return l === r;
        case '!=':
          return l !== r;
        case '>':
          return l > r;
        case '>=':
          return l >= r;
        case '<':
          return l < r;
        case '<=':
          return l <= r;
      }
    }
  }
  throw new Error('未知表达式');
}

function evalValidationExprWithError(
  language: string,
  expr: any,
  context: Record<string, any>,
  funcs: Record<string, (...args: any[]) => any>,
  rule: any,
  meta: any,
  parent: any = null,
  rowIndex: any = null,
  labelZh: string = '',
  labelEn: string = ''
): { ok: any; errors: any[] } {
  const errors: any[] = [];
  // property 节点
  if (expr && typeof expr === 'object' && 'property' in expr) {
    let val = context;
    for (const key of expr.property as string[]) {
      if (val == null) break;
      val = val[key];
    }
    if ('type' in expr) {
      val = normalizeByType(val, expr.type);
    }
    return { ok: val, errors: [] };
  }
  // const 节点
  if (expr && typeof expr === 'object' && 'const' in expr) {
    let val = expr.const;
    if ('type' in expr) {
      val = normalizeByType(val, expr.type);
    }
    return { ok: val, errors: [] };
  }
  // func 节点
  if (expr && typeof expr === 'object' && 'func' in expr) {
    const fn = funcs[expr.func];
    if (!fn) throw new Error(`未注册函数: ${expr.func}`);
    const args = (expr.args || []).map((arg: any) => evalValidationExprWithError(language, arg, context, funcs, rule, meta, parent, rowIndex, labelZh, labelEn).ok);
    const ok = fn(...args, context);
    if (!ok) {
      errors.push({
        code: rule.code,
        message: expr.message || ((language == 'both' || language == 'zh') ? rule.message?.zh : rule.message?.en) || rule.message || '校验未通过',
        parent,
        rowIndex,
        labelZh,
        labelEn
      });
    }
    return { ok, errors };
  }
  // if/then/else
  if (expr && typeof expr === 'object' && 'if' in expr) {
    const cond = evalValidationExprWithError(language, expr.if, context, funcs, rule, meta, parent, rowIndex, labelZh, labelEn);
    if (cond.ok) {
      return evalValidationExprWithError(language, expr.then, context, funcs, rule, meta, parent, rowIndex, labelZh, labelEn);
    } else {
      return evalValidationExprWithError(language, expr.else, context, funcs, rule, meta, parent, rowIndex, labelZh, labelEn);
    }
  }
  // and
  if (expr && typeof expr === 'object' && 'and' in expr) {
    let all = true;
    (expr.and as any[]).forEach((sub: any) => {
      const res = evalValidationExprWithError(language, sub, context, funcs, rule, meta, parent, rowIndex, labelZh, labelEn);
      if (!res.ok) {
        all = false;
        errors.push(...res.errors);
      }
    });
    if (!all && expr.message) {
      errors.push({ code: rule.code, message: expr.message, parent, rowIndex, labelZh, labelEn });
    }
    return { ok: all, errors };
  }
  // or
  if (expr && typeof expr === 'object' && 'or' in expr) {
    let any = false;
    let subErrors: any[] = [];
    (expr.or as any[]).forEach((sub: any) => {
      const res = evalValidationExprWithError(language, sub, context, funcs, rule, meta, parent, rowIndex, labelZh, labelEn);
      if (res.ok) any = true;
      else subErrors.push(...res.errors);
    });
    if (!any) {
      if (expr.message) {
        errors.push({ code: rule.code, message: expr.message, parent, rowIndex, labelZh, labelEn });
      } else {
        errors.push(...subErrors);
      }
    }
    return { ok: any, errors };
  }
  // not
  if (expr && typeof expr === 'object' && 'not' in expr) {
    const res = evalValidationExprWithError(language, expr.not, context, funcs, rule, meta, parent, rowIndex, labelZh, labelEn);
    if (res.ok) {
      return {
        ok: false,
        errors: [{
          code: rule.code,
          message: expr.message || ((language == 'both' || language == 'zh') ? rule.message?.zh : rule.message?.en) || rule.message || '校验未通过',
          parent,
          rowIndex,
          labelZh,
          labelEn
        }]
      };
    } else {
      return { ok: true, errors: [] };
    }
  }
  // 比较操作符
  for (const op of ['==', '!=', '>', '>=', '<', '<=']) {
    if (expr && typeof expr === 'object' && op in expr) {
      const [left, right]: [any, any] = expr[op];
      let l = evalValidationExprWithError(language, left, context, funcs, rule, meta, parent, rowIndex, labelZh, labelEn).ok;
      let r = evalValidationExprWithError(language, right, context, funcs, rule, meta, parent, rowIndex, labelZh, labelEn).ok;
      let ok = false;
      switch (op) {
        case '==': ok = l === r; break;
        case '!=': ok = l !== r; break;
        case '>': ok = l > r; break;
        case '>=': ok = l >= r; break;
        case '<': ok = l < r; break;
        case '<=': ok = l <= r; break;
      }
      if (!ok) {
        errors.push({
          code: rule.code,
          message: expr.message || ((language == 'both' || language == 'zh') ? rule.message?.zh : rule.message?.en) || rule.message || '校验未通过',
          parent,
          rowIndex,
          labelZh,
          labelEn
        });
      }
      return { ok, errors };
    }
  }
  // 直接量
  if (typeof expr === 'boolean' || typeof expr === 'number' || typeof expr === 'string') return { ok: expr, errors };
  // 未知表达式
  console.warn('[DSL校验] 未知表达式', expr, typeof expr);
  throw new Error('未知表达式');
}

function getValidationFuncs() {
  return dslFuncs;
}

function buildContextFromSchema(formSchema: any) {
  const context: Record<string, any> = {};
  const fieldMeta: Record<string, any> = {};

  function collectField(field: any, parent: any = null, rowIndex: number | null = null) {
    context[field.code] = field.value;
    fieldMeta[field.code] = {
      parent,
      rowIndex,
      labelZh: field.labelZh,
      labelEn: field.labelEn,
      field
    };
    // 处理子表单
    if (Array.isArray(field.value) && Array.isArray(field.fields)) {
      // 子表单本身code存的是数组
      field.value.forEach((row: any, idx: number) => {
        field.fields.forEach((child: any) => {
          // 这里child.value是单元格的值
          // 但context里child.code会被覆盖为最后一行的值，
          // 所以建议DSL里用parent code（如subList）+ every/any处理
          // 但meta里可以记录parent/rowIndex
          fieldMeta[child.code + '__' + idx] = {
            parent: field,
            rowIndex: idx,
            labelZh: child.labelZh,
            labelEn: child.labelEn,
            field: child
          };
        });
      });
    }
    // 嵌套字段
    if (Array.isArray(field.fields)) {
      field.fields.forEach((child: any) => {
        collectField(child, field, null);
      });
    }
  }

  formSchema.groups.forEach((group: any) => {
    if (Array.isArray(group.fields)) {
      group.fields.forEach((field: any) => {
        collectField(field, null, null);
      });
    }
  });
  console.log('[DSL调试] context', context);
  return {context, fieldMeta};
}

function validateAll() {
  const result: any[] = [];

  function validateField(language: string, parent: any, field: any, value: any, rowIndex: number | null) {
    const type = field.type;
    const logic = fieldTypeMap[type];
    if (logic && typeof logic.validate === 'function' && (field.visible ?? true)) {
      const err = logic.validate(value, field, language || 'zh');
      if (rowIndex == null) {
        field.errorMsg = err;
      } else {
        if (!field.errorMsgMap) field.errorMsgMap = {};
        field.errorMsgMap[rowIndex] = err;
      }
      result.push({
        code: field.code,
        parent: parent,
        rowIndex: rowIndex,
        labelZh: field.labelZh,
        labelEn: field.labelEn,
        message: err
      });
    }
    if (Array.isArray(field.value) && (field.visible ?? true)) {
      field.value.forEach((row: any, idx: number) => {
        field.fields.forEach((child: any) => {
          const data = row != null && row.hasOwnProperty(child.code) ? row[child.code] : null;
          validateField(props.formSchema.language, field, child, data, idx);
        });
      });
    }
  }

  props.formSchema.groups.forEach((group: any) => {
    if (Array.isArray(group.fields)) {
      group.fields.forEach((q: any) => {
        validateField(props.formSchema.language, null, q, q.value, null)
      });
    }
  });

  // 合并jsonConfig.validation规则校验
  const jsonConfig = (props.formSchema as any) && (props.formSchema as any).jsonConfig;
  if (jsonConfig && Array.isArray(jsonConfig.validation)) {
    const {context, fieldMeta} = buildContextFromSchema(props.formSchema);
    const funcs = getValidationFuncs();
    jsonConfig.validation.forEach((rule: any) => {
      try {
        const res = evalValidationExprWithError(props.formSchema.language, rule.expression, context, funcs, rule, fieldMeta, null, null, '', '');
        if (!res.ok && res.errors.length) {
          res.errors.forEach((err: any) => {
            // 修正labelZh/labelEn
            let labelZhFinal = err.labelZh;
            let labelEnFinal = err.labelEn;
            if ((!labelZhFinal || !labelEnFinal) && rule.triggers && rule.triggers.length > 0 && fieldMeta) {
              const code = rule.triggers[0];
              let metaObj = null;
              if (typeof err?.rowIndex === 'number' && fieldMeta[code + '__' + err.rowIndex]) {
                metaObj = fieldMeta[code + '__' + err.rowIndex];
              } else if (fieldMeta[code]) {
                metaObj = fieldMeta[code];
              }
              if (metaObj) {
                if (!labelZhFinal) labelZhFinal = metaObj.labelZh;
                if (!labelEnFinal) labelEnFinal = metaObj.labelEn;
              }
            }
            result.push({
              ...err,
              labelZh: labelZhFinal,
              labelEn: labelEnFinal
            });
            // 设置控件errorMsg/Map逻辑保持不变
            if (Array.isArray(rule.triggers)) {
              rule.triggers.forEach((code: any) => {
                let fieldObj = null;
                if (err.rowIndex != null && fieldMeta[code + '__' + err.rowIndex]) {
                  fieldObj = fieldMeta[code + '__' + err.rowIndex].field;
                  if (fieldObj) {
                    if (!fieldObj.errorMsgMap) fieldObj.errorMsgMap = {};
                    fieldObj.errorMsgMap[err.rowIndex] = err.message;
                  }
                } else if (fieldMeta[code]) {
                  fieldObj = fieldMeta[code].field;
                  if (fieldObj) {
                    fieldObj.errorMsg = err.message;
                  }
                }
              });
            }
          });
        }
      } catch (e: any) {
        result.push({
          code: rule.code,
          parent: null,
          rowIndex: null,
          labelZh: '',
          labelEn: '',
          message: `校验异常: ${e.message}`
        });
      }
    });
  }

  const hasError = result.some((q: any) => q.message);
  return {
    valid: !hasError,
    messages: result
  };
}

watch(() => props.formSchema, (newVal: any) => {
  if (!newVal?.jsonConfig) return

  newVal.jsonConfig.behavior ??= []
  const behaviorFields = newVal.jsonConfig.behavior.map((q: any) => q.code)

  behaviorFields.forEach((def: any) => {
    recursiveField([def], (field: any) => {
      handleBehavior(field)
    })
  })
}, {immediate: true, deep: false})

function onUpdateValue(field: any) {
  console.log("formCanvas收到updateValue：", field);

  handleBehavior(field);
}

function handleBehavior(field: any) {
  (props.formSchema as any).jsonConfig ??= {};
  (props.formSchema as any).jsonConfig.behavior ??= [];

  const finder = (props.formSchema as any).jsonConfig.behavior.find((q: any) => q.code == field.code);

  if (finder == null) {
    return;
  }

  console.log(finder);

  finder.showFields.forEach((behavior: any) => {
    handleShowFieldBehavior(field, behavior);
  })

  finder.hideFields.forEach((behavior: any) => {
    handleHideFieldBehavior(field, behavior);
  })
}

function getTargetValue(field: any, fieldShowFieldBehavior: any) {
  const property = fieldShowFieldBehavior.property;
  let targetValue = "";
  if (property) {
    targetValue = field.value[property]
  } else {
    targetValue = field.value;
  }

  return targetValue;
}

function getTargetValueMatch(targetValue: any, condition: any) {
  let matched = false;
  if (condition.equals && condition.equals == targetValue) {
    matched = true;
  } else if (condition.notEquals && condition.notEquals != targetValue) {
    matched = true;
  }

  return matched;
}

function handleShowFieldBehavior(field: any, fieldShowFieldBehavior: any) {
  let targetValue = getTargetValue(field, fieldShowFieldBehavior);

  let matched = getTargetValueMatch(targetValue, fieldShowFieldBehavior.condition);

  // console.log("target value is ", targetValue);
  // console.log("matched is ", matched);

  if (matched) {
    recursiveField(fieldShowFieldBehavior.fields, (lt) => {
      lt.visible = true;
    });
  }
}

function handleHideFieldBehavior(field: any, fieldHideFieldBehavior: any) {

  let targetValue = getTargetValue(field, fieldHideFieldBehavior);

  let matched = getTargetValueMatch(targetValue, fieldHideFieldBehavior.condition);

  // console.log("target value is ", targetValue);
  // console.log("matched is ", matched);

  if (matched) {
    recursiveField(fieldHideFieldBehavior.fields, (lt) => {
      lt.visible = false;
    });
  }
}

function recursiveField(matchCodes: string[], callback: (field: any) => void) {
  props.formSchema.groups.forEach((group: any) => {
    group.fields?.forEach((field: any) => {
      if (matchCodes.includes(field.code)) {
        callback(field);
      }
      field.fields?.forEach((child: any) => {
        if (matchCodes.includes(child.code)) {
          callback(child);
        }
      })
    })
  })
}

defineExpose({validateAll});
</script>

<style scoped>
.form-canvas {
  min-height: 200px;
  position: relative;
  width: 100%;
}

/* 设计模式样式 */
.form-canvas.design-mode {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 8px 0 0 0;
  transition: box-shadow 0.2s, border-color 0.2s;
  border: 1px solid #ebeef5;
}

/* 编辑模式样式 */
.form-canvas:not(.design-mode) {
  background: #fff;
  padding: 16px 0;
}

.empty-root-tip {
  color: #909399;
  background: #f8f8fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  padding: 32px;
  text-align: center;
  margin: 32px 24px;
  font-size: 16px;
}

.group-insert-placeholder {
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  background: #f8f8fa;
  border: 2px dashed transparent;
  border-radius: 6px;
  margin: 8px 0;
  font-size: 14px;
  transition: border-color 0.2s, background 0.2s;
}

.group-insert-placeholder:hover {
  border-color: #c0c4cc;
  background: #f5f7fa;
}
</style>
