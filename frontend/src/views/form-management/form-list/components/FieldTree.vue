<template>
  <div class="field-tree">
    <div class="field-tree-title">控件库</div>
    <div class="field-tree-list">
      <el-card
        v-for="item in fieldTypes"
        :key="(item as any).type"
        class="field-tree-card"
        :class="{
          'dragging': draggingType === (item as any).type,
          'disabled': isDisabled((item as any).type)
        }"
        shadow="hover"
        :body-style="{ padding: '0' }"
      >
        <el-tooltip
          v-if="isDisabled((item as any).type)"
          content="子表单或多行子表单中不能嵌套其他子表单"
          placement="right"
        >
          <el-button
            class="field-tree-btn"
            :draggable="!isDisabled((item as any).type)"
            @dragstart="(e: DragEvent) => onDragStart((item as any).type, e)"
            @dragend="(e: DragEvent) => onDragEnd((item as any).type, e)"
            type="default"
            size="small"
            style="width: 100%;"
            :disabled="isDisabled((item as any).type)"
          >
            {{ (item as any).label }}
          </el-button>
        </el-tooltip>
        <el-button
          v-else
          class="field-tree-btn"
          draggable="true"
          @dragstart="(e: DragEvent) => onDragStart((item as any).type, e)"
          @dragend="(e: DragEvent) => onDragEnd((item as any).type, e)"
          type="default"
          size="small"
          style="width: 100%;"
        >
          {{ (item as any).label }}
        </el-button>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import { ElButton, ElCard, ElTooltip } from 'element-plus'

const props = defineProps({
  fieldTypes: {
    type: Array,
    required: true
  },
  currentContainerType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['dragstart'])

const draggingType = ref<string | null>(null)

// 判断控件类型是否应该被禁用
function isDisabled(type: string): boolean {
  // 如果当前容器是子表单或多行子表单，则禁用子表单和多行子表单控件
  return (props.currentContainerType === 'subForm' || props.currentContainerType === 'multiSubForm') &&
         (type === 'subForm' || type === 'multiSubForm');
}

function onDragStart(type: string, e: DragEvent) {
  // 如果控件被禁用，阻止拖拽
  if (isDisabled(type)) {
    e.preventDefault();
    return;
  }

  if (e && e.dataTransfer) {
    e.dataTransfer.setData('fieldType', type)
    e.dataTransfer.effectAllowed = 'copyMove'
  }
  draggingType.value = type
  if (type === 'group') {
    // @ts-ignore
    window.__draggingGroup = true
  }
  emit('dragstart', type)
}

function onDragEnd(type: string, e: DragEvent) {
  // 参数 e 未使用，但保留以符合事件处理函数签名
  draggingType.value = null
  if (type === 'group') {
    // @ts-ignore
    window.__draggingGroup = false
  }
}
</script>

<style scoped>
.field-tree {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: #f7f8fa;
  border-radius: 12px;
  border: 1.5px solid #e4e7ed;
  padding: 20px 10px 16px 10px;
  margin: 0 8px;
  min-width: 180px;
  box-shadow: 0 2px 12px rgba(64,158,255,0.08);
}
.field-tree-title {
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 12px;
  letter-spacing: 1px;
  text-align: center;
}
.field-tree-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.field-tree-card {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(64,158,255,0.04);
  transition: box-shadow 0.2s;
}
.field-tree-card.dragging {
  opacity: 0.5;
  border: 1.5px dashed #409eff;
}

.field-tree-card.disabled {
  opacity: 0.5;
}
.field-tree-btn.el-button {
  border-radius: 8px !important;
  background: #fff;
  color: #333;
  font-weight: 500;
  padding: 0 0;
  margin: 0;
  text-align: center;
  width: 100%;
  min-width: 120px;
  transition: background 0.2s, color 0.2s, opacity 0.2s;
}
.field-tree-btn.el-button:hover {
  background: #ecf5ff;
  color: #409eff;
  border-color: #b3d8ff;
}
</style>