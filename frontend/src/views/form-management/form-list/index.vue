<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      title="表单列表"
      :data="formList"
      :columns="tableColumns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #toolbar>
        <el-button
          type="primary"
          @click="handleAdd"
          v-hasPermission="['form:defined:add']"
        >
          <el-icon><Plus /></el-icon>
          新增表单
        </el-button>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button
          type="primary"
          link
          @click="handleEdit(row)"
          v-hasPermission="['form:defined:edit']"
        >
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-button
          type="primary"
          link
          @click="handleDesign(row)"
          v-hasPermission="['form:defined:design']"
        >
          <el-icon><List /></el-icon>
          表单设计
        </el-button>
        <el-button
          type="danger"
          link
          @click="handleDelete(row)"
          v-hasPermission="['form:defined:delete']"
        >
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </template>
    </DataTable>

    <!-- 表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="表单编码" prop="code">
          <el-input
            v-model="form.code"
            placeholder="请输入表单编码"
            :readonly="dialogType === 'edit'"
            :disabled="dialogType === 'edit'"
          />
        </el-form-item>
        <el-form-item label="表单名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入表单名称"
          />
        </el-form-item>
        <el-form-item label="表单描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入表单描述"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { Plus, Edit, Delete, List } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import { useRouter } from "vue-router";
import SearchForm, { FormItem } from "@/components/common/SearchForm.vue";
import DataTable, { TableColumn } from "@/components/common/DataTable.vue";
import {
  getFormPage,
  createForm,
  updateForm,
  deleteForm
} from "@/api/dynamic-form-mgt";
import { FormDto, FormOperationDto, FormQueryParams } from "@/dtos/dynamic-form-mgt.dto";

const router = useRouter();
const loading = ref(false);
const total = ref(0);
const formList = ref<FormDto[]>([]);
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");
const formRef = ref<FormInstance>();
const currentEditId = ref("");

// 查询参数
const queryParams = reactive<FormQueryParams>({
  $pageIndex: 1,
  $pageSize: 10,
  name: "",
  code: ""
});

// 表单对象
const form = reactive<FormOperationDto>({
  code: "",
  name: "",
  description: ""
});

// 搜索表单项
const searchFormItems = ref<FormItem[]>([
  {
    field: "name",
    label: "表单名称",
    component: "Input",
    componentProps: {
      placeholder: "请输入表单名称"
    }
  },
  {
    field: "code",
    label: "表单编码",
    component: "Input",
    componentProps: {
      placeholder: "请输入表单编码"
    }
  }
]);

// 表格列配置
const tableColumns = ref<TableColumn[]>([
  {
    prop: "name",
    label: "表单名称",
    width: "180"
  },
  {
    prop: "code",
    label: "表单编码",
    width: "180"
  },
  {
    prop: "description",
    label: "描述"
  },
  {
    prop: "version",
    label: "版本",
    width: "180"
  }
]);

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入表单名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入表单编码", trigger: "blur" },
    { pattern: /^[a-zA-Z0-9_]+$/, message: "编码只能包含字母、数字和下划线", trigger: "blur" }
  ]
});

const dialogTitle = computed(() => {
  return dialogType.value === "add" ? "新增表单" : "编辑表单";
});

onMounted(() => {
  getList();
});

const getList = async () => {
  try {
    loading.value = true;
    const response = await getFormPage(queryParams);
    console.log("表单列表数据:", response);

    const { data } = response;
    formList.value = data.rows || [];
    total.value = data.totals || 0;
  } catch (error) {
    console.error("获取表单列表失败", error);
    ElMessage.error("获取表单列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

const resetForm = () => {
  form.code = "";
  form.name = "";
  form.description = "";
  currentEditId.value = "";
};

const handleAdd = () => {
  resetForm();
  dialogType.value = "add";
  dialogVisible.value = true;
};

const handleEdit = (row: FormDto) => {
  resetForm();
  dialogType.value = "edit";
  dialogVisible.value = true;

  // 将行数据复制到表单
  form.code = row.code || "";
  form.name = row.name || "";
  form.description = row.description || "";

  // 保存当前编辑的表单ID
  currentEditId.value = row.key;
};

// 处理表单设计
const handleDesign = (row: FormDto) => {
  // 跳转到表单设计器页面，并传递表单ID作为路由参数
  router.push({
    path: `/form-management/form-designer/${row.code}`
  });
};

const handleDelete = async (row: FormDto) => {
  try {
    await ElMessageBox.confirm("确认要删除该表单吗？", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    await deleteForm(row.key);
    ElMessage.success("删除成功");
    getList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除表单失败:", error);
      ElMessage.error("删除表单失败");
    }
  }
};

const submitForm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    if (dialogType.value === "add") {
      await createForm(form);
      ElMessage.success("新增成功");
    } else {
      await updateForm(currentEditId.value, form);
      ElMessage.success("修改成功");
    }
    dialogVisible.value = false;
    getList();
  } catch (error) {
    console.error("提交表单失败:", error);
    ElMessage.error("提交表单失败");
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
