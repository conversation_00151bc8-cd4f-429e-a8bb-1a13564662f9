<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>{{ title }}</h1>
      <div class="dashboard-date">{{ currentDate }}</div>
    </div>

    <el-row>
      <el-col :span="18">
        <el-row :gutter="20">
          <el-col :span="6" v-if="roles.includes('RESEARCHER')">
            <el-card shadow="hover" class="dashboard-card" @click="router.push({ name: 'projectUserAllList' })">
              <template #header>
                <div class="card-header">
                  <span>我的项目</span>
                  <!-- <el-tag type="success">增长</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['user-all-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#3164a0"/>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6" v-if="roles.includes('RESEARCHER')">
            <el-card shadow="hover" class="dashboard-card" @click="router.push({ name: 'projectUserPendingSubmit' })">
              <template #header>
                <div class="card-header">
                  <span>待提交项目</span>
                  <!-- <el-tag type="success">增长</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['user-pending-submit'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#3164a0"/>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6" v-if="roles.includes('RESEARCHER')">
            <el-card shadow="hover" class="dashboard-card" @click="router.push({ name: 'projectUserPendingApproval' })">
              <template #header>
                <div class="card-header">
                  <span>待审核项目</span>
                  <!-- <el-tag type="success">增长</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['user-pending-approval-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#3164a0"/>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6" v-if="roles.includes('RESEARCHER')">
            <el-card shadow="hover" class="dashboard-card" @click="router.push({ name: 'projectUserApprovedList' })">
              <template #header>
                <div class="card-header">
                  <span>已通过项目</span>
                  <!-- <el-tag type="success">增长</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['user-approved-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#3164a0"/>
                </div>
              </div>
            </el-card>
          </el-col>


          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_1')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingJudgeList' })">
              <template #header>
                <div class="card-header">
                  <span>待判断项目</span>
                  <!-- <el-tag type="success">增长</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-judge-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#3164a0"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_1')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingSendNumberList' })">
              <template #header>
                <div class="card-header">
                  <span>待发号项目</span>
                  <!-- <el-tag type="success">增长</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-send-number-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#3164a0"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_1')">
            <el-card shadow="hover" class="dashboard-card" @click="router.push({ name: 'projectSystemApplyEditList' })">
              <template #header>
                <div class="card-header">
                  <span>再修改申请项目</span>
                  <!-- <el-tag type="warning">平稳</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['apply-edit-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#E6A23C"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_1')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingReviewList' })">
              <template #header>
                <div class="card-header">
                  <span>再修改复核项目</span>
                  <!-- <el-tag type="warning">平稳</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-review-list-1'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#E6A23C"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_1')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemReturnEditList' })">
              <template #header>
                <div class="card-header">
                  <span>再修改退回</span>
                  <!-- <el-tag type="danger">紧急</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['return-edit-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#F56C6C"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_1')">
            <el-card shadow="hover" class="dashboard-card" @click="router.push({ name: 'projectSystemApprovedList' })">
              <template #header>
                <div class="card-header">
                  <span>已发号项目</span>
                  <!-- <el-tag type="danger">紧急</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['approved-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#F56C6C"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_1')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemNonTraditionalList' })">
              <template #header>
                <div class="card-header">
                  <span>非传统医学项目</span>
                  <!-- <el-tag type="info">正常</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['non-traditional-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#909399"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_1')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemAllSubmittedList' })">
              <template #header>
                <div class="card-header">
                  <span>审核状态查询</span>
                  <!-- <el-tag type="info">正常</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['all-submitted-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#909399"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_2')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingAssignList' })">
              <template #header>
                <div class="card-header">
                  <span>待分配项目</span>
                  <!-- <el-tag type="info">正常</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-assign-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#909399"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_2')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingReviewList2' })">
              <template #header>
                <div class="card-header">
                  <span>待核审项目</span>
                  <!-- <el-tag type="success">增长</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-review-list-2'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#3164a0"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_2')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemReviewReturnedList2' })">
              <template #header>
                <div class="card-header">
                  <span>已退回项目</span>
                  <!-- <el-tag type="warning">平稳</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['review-returned-list-2'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#E6A23C"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_2')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingApprovedList2' })">
              <template #header>
                <div class="card-header">
                  <span>已核审通过项目</span>
                  <!-- <el-tag type="danger">紧急</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-approved-list-2'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#3164a0"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_2')">
            <el-card shadow="hover" class="dashboard-card" @click="router.push({ name: 'projectSystemApprovedList2' })">
              <template #header>
                <div class="card-header">
                  <span>已发号项目</span>
                  <!-- <el-tag type="danger">紧急</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['approved-list-2'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#F56C6C"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_2')">
            <el-card shadow="hover" class="dashboard-card" @click="router.push({ name: 'projectSystemReAssignList' })">
              <template #header>
                <div class="card-header">
                  <span>重新分配项目</span>
                  <!-- <el-tag type="info">正常</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['re-assign-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#909399"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_3')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingAssignReviewList' })">
              <template #header>
                <div class="card-header">
                  <span>待分配项目</span>
                  <!-- <el-tag type="success">增长</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-assign-review-list'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#3164a0"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_3')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingReviewList3' })">
              <template #header>
                <div class="card-header">
                  <span>待复审项目</span>
                  <!-- <el-tag type="warning">平稳</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-review-list-3'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#E6A23C"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_3')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingApprovedList3' })">
              <template #header>
                <div class="card-header">
                  <span>已复审通过项目</span>
                  <!-- <el-tag type="info">正常</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-approved-list-3'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#909399"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_3')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemReviewReturnedList3' })">
              <template #header>
                <div class="card-header">
                  <span>已退回项目</span>
                  <!-- <el-tag type="danger">紧急</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['review-returned-list-3'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#F56C6C"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_3')">
            <el-card shadow="hover" class="dashboard-card" @click="router.push({ name: 'projectSystemApprovedList3' })">
              <template #header>
                <div class="card-header">
                  <span>已发号项目</span>
                  <!-- <el-tag type="info">正常</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['approved-list-3'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#909399"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_4')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingReviewList4' })">
              <template #header>
                <div class="card-header">
                  <span>待初审项目</span>
                  <!-- <el-tag type="success">增长</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-review-list-4'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#3164a0"/>
                </div>
              </div>
            </el-card>
          </el-col>


          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_4')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemPendingApprovedList4' })">
              <template #header>
                <div class="card-header">
                  <span>已初审通过项目</span>
                  <!-- <el-tag type="danger">紧急</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['pending-approved-list-4'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#F56C6C"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_4')">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: 'projectSystemReviewReturnedList4' })">
              <template #header>
                <div class="card-header">
                  <span>已退回项目</span>
                  <!-- <el-tag type="warning">平稳</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['review-returned-list-4'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" color="#E6A23C"/>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6" v-if="positions.includes('CHECKER_LEVEL_4')">
            <el-card shadow="hover" class="dashboard-card" @click="router.push({ name: 'projectSystemApprovedList4' })">
              <template #header>
                <div class="card-header">
                  <span>已发号项目</span>
                  <!-- <el-tag type="danger">紧急</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ projectCountData['approved-list-4'] ?? 0 }}</div>
                <div class="card-chart">
                  <el-progress :percentage="100" :show-text="false" color="#F56C6C"/>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card" style="margin-left:20px">
          <template #header>
            <div class="card-header">
              <span>快速导航/Fast Navigation</span>
            </div>
          </template>
          <div class="quick-nav">


            <template
                v-for="link in links"
                :key="link.name">

              <el-button :type="link.type" plain @click="handleGoto(link)">
                {{ link.title }}
              </el-button>

            </template>


          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, watch} from 'vue'
import {IndexProjectCountRequestDto} from "@/dtos/itmctr";
import {getIndexProjectCount, test, getTrialSearchPage} from "@/api/itmctr";
import {useUserStore} from "@/stores/user";
import {useRouter} from 'vue-router'
import {getPositions} from "@/utils/auth";

const router = useRouter()
// 获取当前日期
const currentDate = ref(new Date().toLocaleDateString('zh-CN', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  weekday: 'long'
}))
const userStore = useUserStore();


const positions = userStore.positions;

const roles = userStore.roles;

function handleGoto(link: any) {
  if (link.open) {
    // 如果链接是打开的，直接跳转到外部链接
    window.open(router.resolve({name: link.name}).href, '_blank', 'noopener');
    return;
  } else {
    // 跳转到指定的路由
    router.push({name: link.name});
  }
}

const links = computed(() => {


  if (roles.includes("RESEARCHER")) {
    return [
      {
        name: 'searchIndex',
        title: '项目检索',
        type: 'primary',
        open: true
      }

    ];
  } else if (roles.includes("CHECKER") || positions.includes("CHECKER_LEVEL_1") || positions.includes("CHECKER_LEVEL_2") || positions.includes("CHECKER_LEVEL_3") || positions.includes("CHECKER_LEVEL_4")) {
    return [{
      name: 'searchIndex',
      title: '项目检索',
      type: 'primary',
      open: true
    }, {
      name: 'reviewRule',
      title: '初级临床试验审核工作规程',
      type: 'success',
      open: true
    }];


  } else {
    return [];
  }
});

// console.log(userStore.roles);
const title = computed(() => {
  if (roles.includes("RESEARCHER")) {
    return "项目中心";
  } else if (roles.includes("CHECKER") || positions.includes("CHECKER_LEVEL_1") || positions.includes("CHECKER_LEVEL_2") || positions.includes("CHECKER_LEVEL_3") || positions.includes("CHECKER_LEVEL_4")) {
    return "项目审核";
  } else {
    return "仪表盘";
  }
});


const projectCountData = ref<Record<string, number>>({});
onMounted(() => {
  fetchGetIndexProjectCount();
  testtest();
  fetchTrialSearchPage();
});

// 获取项目统计数据
async function fetchGetIndexProjectCount() {
  try {
    const params: IndexProjectCountRequestDto = {
      formCode: 'PROJECT',
      // PositionCodes: positions.value,
      // userId: userStore.userId ?? '',
      status: 1
    };
    const response = await getIndexProjectCount(params);
    projectCountData.value = response.data;
  } catch (error) {
    console.error('获取项目统计数据失败:', error);
  }
}

async function testtest() {
  try {
    const response = await test();
  } catch (error) {
    console.error('测试接口:', error);
  }
}

const queryParams = reactive<any>({
  $pageIndex: 1,
  $pageSize: 10,
  formCode: "PROJECT",
  isObsoleted: false,
  sponsor_institution: "zzz",
});

async function fetchTrialSearchPage() {
  try {
    const response = await getTrialSearchPage(queryParams);
  } catch (error) {
    console.error('获取项目统计数据失败:', error);
  }
}

</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      margin: 0;
      font-size: 24px;
      color: #303133;
    }

    .dashboard-date {
      font-size: 14px;
      color: #909399;
    }
  }

  .dashboard-row {
    margin-top: 20px;
  }

  .dashboard-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-content {
      padding: 10px 0;

      .card-value {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
      }
    }

    .welcome-content {
      display: flex;
      align-items: center;

      .welcome-logo {
        width: 80px;
        height: 80px;
        margin-right: 20px;
      }

      .welcome-text {
        h2 {
          margin-top: 0;
          margin-bottom: 10px;
          color: #303133;
        }

        p {
          margin: 5px 0;
          color: #606266;
        }
      }
    }

    .quick-nav {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .el-button {
        margin: 0;
      }
    }
  }
}
</style>