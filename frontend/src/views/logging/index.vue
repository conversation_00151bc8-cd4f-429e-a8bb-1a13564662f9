<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      :show-more-button="true"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      title="应用日志"
      :data="logList"
      :columns="tableColumns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handleCurrentChange"
      @size-change="handleSizeChange"
      :show-index="true"
      :action-width="200"
    >
      <!-- 日志级别列 -->
      <template #level="{ row }">
        <el-tag
          :type="getLevelTagType(row.level)"
        >
          {{ row.level }}
        </el-tag>
      </template>

      <!-- 来源 -->
      <template #sourceContext="{ row }">
        {{ row.sourceContext }}
      </template>
      <!-- 控制器 -->
      <template #controller="{ row }">
        {{ row.controller }}/{{ row.action }}
      </template>
      <!-- 时间戳列 -->
      <template #timestamp="{ row }">
        {{ formatDateTime(row.timestamp) }}
      </template>

      <!-- 消息列 -->
      <template #message="{ row }">
        <div class="message-cell">
          <span :title="row.message">{{ truncateText(row.message, 50) }}</span>
        </div>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button
          type="primary"
          link
          @click="handleViewDetail(row)"
        >
          <el-icon><View /></el-icon>
          查看详情
        </el-button>
      </template>
    </DataTable>

    <!-- 日志详情对话框 -->
    <el-dialog
      title="日志详情"
      v-model="detailDialogVisible"
      width="80%"
      append-to-body
      destroy-on-close
      class="log-detail-dialog"
    >
      <div v-if="currentLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="应用名称">
            {{ currentLog.application || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="日志级别">
            <el-tag :type="getLevelTagType(currentLog.level)">
              {{ currentLog.level || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="日志分类">
            {{ currentLog.category || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户端IP">
            {{ currentLog.clientIp || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="控制器">
            {{ currentLog.controller || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="动作/方法">
            {{ currentLog.action || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="路由模板">
            {{ currentLog.routeTemplate || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="当前用户">
            {{ currentLog.currentUser || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="关联ID">
            {{ currentLog.correlationId || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="来源">
            {{ currentLog.sourceContext || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="日志时间" :span="2">
            {{ formatDateTime(currentLog.timestamp) }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="log-details-container">
          <el-tabs v-model="activeTab" class="log-tabs">
            <el-tab-pane label="日志内容" name="message">
              <div v-if="isJsonContent(currentLog.message)" class="json-viewer">
                <div class="json-header">
                  <span class="json-label">JSON格式</span>
                  <el-button
                    size="small"
                    type="primary"
                    link
                    @click="toggleJsonView"
                  >
                    {{ showRawJson ? '格式化显示' : '原始文本' }}
                  </el-button>
                </div>
                <div v-if="showRawJson">
                  <el-input
                    v-model="currentLog.message"
                    type="textarea"
                    :rows="12"
                    readonly
                    class="log-content"
                    placeholder="暂无日志内容"
                  />
                </div>
                <div v-else class="json-display">
                  <pre class="json-formatted" v-html="formatJsonContent(currentLog.message)"></pre>
                </div>
              </div>
              <div v-else>
                <el-input
                  v-model="currentLog.message"
                  type="textarea"
                  :rows="12"
                  readonly
                  class="log-content"
                  placeholder="暂无日志内容"
                />
              </div>
            </el-tab-pane>
            <el-tab-pane
              v-if="currentLog.exception"
              label="异常信息"
              name="exception"
            >
              <el-input
                v-model="currentLog.exception"
                type="textarea"
                :rows="12"
                readonly
                class="exception-content"
                placeholder="暂无异常信息"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { View } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import SearchForm, {FormItem} from "@/components/common/SearchForm.vue";
import DataTable from "@/components/common/DataTable.vue";
import { getApplicationLogPage } from "@/api/logging-mgt";
import {
  ApplicationLogDto,
  ApplicationLogQueryParams,
} from "@/dtos/logging.dto";
import { appInfoList } from "@/config/app-info";

// 将 appCode 转换为对应的应用名称格式
const convertAppCodeToApplicationName = (appCode: string): string => {
  // 将连字符分隔的字符串转换为 PascalCase，并添加 WebApi 后缀
  return appCode
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('') + 'WebApi';
};

// 生成应用选项列表
const applicationOptions = computed(() => {
  return appInfoList.map(app => ({
    label: app.appName,
    value: convertAppCodeToApplicationName(app.appCode)
  }));
});

// 搜索表单项配置
const searchFormItems = ref<FormItem[]>([
  {
    type: "select",
    label: "应用名称",
    prop: "application",
    placeholder: "请选择应用",
    options: applicationOptions.value,
  },
  {
    type: "select",
    label: "日志级别",
    prop: "level",
    placeholder: "请选择日志级别",
    options: [
      { label: "Trace", value: "Trace" },
      { label: "Debug", value: "Debug" },
      { label: "Information", value: "Information" },
      { label: "Warning", value: "Warning" },
      { label: "Error", value: "Error" },
      { label: "Critical", value: "Critical" },
    ],
  },
  {
    type: "input",
    label: "日志分类",
    prop: "category",
    placeholder: "请输入日志分类",
    visible: false,
  },
  {
    type: "input",
    label: "客户端IP",
    prop: "clientIp",
    placeholder: "请输入客户端IP",
    visible: false,
  },
  {
    type: "input",
    label: "控制器",
    prop: "controller",
    placeholder: "请输入控制器名称",
    visible: false,
  },
  {
    type: "input",
    label: "动作/方法",
    prop: "action",
    placeholder: "请输入动作/方法名称",
    visible: false,
  },
  {
    type: "input",
    label: "当前用户",
    prop: "currentUser",
    placeholder: "请输入用户名",
    visible: false,
  },
  {
    type: "input",
    label: "关联ID",
    prop: "correlationId",
    placeholder: "请输入关联ID",
    visible: false,
  },
  {
    type: "input",
    label: "日志内容",
    prop: "message",
    placeholder: "请输入日志内容关键字",
    visible: false,
  },
]);

// 表格列配置
const tableColumns = computed(() => [
  { prop: "application", label: "应用名称", width: 120 },
  { prop: "level", label: "日志级别", slot: "level", width: 120 },
  { prop: "sourceContext", label: "来源", slot: "sourceContext", width: 180 },
  { prop: "category", label: "日志分类", width: 180 },
  { prop: "clientIp", label: "客户端IP", width: 120 },
  { prop: "controller", label: "控制器", width: 150 , slot: "controller"},
  { prop: "currentUser", label: "当前用户", width: 120 },
  { prop: "timestamp", label: "日志时间", slot: "timestamp", width: 180 },
  { prop: "message", label: "日志内容", slot: "message", width: 300 },
]);

const loading = ref(false);
const total = ref(0);
const logList = ref<ApplicationLogDto[]>([]);
const detailDialogVisible = ref(false);
const currentLog = ref<ApplicationLogDto>();
const activeTab = ref("message");
const showRawJson = ref(false);

const queryParams = reactive<ApplicationLogQueryParams>({
  $pageIndex: 1,
  $pageSize: 10,
  application: "",
  level: "",
  category: "",
  clientIp: "",
  controller: "",
  action: "",
  currentUser: "",
  correlationId: "",
  message: "",
});

onMounted(() => {
  getList();
});

const getList = async () => {
  try {
    loading.value = true;
    const response = await getApplicationLogPage(queryParams);
    console.log("日志列表数据:", response);

    const { data } = response;
    logList.value = data.rows || [];
    total.value = data.totals;
  } catch (error) {
    console.error("获取日志列表失败", error);
    ElMessage.error("获取日志列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

const handleViewDetail = (row: ApplicationLogDto) => {
  currentLog.value = row;
  activeTab.value = "message"; // 默认显示日志内容tab
  showRawJson.value = false; // 重置JSON显示状态
  detailDialogVisible.value = true;
};

// 判断内容是否为JSON格式
const isJsonContent = (content?: string): boolean => {
  if (!content || typeof content !== 'string') return false;

  const trimmed = content.trim();
  if (!trimmed) return false;

  // 简单判断是否可能是JSON（以{或[开头）
  if (!trimmed.startsWith('{') && !trimmed.startsWith('[')) return false;

  try {
    JSON.parse(trimmed);
    return true;
  } catch {
    return false;
  }
};

// 格式化JSON内容并添加语法高亮
const formatJsonContent = (content?: string): string => {
  if (!content) return '';

  try {
    const parsed = JSON.parse(content);
    const formatted = JSON.stringify(parsed, null, 2);

    // 添加简单的语法高亮
    return formatted
      .replace(/(".*?")(\s*:)/g, '<span class="json-key">$1</span>$2')
      .replace(/:\s*(".*?")/g, ': <span class="json-string">$1</span>')
      .replace(/:\s*(\d+\.?\d*)/g, ': <span class="json-number">$1</span>')
      .replace(/:\s*(true|false)/g, ': <span class="json-boolean">$1</span>')
      .replace(/:\s*(null)/g, ': <span class="json-null">$1</span>');
  } catch {
    return content;
  }
};

// 切换JSON显示模式
const toggleJsonView = () => {
  showRawJson.value = !showRawJson.value;
};

// 获取日志级别对应的标签类型
const getLevelTagType = (level?: string) => {
  switch (level) {
    case "Trace":
    case "Debug":
      return "";
    case "Information":
      return "success";
    case "Warning":
      return "warning";
    case "Error":
    case "Critical":
      return "danger";
    default:
      return "";
  }
};

// 格式化日期时间
const formatDateTime = (dateStr?: string) => {
  if (!dateStr) return "-";
  try {
    const date = new Date(dateStr);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  } catch {
    return dateStr;
  }
};

// 截断文本
const truncateText = (text?: string, maxLength: number = 50) => {
  if (!text) return "-";
  return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
};
</script>

<style lang="scss" scoped>


.message-cell {
  word-break: break-all;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.log-detail {
  .log-details-container {
    margin-top: 20px;

    .log-tabs {
      :deep(.el-tabs__content) {
        max-height: 45vh;
        overflow-y: auto;
        padding: 0;

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }

      :deep(.el-tab-pane) {
        padding: 0;
      }

      .log-content,
      .exception-content {
        font-family: 'Courier New', monospace;
        font-size: 13px;
        line-height: 1.5;
        width: 100%;

        :deep(.el-textarea__inner) {
          font-family: 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.5;
          background-color: #f8f9fa;
          border: 1px solid #e9ecef;
          resize: none;
        }
      }

      .json-viewer {
        .json-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          padding: 8px 12px;
          background-color: #f5f7fa;
          border: 1px solid #e4e7ed;
          border-radius: 4px;

          .json-label {
            font-size: 12px;
            color: #909399;
            font-weight: 500;
          }
        }

        .json-display {
          border: 1px solid #e9ecef;
          border-radius: 4px;
          background-color: #f8f9fa;
          max-height: 400px;
          overflow: auto;

          .json-formatted {
            margin: 0;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #2c3e50;
            white-space: pre-wrap;
            word-break: break-all;

            // JSON语法高亮样式
            .json-key {
              color: #e74c3c;
              font-weight: bold;
            }

            .json-string {
              color: #27ae60;
            }

            .json-number {
              color: #3498db;
            }

            .json-boolean {
              color: #9b59b6;
              font-weight: bold;
            }

            .json-null {
              color: #95a5a6;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
}

.log-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }
}
</style>
