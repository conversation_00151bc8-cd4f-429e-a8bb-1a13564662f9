import { appInfoList, getAppInfo } from '@/config/app-info';

export const API_URLS: Record<string, string> = {
    "rbac-mgt": import.meta.env.VITE_API_RBAC_MGT,
    "rbac": import.meta.env.VITE_API_RBAC,
    "files": import.meta.env.VITE_API_FILES,
    "files-mgt": import.meta.env.VITE_API_FILES_MGT,
    "dynamic-form": import.meta.env.VITE_API_DYNAMIC_FORM,
    "dynamic-form-mgt": import.meta.env.VITE_API_DYNAMIC_FORM_MGT,
    "messaging": import.meta.env.VITE_API_MESSAGING,
    "messaging-mgt": import.meta.env.VITE_API_MESSAGING_MGT,
    "itmctr": import.meta.env.VITE_API_ITMCTR,
    "itmctr-mgt": import.meta.env.VITE_API_ITMCTR_MGT,
    "logging-mgt": import.meta.env.VITE_API_LOGGING_MGT,
};

export function getApiBaseUrl(appCode: string): string {
    const isDev = import.meta.env.MODE === 'development';
    if (isDev) {
        // 使用getAppInfo获取proxyApiUrl
        const app = getAppInfo(appCode);
        return app?.proxyApiUrl;
    }
    return API_URLS[appCode];
}
