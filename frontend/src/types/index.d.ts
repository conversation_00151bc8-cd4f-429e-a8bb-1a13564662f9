declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '*.svg'
declare module '*.png'
declare module '*.jpg'
declare module '*.jpeg'
declare module '*.gif'
declare module '*.bmp'
declare module '*.tiff'

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_BASE_URL: string
  readonly VITE_APP_ENV: string
  readonly VITE_API_RBAC_MGT: string
  readonly VITE_API_RBAC: string
  readonly VITE_API_FILES: string
  readonly VITE_API_FILES_MGT: string
  readonly VITE_API_DYNAMIC_FORM: string
  readonly VITE_API_DYNAMIC_FORM_MGT: string
  readonly VITE_API_ITMCTR: string
  readonly VITE_API_ITMCTR_MGT: string
  readonly VITE_API_LOGGING_MGT: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
} 