import {createRouter, createWebHistory, RouteRecordRaw} from 'vue-router'
import {useUserStore} from '@/stores/user'
import {usePermissionStore} from '@/stores/permission'
import {ElMessage} from 'element-plus'
import {RouteConfig} from './route-config'

const base = import.meta.env.BASE_URL

/**
 * 创建路由实例
 */
const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: RouteConfig.constantRoutes,
    scrollBehavior: () => ({top: 0})
})

/**
 * 重置路由
 * 用于在用户退出登录时重置路由
 */
export function resetRouter() {
    // 获取所有已添加的路由
    const permissionStore = usePermissionStore()

    // 移除所有动态添加的路由
    permissionStore.addRoutes.forEach((route: RouteRecordRaw) => {
        if (route.name) {
            router.removeRoute(route.name)
        }
    })

    // 重置权限store
    permissionStore.$reset()
}

// 导出路由配置，方便其他模块使用
export const {publicRoutes, baseRoutes, permissionRoutes, constantRoutes, fallbackRoute} = RouteConfig

// 递归扁平化所有路由
function flattenRoutes(routes: RouteRecordRaw[]): RouteRecordRaw[] {
    const result: RouteRecordRaw[] = []
    for (const route of routes) {
        result.push(route)
        if (route.children && route.children.length > 0) {
            result.push(...flattenRoutes(route.children))
        }
    }
    return result
}

const flatPublicRoutes = flattenRoutes(publicRoutes)
const flatBaseRoutes = flattenRoutes(baseRoutes)
const flatPermissionRoutes = flattenRoutes(permissionRoutes)
const flatAllRoutes = [...flatPublicRoutes, ...flatBaseRoutes, ...flatPermissionRoutes]

// 构建name到路由对象的映射，便于查找父级
const nameRouteMap = new Map(flatPermissionRoutes.filter(r => r.name).map(r => [r.name, r]))

// 递归查找目标name的路由链（从permissionRoutes树结构中）
function findRouteChainByName(routes: RouteRecordRaw[], targetName: string): RouteRecordRaw[] | null {
    for (const route of routes) {
        if (route.name === targetName) {
            return [route]
        }
        if (route.children) {
            const childChain = findRouteChainByName(route.children, targetName)
            if (childChain) {
                return [route, ...childChain]
            }
        }
    }
    return null
}

// 获取所有子路由的name（含自身）
function getAllChildNames(route: RouteRecordRaw): string[] {
    let names: string[] = []
    if (route.name) names.push(route.name as string)
    if (route.children) {
        for (const child of route.children) {
            names = names.concat(getAllChildNames(child))
        }
    }
    return names
}

// 通过path查找所有路由的name
function getAllNamesByPath(routes: RouteRecordRaw[], targetPath: string): string[] {
    let names: string[] = []
    for (const route of routes) {
        if ((route.redirect === targetPath || route.path === targetPath) && route.name) {
            names.push(route.name as string)
        }
        if (route.children) {
            names = names.concat(getAllNamesByPath(route.children, targetPath))
        }
    }
    return names
}

/**
 * 路由守卫
 * 处理路由跳转前的权限验证
 */
// 用于标记是否已批量注册动态路由
let hasRegisteredPermissionRoutes = false

router.beforeEach(async (to, from, next) => {
    const appTitle = import.meta.env.VITE_APP_TITLE || 'XJ Framework Admin';
    document.title = to.meta.title ? `${to.meta.title} - ${appTitle}` : appTitle;

    const userStore = useUserStore()
    const permissionStore = usePermissionStore()

    // ===== 新增：批量动态注册有权限的路由 =====
    if (userStore.token) {
        // 拉取权限菜单
        const permissionsObj = await userStore.fetchPermissions()
        const permissionsArr = Array.isArray(permissionsObj)
            ? permissionsObj
            : (permissionsObj.menus || [])
        // 记录已注册，避免重复注册
        const registeredNames = new Set(flatPermissionRoutes.filter(r => router.hasRoute(r.name as string)).map(r => r.name as string))
        let hasNewRoute = false
        // 批量注册所有有权限的路由及其父级
        for (const permName of permissionsArr) {
            if (!permName) continue
            if (router.hasRoute(permName)) continue
            // 查找路由链（父级+自身）
            const chain = findRouteChainByName(permissionRoutes, permName)
            if (chain && chain.length > 0) {
                for (let i = 0; i < chain.length; i++) {
                    const r = chain[i]
                    if (!r.name) continue
                    if (!router.hasRoute(r.name as string)) {
                        if (i === 0) {
                            router.addRoute(r)
                        } else {
                            router.addRoute(chain[i - 1].name as string, r)
                        }
                        hasNewRoute = true
                    }
                }
            }
        }
        // 只在首次注册动态路由时，强制重新进入当前路由，避免刷新空白
        if (!hasRegisteredPermissionRoutes && hasNewRoute) {
            hasRegisteredPermissionRoutes = true
            return next({ ...to, replace: true })
        }
        hasRegisteredPermissionRoutes = true
    }
    // ===== 批量动态注册结束 =====

    // 获取所有路由集合（递归展开）
    const allRouteNames = flatAllRoutes.map(r => r.name).filter(Boolean)
    const allRoutePaths = flatAllRoutes.map(r => r.path).filter(Boolean)

    // 1. 路由是否存在
    const toName = to.name ? String(to.name) : ''
    const toPath = to.path ? String(to.path) : ''
    if (!toName && !toPath) {
        return next('/404')
    }
    if (!allRouteNames.includes(toName) && !allRoutePaths.includes(toPath)) {
        return next('/404')
    }

    // 2. publicRoutes
    if (flatPublicRoutes.some(r => r.path === toPath || r.name === toName)) {
        // 已登录访问 /login，跳转到 /dashboard
        if (toPath === '/login' && userStore.token) {
            return next({path: '/dashboard'})
        }
        return next()
    }

    // 3. baseRoutes
    if (flatBaseRoutes.some(r => r.path === toPath || r.name === toName)) {
        if (!userStore.token) {
            return next({name: 'login', query: {redirect: to.fullPath}})
        }
        // 已登录
        if (!userStore.name) {
            await userStore.fetchUserInfo()
        }
        return next()
    }

    // 4. permissionRoutes
    if (flatPermissionRoutes.some(r => r.path === toPath || r.name === toName)) {
        if (!userStore.token) {
            return next({name: 'login', query: {redirect: to.fullPath}})
        }
        if (!userStore.name) {
            await userStore.fetchUserInfo()
        }
        // 拉取权限
        const permissionsObj = await userStore.fetchPermissions()
        const permissionsArr = Array.isArray(permissionsObj)
            ? permissionsObj
            : (permissionsObj.menus || [])
        let checkName = toName
        let found;
        if (!checkName) {
            // 如果toName不存在，用toPath查找对应的name
            found = flatPermissionRoutes.find(r => r.path === toPath)
            checkName = found && found.name ? String(found.name) : ''
        } else {
            found = flatPermissionRoutes.find(r => r.name === toName)
        }
        // 动态添加当前有权限的路由及其父级
        let hasPermission = false
        if (checkName && permissionsArr.includes(checkName)) {
            hasPermission = true
        } else if (found) {
            // 检查所有子路由name
            const allChildNames = getAllChildNames(found)
            hasPermission = allChildNames.some(name => permissionsArr.includes(name))
            // 兜底：用path查找所有name再比对
            if (!hasPermission && found.path) {
                const allNamesByPath = getAllNamesByPath(permissionRoutes, found.path)
                hasPermission = allNamesByPath.some(name => permissionsArr.includes(name))
            }
        }
        if (hasPermission) {
            return next()
        } else {
            return next('/403')
        }
    }

    // 5. 兜底
    return next('/404')
})

export default router