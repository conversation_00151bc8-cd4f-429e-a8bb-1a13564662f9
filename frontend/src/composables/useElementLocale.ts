import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import en from 'element-plus/dist/locale/en.mjs'

/**
 * Element Plus 国际化组合式函数
 * 根据当前语言返回对应的 Element Plus locale
 * 支持通过参数动态设置控件语言
 */
export function useElementLocale(customLocale?: string) {
  const { locale } = useI18n()

  // 如果传入了自定义语言，使用自定义语言，否则使用全局语言
  const currentLocale = customLocale ? ref(customLocale) : locale

  const elementLocale = computed(() => {
    const lang = currentLocale.value
    switch (lang) {
      case 'zh-CN':
        return zhCn
      case 'en-US':
      case 'en':
        return en
      default:
        return zhCn // 默认使用中文
    }
  })

  // 设置自定义语言的方法
  const setCustomLocale = (lang: string) => {
    if (customLocale && currentLocale.value !== undefined) {
      currentLocale.value = lang
    }
  }

  return {
    elementLocale,
    currentLocale: currentLocale.value,
    setCustomLocale
  }
}
