import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import en from 'element-plus/dist/locale/en.mjs'

/**
 * Element Plus 国际化组合式函数
 * 根据当前语言返回对应的 Element Plus locale
 */
export function useElementLocale() {
  const { locale } = useI18n()
  
  const elementLocale = computed(() => {
    switch (locale.value) {
      case 'zh-CN':
        return zhCn
      case 'en':
      case 'en-US':
        return en
      default:
        return zhCn // 默认使用中文
    }
  })
  
  return {
    elementLocale
  }
}
