import { ref, computed, onMounted, onUnmounted } from 'vue'

/**
 * 浮动操作按钮组合式函数
 * 用于统一管理页面中的浮动操作按钮样式和行为
 */
export function useFloatingActions(options?: {
  /** 是否有footer，默认为true */
  hasFooter?: boolean
  /** 自定义底部距离，会覆盖hasFooter的设置 */
  customBottom?: string
}) {
  const { hasFooter = true, customBottom } = options || {}
  
  // 浮动按钮容器的引用
  const floatingActionsRef = ref<HTMLElement>()
  
  // 计算浮动按钮的CSS类
  const floatingActionsClass = computed(() => {
    const classes = ['floating-actions']
    if (!hasFooter) {
      classes.push('no-footer')
    }
    return classes.join(' ')
  })
  
  // 计算浮动按钮的样式
  const floatingActionsStyle = computed(() => {
    if (customBottom) {
      return { bottom: customBottom }
    }
    return {}
  })
  
  // 检查footer是否存在的函数
  const checkFooterExists = () => {
    return document.querySelector('.footer') !== null
  }
  
  // 动态调整浮动按钮位置
  const adjustPosition = () => {
    if (!floatingActionsRef.value) return
    
    const footer = document.querySelector('.footer') as HTMLElement
    if (footer) {
      const footerHeight = footer.offsetHeight
      floatingActionsRef.value.style.bottom = `${footerHeight}px`
    } else {
      floatingActionsRef.value.style.bottom = '0px'
    }
  }
  
  // 监听页面变化
  let resizeObserver: ResizeObserver | null = null
  
  onMounted(() => {
    // 如果没有自定义底部距离，则动态调整位置
    if (!customBottom) {
      adjustPosition()
      
      // 监听窗口大小变化
      window.addEventListener('resize', adjustPosition)
      
      // 监听footer元素变化
      const footer = document.querySelector('.footer')
      if (footer && ResizeObserver) {
        resizeObserver = new ResizeObserver(adjustPosition)
        resizeObserver.observe(footer)
      }
    }
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', adjustPosition)
    if (resizeObserver) {
      resizeObserver.disconnect()
    }
  })
  
  return {
    floatingActionsRef,
    floatingActionsClass,
    floatingActionsStyle,
    checkFooterExists,
    adjustPosition
  }
}

/**
 * 为页面内容添加底部内边距，避免被浮动按钮遮盖
 * @param hasFloatingActions 是否有浮动操作按钮
 * @param hasFooter 是否有footer
 * @param customPadding 自定义内边距
 */
export function usePagePadding(options?: {
  hasFloatingActions?: boolean
  hasFooter?: boolean
  customPadding?: string
}) {
  const { hasFloatingActions = true, hasFooter = true, customPadding } = options || {}
  
  const pageContentStyle = computed(() => {
    if (!hasFloatingActions) return {}
    
    if (customPadding) {
      return { paddingBottom: customPadding }
    }
    
    // 默认浮动按钮高度约为60px，footer高度为60px
    const floatingActionsHeight = 60
    const footerHeight = hasFooter ? 60 : 0
    const totalPadding = floatingActionsHeight + footerHeight + 20 // 额外20px间距
    
    return { paddingBottom: `${totalPadding}px` }
  })
  
  return {
    pageContentStyle
  }
}
