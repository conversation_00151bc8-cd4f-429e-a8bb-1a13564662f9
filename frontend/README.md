# Vue 3 + TypeScript + Element Plus 后台管理系统

## 项目介绍

这是一个基于 Vue 3、TypeScript、Element Plus 和 Vite 构建的后台管理系统模板。项目采用了最新的前端技术栈，内置了动态路由、权限验证、国际化等业务功能。

## 主要特性

- 使用 Vue 3 + TypeScript 开发，提供更好的类型检查和IDE支持
- 基于 Vite 构建，提供极速的开发体验
- 使用 Element Plus 组件库，提供美观的UI界面
- 支持动态路由和权限控制
- 集成国际化 i18n 支持
- JWT Token 认证
- 响应式布局，支持多端适配

## 开发环境

- Node.js >= 14.0.0
- npm >= 6.0.0

## 项目结构

```
├── src/
│   ├── api/                # API 接口
│   ├── assets/            # 静态资源
│   ├── components/        # 公共组件
│   ├── directives/        # 自定义指令
│   ├── i18n/              # 国际化
│   ├── layout/            # 布局组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── public/               # 静态资源
├── types/               # 类型声明
├── .env                 # 环境变量
├── .eslintrc.js        # ESLint 配置
├── tsconfig.json       # TypeScript 配置
├── vite.config.ts      # Vite 配置
└── package.json        # 项目依赖
```

## 开始使用

1. 安装依赖

```bash
npm install
```

2. 启动开发服务器

```bash
npm run dev
```

3. 构建生产版本

```bash
npm run build
```

## 代码提交规范

- feat: 新功能
- fix: 修复问题
- docs: 修改文档
- style: 修改代码格式，不影响代码逻辑
- refactor: 重构代码，理论上不影响现有功能
- perf: 提升性能
- test: 增加修改测试用例
- chore: 修改工具相关（包括但不限于文档、代码生成等）
- deps: 升级依赖

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 13
- Edge >= 88 