import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { appInfoList } from './src/config/app-info'

function getProxyConfig(env: Record<string, string>) {
  const proxyConfig: Record<string, any> = {}
  appInfoList.forEach(app => {
    const envKey = `VITE_API_${app.appCode.replace(/-/g, '_').toUpperCase()}`
    const target = env[envKey]
    if (target) {
      proxyConfig[`${app.proxyApiUrl}/`] = {
        target,
        changeOrigin: true,
        rewrite: (path: string) => path.replace(new RegExp(`^${app.proxyApiUrl}`), ''),
      }
    }
  })
  return proxyConfig
}
const envConfig = {
  'production': { base: '/mgt/' },
  'sit': { base: '/mgt/' },
  'development': { base: '/' },
  'DockerDev': { base: '/mgt/' }
}
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())

  return {
    base: envConfig[mode].base,
    plugins: [
      vue(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
        imports: ['vue', 'vue-router', 'pinia', '@vueuse/core'],
        dts: 'src/auto-imports.d.ts',
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        dts: 'src/components.d.ts',
      }),
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core',
        'element-plus',
        '@element-plus/icons-vue'
      ]
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`,
        },
      },
    },
    server: {
      host: '0.0.0.0',
      port: parseInt(env.VITE_PORT || '3000'),
      proxy: getProxyConfig(env),
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: mode !== 'production',
      rollupOptions: {
        output: {
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: '[ext]/[name]-[hash].[ext]',
        },
      },
    },
  }
})